#!/bin/bash

# Mthunzi Deployment Script
# This script deploys the Mthunzi application to production

set -e  # Exit on any error

echo "🚀 Starting Mthunzi deployment..."

# Configuration
ENVIRONMENT=${1:-production}
BACKUP_DB=${2:-true}

echo "Environment: $ENVIRONMENT"
echo "Backup database: $BACKUP_DB"

# Load environment variables
if [ -f ".env.${ENVIRONMENT}" ]; then
    export $(cat .env.${ENVIRONMENT} | xargs)
    echo "✅ Loaded environment variables from .env.${ENVIRONMENT}"
else
    echo "❌ Environment file .env.${ENVIRONMENT} not found!"
    exit 1
fi

# Backup database if requested
if [ "$BACKUP_DB" = "true" ]; then
    echo "📦 Creating database backup..."
    BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"
    docker-compose exec -T db pg_dump -U mthunzi mthunzi > "backups/$BACKUP_FILE"
    echo "✅ Database backup created: backups/$BACKUP_FILE"
fi

# Pull latest code
echo "📥 Pulling latest code..."
git pull origin main

# Build and start services
echo "🔨 Building Docker images..."
docker-compose build --no-cache

echo "🔄 Stopping existing services..."
docker-compose down

echo "🚀 Starting services..."
docker-compose up -d

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 10

# Run migrations
echo "🗄️ Running database migrations..."
docker-compose exec web python manage.py migrate

# Collect static files
echo "📁 Collecting static files..."
docker-compose exec web python manage.py collectstatic --noinput

# Create superuser if it doesn't exist
echo "👤 Ensuring superuser exists..."
docker-compose exec web python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', '${ADMIN_PASSWORD}', phone_number='+27000000000', role='forge')
    print('Superuser created')
else:
    print('Superuser already exists')
"

# Health check
echo "🏥 Performing health check..."
sleep 5
if curl -f http://localhost/health/ > /dev/null 2>&1; then
    echo "✅ Health check passed!"
else
    echo "❌ Health check failed!"
    echo "📋 Checking service status..."
    docker-compose ps
    echo "📋 Checking logs..."
    docker-compose logs web
    exit 1
fi

# Cleanup old Docker images
echo "🧹 Cleaning up old Docker images..."
docker image prune -f

echo "🎉 Deployment completed successfully!"
echo "🌐 Application is available at: https://$(hostname)"
echo "🔧 Admin interface: https://$(hostname)/admin/"

# Send deployment notification (if webhook URL is configured)
if [ ! -z "$DEPLOYMENT_WEBHOOK_URL" ]; then
    curl -X POST "$DEPLOYMENT_WEBHOOK_URL" \
        -H "Content-Type: application/json" \
        -d "{\"text\":\"🚀 Mthunzi deployed successfully to $ENVIRONMENT\"}" \
        > /dev/null 2>&1 || true
fi

echo "✅ Deployment script completed!"
