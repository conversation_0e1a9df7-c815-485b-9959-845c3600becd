"""
Serializers for the payments app.
"""

from rest_framework import serializers
from django.utils import timezone
from .models import PaymentMethod, Payment, PaymentProof


class PaymentMethodSerializer(serializers.ModelSerializer):
    """
    Serializer for payment methods.
    """
    method_type_display = serializers.CharField(source='get_method_type_display', read_only=True)
    business_name = serializers.CharField(source='business.name', read_only=True)
    
    class Meta:
        model = PaymentMethod
        fields = (
            'id', 'business', 'business_name', 'method_type',
            'method_type_display', 'name', 'account_name',
            'account_number', 'bank_name', 'mobile_number',
            'instructions', 'is_active', 'created_at', 'updated_at'
        )
        read_only_fields = ('id', 'business', 'created_at', 'updated_at')


class PaymentMethodCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating payment methods.
    """
    
    class Meta:
        model = PaymentMethod
        fields = (
            'method_type', 'name', 'account_name', 'account_number',
            'bank_name', 'mobile_number', 'instructions', 'is_active'
        )
    
    def create(self, validated_data):
        # Set business from request context
        business = self.context['request'].user.business
        validated_data['business'] = business
        return super().create(validated_data)


class PaymentProofSerializer(serializers.ModelSerializer):
    """
    Serializer for payment proofs.
    """
    proof_type_display = serializers.CharField(source='get_proof_type_display', read_only=True)
    file_url = serializers.SerializerMethodField()
    
    class Meta:
        model = PaymentProof
        fields = (
            'id', 'proof_type', 'proof_type_display', 'file',
            'file_url', 'description', 'uploaded_at'
        )
        read_only_fields = ('id', 'uploaded_at')
    
    def get_file_url(self, obj):
        if obj.file:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.file.url)
            return obj.file.url
        return None


class PaymentSerializer(serializers.ModelSerializer):
    """
    Serializer for payments.
    """
    proofs = PaymentProofSerializer(many=True, read_only=True)
    order_number = serializers.CharField(source='order.order_number', read_only=True)
    customer_name = serializers.CharField(source='order.customer.get_full_name', read_only=True)
    customer_phone = serializers.CharField(source='order.customer.phone_number', read_only=True)
    business_name = serializers.CharField(source='order.business.name', read_only=True)
    payment_method_name = serializers.CharField(source='payment_method.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    verified_by_name = serializers.CharField(source='verified_by.get_full_name', read_only=True)
    
    class Meta:
        model = Payment
        fields = (
            'id', 'payment_reference', 'order', 'order_number',
            'customer_name', 'customer_phone', 'business_name',
            'payment_method', 'payment_method_name', 'amount',
            'status', 'status_display', 'customer_reference',
            'customer_notes', 'verified_by', 'verified_by_name',
            'verification_notes', 'proofs', 'created_at',
            'updated_at', 'submitted_at', 'verified_at'
        )
        read_only_fields = (
            'id', 'payment_reference', 'order', 'amount',
            'verified_by', 'created_at', 'updated_at',
            'submitted_at', 'verified_at'
        )


class PaymentCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating payments.
    """
    
    class Meta:
        model = Payment
        fields = ('payment_method', 'customer_reference', 'customer_notes')
    
    def create(self, validated_data):
        # Get order from context
        order = self.context['order']
        validated_data['order'] = order
        validated_data['amount'] = order.total_amount
        return super().create(validated_data)


class PaymentVerificationSerializer(serializers.ModelSerializer):
    """
    Serializer for payment verification by business owners.
    """
    
    class Meta:
        model = Payment
        fields = ('status', 'verification_notes')
    
    def validate_status(self, value):
        current_status = self.instance.status
        
        # Only allow verification transitions
        if current_status == 'submitted':
            if value not in ['verified', 'rejected']:
                raise serializers.ValidationError(
                    "Payment can only be verified or rejected from submitted status"
                )
        else:
            raise serializers.ValidationError(
                "Payment can only be verified when status is 'submitted'"
            )
        
        return value
    
    def update(self, instance, validated_data):
        # Set verified_by to current user
        instance.verified_by = self.context['request'].user
        
        # Set verification timestamp
        if validated_data.get('status') in ['verified', 'rejected']:
            instance.verified_at = timezone.now()
        
        return super().update(instance, validated_data)


class PaymentProofUploadSerializer(serializers.ModelSerializer):
    """
    Serializer for uploading payment proofs.
    """
    
    class Meta:
        model = PaymentProof
        fields = ('proof_type', 'file', 'description')
    
    def validate_file(self, value):
        # Check file size (max 10MB)
        if value.size > 10 * 1024 * 1024:
            raise serializers.ValidationError("File size cannot exceed 10MB")
        
        # Check file type
        allowed_types = [
            'image/jpeg', 'image/png', 'image/gif',
            'application/pdf', 'image/webp'
        ]
        
        if hasattr(value, 'content_type'):
            if value.content_type not in allowed_types:
                raise serializers.ValidationError(
                    "File type not supported. Please upload JPEG, PNG, GIF, WebP, or PDF files."
                )
        
        return value
    
    def create(self, validated_data):
        # Get payment from context
        payment = self.context['payment']
        validated_data['payment'] = payment
        return super().create(validated_data)


class PaymentSummarySerializer(serializers.ModelSerializer):
    """
    Simplified serializer for payment summaries.
    """
    order_number = serializers.CharField(source='order.order_number', read_only=True)
    customer_name = serializers.CharField(source='order.customer.get_full_name', read_only=True)
    business_name = serializers.CharField(source='order.business.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    proof_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Payment
        fields = (
            'id', 'payment_reference', 'order_number', 'customer_name',
            'business_name', 'amount', 'status', 'status_display',
            'proof_count', 'created_at', 'submitted_at', 'verified_at'
        )
    
    def get_proof_count(self, obj):
        return obj.proofs.count()


class PaymentStatusUpdateSerializer(serializers.Serializer):
    """
    Serializer for updating payment status.
    """
    status = serializers.ChoiceField(choices=Payment.PaymentStatus.choices)
    notes = serializers.CharField(required=False, allow_blank=True)
    
    def validate_status(self, value):
        payment = self.context['payment']
        current_status = payment.status
        
        # Define allowed status transitions
        allowed_transitions = {
            'pending': ['submitted', 'cancelled'],
            'submitted': ['verified', 'rejected'],
            'verified': [],  # Final state
            'rejected': ['submitted'],  # Can resubmit
            'refunded': []   # Final state
        }
        
        if value not in allowed_transitions.get(current_status, []):
            raise serializers.ValidationError(
                f"Cannot change status from {current_status} to {value}"
            )
        
        return value
