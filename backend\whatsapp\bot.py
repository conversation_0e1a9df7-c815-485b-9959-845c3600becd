"""
WhatsApp bot (Luma) for handling customer interactions.
"""

import re
import logging
from typing import Dict, Any, Optional
from django.contrib.auth import get_user_model
from accounts.models import Business
from orders.models import Product, Order
from .services import whatsapp_service
from .models import WhatsAppMessage, ChatSession

User = get_user_model()
logger = logging.getLogger(__name__)


class LumaBot:
    """
    WhatsApp chatbot for handling customer interactions.
    """
    
    def __init__(self):
        self.commands = {
            'help': self.handle_help,
            'menu': self.handle_menu,
            'order': self.handle_order,
            'status': self.handle_order_status,
            'cancel': self.handle_cancel_order,
            'contact': self.handle_contact,
            'track': self.handle_track_delivery,
            'payment': self.handle_payment_info,
            'feedback': self.handle_feedback,
            'hours': self.handle_business_hours,
            'location': self.handle_location,

            # Service business commands
            'services': self.handle_services,
            'book': self.handle_book_appointment,
            'appointments': self.handle_view_appointments,
            'reschedule': self.handle_reschedule_appointment,

            # Food business commands
            'specials': self.handle_daily_specials,
            'today': self.handle_todays_menu,
            'products': self.handle_menu,  # Alias for menu
        }

        # Conversation states for multi-step interactions
        self.conversation_states = {}

        # Common responses
        self.responses = {
            'greeting': [
                "Hello! 👋 Welcome to {business_name}!",
                "Hi there! 😊 How can I help you today?",
                "Welcome! I'm Luma, your WhatsApp assistant. What can I do for you?"
            ],
            'unknown': [
                "I'm not sure I understand. Type 'help' to see what I can do!",
                "Sorry, I didn't get that. Try typing 'menu' to see our products or 'help' for commands.",
                "I'm still learning! Type 'help' to see available commands."
            ],
            'error': "Oops! Something went wrong. Please try again or contact support."
        }
    
    def process_message(self, whatsapp_message: WhatsAppMessage) -> bool:
        """
        Process incoming WhatsApp message and generate appropriate response.
        
        Args:
            whatsapp_message: WhatsApp message to process
        
        Returns:
            True if message was processed successfully, False otherwise
        """
        try:
            # Get or create user
            user = self._get_or_create_user(whatsapp_message.sender_phone)
            if not user:
                return False
            
            # Update message with user
            whatsapp_message.sender = user
            whatsapp_message.save()
            
            # Determine business context
            business = self._determine_business_context(whatsapp_message)
            if business:
                whatsapp_message.business = business
                whatsapp_message.save()
            
            # Process the message content
            response = self._generate_response(whatsapp_message, user, business)
            
            if response:
                # Send response
                whatsapp_service.send_text_message(
                    whatsapp_message.sender_phone,
                    response,
                    business
                )
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            return False
    
    def _get_or_create_user(self, phone_number: str) -> Optional[User]:
        """Get or create user from phone number."""
        try:
            # Try to find existing user
            user = User.objects.filter(phone_number=phone_number).first()
            
            if not user:
                # Create new user
                username = f"user_{phone_number.replace('+', '').replace(' ', '')}"
                user = User.objects.create_user(
                    username=username,
                    phone_number=phone_number,
                    role='pulse'  # Customer role
                )
                logger.info(f"Created new user for phone: {phone_number}")
            
            return user
            
        except Exception as e:
            logger.error(f"Error getting/creating user: {str(e)}")
            return None
    
    def _determine_business_context(self, message: WhatsAppMessage) -> Optional[Business]:
        """Determine which business the message is for."""
        # For now, we'll use the recipient number to determine business
        # In a real implementation, you might have different WhatsApp numbers for different businesses
        try:
            business = Business.objects.filter(
                whatsapp_business_number=message.recipient_phone
            ).first()
            
            if not business:
                # Default to first active business
                business = Business.objects.filter(status='active').first()
            
            return business
            
        except Exception as e:
            logger.error(f"Error determining business context: {str(e)}")
            return None
    
    def _generate_response(self, message: WhatsAppMessage, user: User, business: Optional[Business]) -> str:
        """Generate response based on message content."""
        content = message.content.lower().strip()
        user_phone = message.sender_phone

        # Check if user is in a conversation state
        conversation_state = self.get_conversation_state(user_phone)
        if conversation_state.get('step'):
            return self.handle_multi_step_order(message, user, business)

        # Check for commands
        for command, handler in self.commands.items():
            if content.startswith(command):
                return handler(message, user, business)

        # Check for greetings
        greetings = ['hi', 'hello', 'hey', 'good morning', 'good afternoon', 'good evening', 'hola', 'sawubona']
        if any(greeting in content for greeting in greetings):
            return self.handle_greeting(message, user, business)

        # Check for order-related keywords
        order_keywords = ['buy', 'purchase', 'want', 'need', 'price', 'cost', 'how much']
        if any(keyword in content for keyword in order_keywords):
            return self.handle_menu(message, user, business)

        # Check for payment-related keywords
        payment_keywords = ['pay', 'payment', 'bank', 'transfer', 'money']
        if any(keyword in content for keyword in payment_keywords):
            return self.handle_payment_info(message, user, business)

        # Check for delivery/tracking keywords
        delivery_keywords = ['track', 'delivery', 'where', 'location', 'driver', 'runner']
        if any(keyword in content for keyword in delivery_keywords):
            return self.handle_track_delivery(message, user, business)

        # Check for business info keywords
        info_keywords = ['hours', 'time', 'open', 'closed', 'address', 'where are you']
        if any(keyword in content for keyword in info_keywords):
            if 'hour' in content or 'time' in content or 'open' in content:
                return self.handle_business_hours(message, user, business)
            else:
                return self.handle_location(message, user, business)

        # Check for feedback keywords
        feedback_keywords = ['feedback', 'review', 'complain', 'suggest', 'good', 'bad', 'excellent', 'terrible']
        if any(keyword in content for keyword in feedback_keywords):
            return self.handle_feedback(message, user, business)

        # Check for numbers (might be responding to a menu)
        if content.isdigit():
            # Start multi-step order process
            self.set_conversation_state(user_phone, {'step': 'select_product'})
            return self.handle_multi_step_order(message, user, business)

        # Random response for unknown input
        import random
        unknown_responses = self.responses['unknown']
        return random.choice(unknown_responses)
    
    def handle_greeting(self, message: WhatsAppMessage, user: User, business: Optional[Business]) -> str:
        """Handle greeting messages."""
        if business:
            # Customize greeting based on business type
            if business.is_service_business:
                options = """
• Type 'services' to see our services
• Type 'book' to book an appointment
• Type 'appointments' to check your appointments
• Type 'help' for more options"""
            elif business.is_food_business:
                options = """
• Type 'menu' to see today's menu
• Type 'order' to place an order
• Type 'status' to check your order status
• Type 'help' for more options"""
            else:  # Product business
                options = """
• Type 'menu' to see our products
• Type 'order' to place an order
• Type 'status' to check your order status
• Type 'help' for more options"""

            return f"""
{business.welcome_message}

Here's what I can help you with:
{options}

How can I assist you today?
            """.strip()
        else:
            return """
Hello! Welcome to Mthunzi.

I'm Luma, your WhatsApp assistant. I can help you with orders and information.

Type 'help' to see what I can do for you.
            """.strip()
    
    def handle_help(self, message, user, business):
        """Handle help command."""
        if business:
            if business.is_service_business:
                return """
🤖 Luma - Your Service Assistant

Available commands:
• 'services' - View available services
• 'book' - Book an appointment
• 'appointments' - View your appointments
• 'cancel' - Cancel an appointment
• 'reschedule' - Reschedule an appointment
• 'contact' - Contact information
• 'help' - Show this help message

Example: "book haircut tomorrow 2pm"
                """.strip()
            elif business.is_food_business:
                return """
🤖 Luma - Your Food Ordering Assistant

Available commands:
• 'menu' - View today's menu
• 'order' - Place a food order
• 'status' - Check order status
• 'specials' - Today's specials
• 'contact' - Contact information
• 'help' - Show this help message

Example: "order 2 burgers and 1 pizza"
                """.strip()
            else:  # Product business
                return """
🤖 Luma - Your Shopping Assistant

Available commands:
• 'menu' or 'products' - View products
• 'order' - Place an order
• 'status' - Check order status
• 'delivery' - Delivery information
• 'contact' - Contact information
• 'help' - Show this help message

Example: "order 2 shirts size M"
                """.strip()

        return """
🤖 Luma - Your WhatsApp Assistant

Available commands:
• *menu* - View products and prices
• *order* - Place a new order (interactive)
• *status* - Check your order status
• *track* - Track your delivery
• *payment* - View payment methods
• *cancel* - Cancel your latest order
• *contact* - Get business contact info
• *hours* - Business hours
• *location* - Business address
• *feedback* - Share your feedback
• *help* - Show this help message

You can also just chat naturally! I understand:
- Greetings (hi, hello, good morning)
- Order requests (I want to buy, how much)
- Payment questions (how to pay, bank details)
- Delivery tracking (where is my order)
- Business info (when are you open, where are you)

Just type what you need and I'll help! 😊
        """.strip()
    
    def handle_menu(self, message: WhatsAppMessage, user: User, business: Optional[Business]) -> str:
        """Handle menu command."""
        if not business:
            return "Sorry, I couldn't find the business menu. Please contact support."
        
        products = Product.objects.filter(
            business=business,
            status='active'
        )[:10]  # Limit to 10 products
        
        if not products:
            return f"{business.name} doesn't have any products available right now. Please check back later."
        
        menu_text = f"🛍️ *{business.name} - Menu*\n\n"
        
        for product in products:
            stock_status = "✅ In Stock" if product.is_in_stock else "❌ Out of Stock"
            menu_text += f"*{product.name}*\n"
            menu_text += f"Price: R{product.price}\n"
            menu_text += f"Status: {stock_status}\n"
            if product.description:
                menu_text += f"Description: {product.description}\n"
            menu_text += "\n"
        
        menu_text += "To place an order, type 'order' followed by the product name and quantity.\n"
        menu_text += "Example: order Burger 2"
        
        return menu_text
    
    def handle_order(self, message: WhatsAppMessage, user: User, business: Optional[Business]) -> str:
        """Handle order command."""
        if not business:
            return "Sorry, I couldn't process your order. Please contact support."
        
        # Parse order from message
        # Simple parsing: "order product_name quantity"
        content = message.content.strip()
        parts = content.split()
        
        if len(parts) < 3:
            return """
To place an order, please use this format:
*order [product name] [quantity]*

Example: order Burger 2

Type 'menu' to see available products.
            """.strip()
        
        product_name = ' '.join(parts[1:-1])
        try:
            quantity = int(parts[-1])
        except ValueError:
            return "Please specify a valid quantity number."
        
        # Find product
        product = Product.objects.filter(
            business=business,
            name__icontains=product_name,
            status='active'
        ).first()
        
        if not product:
            return f"Sorry, I couldn't find '{product_name}' in our menu. Type 'menu' to see available products."
        
        if not product.is_in_stock:
            return f"Sorry, {product.name} is currently out of stock."
        
        if quantity <= 0:
            return "Please specify a quantity greater than 0."
        
        # For now, just return order information
        # In a full implementation, you'd create the order here
        total = product.price * quantity
        
        return f"""
📝 *Order Summary*

Product: {product.name}
Quantity: {quantity}
Unit Price: R{product.price}
Total: R{total}

To complete your order, please provide:
1. Your delivery address
2. Your contact number

Reply with your address and we'll process your order!
        """.strip()
    
    def handle_order_status(self, message: WhatsAppMessage, user: User, business: Optional[Business]) -> str:
        """Handle order status command."""
        # Get user's recent orders
        orders = Order.objects.filter(customer=user).order_by('-created_at')[:3]
        
        if not orders:
            return "You don't have any orders yet. Type 'menu' to see our products and place an order."
        
        status_text = "📋 *Your Recent Orders*\n\n"
        
        for order in orders:
            status_emoji = {
                'pending': '⏳',
                'confirmed': '✅',
                'processing': '🔄',
                'ready_for_delivery': '📦',
                'out_for_delivery': '🚚',
                'delivered': '✅',
                'cancelled': '❌'
            }.get(order.status, '❓')
            
            status_text += f"{status_emoji} *Order {order.order_number}*\n"
            status_text += f"Status: {order.get_status_display()}\n"
            status_text += f"Total: R{order.total_amount}\n"
            status_text += f"Date: {order.created_at.strftime('%Y-%m-%d %H:%M')}\n\n"
        
        return status_text
    
    def handle_cancel_order(self, message: WhatsAppMessage, user: User, business: Optional[Business]) -> str:
        """Handle cancel order command."""
        # Get user's latest pending order
        order = Order.objects.filter(
            customer=user,
            status__in=['pending', 'confirmed']
        ).order_by('-created_at').first()
        
        if not order:
            return "You don't have any orders that can be cancelled."
        
        # For now, just return information
        # In a full implementation, you'd actually cancel the order
        return f"""
❌ *Cancel Order*

Order: {order.order_number}
Status: {order.get_status_display()}
Total: R{order.total_amount}

To cancel this order, please contact us directly or confirm by typing 'confirm cancel'.
        """.strip()
    
    def handle_contact(self, message: WhatsAppMessage, user: User, business: Optional[Business]) -> str:
        """Handle contact command."""
        if not business:
            return "For support, please contact our customer service team."
        
        contact_text = f"📞 *Contact {business.name}*\n\n"
        
        if business.business_phone:
            contact_text += f"Phone: {business.business_phone}\n"
        
        if business.business_email:
            contact_text += f"Email: {business.business_email}\n"
        
        if business.address:
            contact_text += f"Address: {business.address}\n"
        
        contact_text += "\nWe're here to help! Feel free to reach out anytime."

        return contact_text

    def handle_track_delivery(self, message, user, business):
        """Handle delivery tracking command."""
        # Get user's recent deliveries
        from deliveries.models import Delivery

        deliveries = Delivery.objects.filter(
            order__customer=user
        ).order_by('-created_at')[:3]

        if not deliveries:
            return "You don't have any deliveries to track yet."

        track_text = "🚚 *Delivery Tracking*\n\n"

        for delivery in deliveries:
            status_emoji = {
                'pending': '⏳',
                'assigned': '👤',
                'picked_up': '📦',
                'in_transit': '🚚',
                'delivered': '✅',
                'failed': '❌'
            }.get(delivery.status, '❓')

            track_text += f"{status_emoji} *{delivery.delivery_number}*\n"
            track_text += f"Order: {delivery.order.order_number}\n"
            track_text += f"Status: {delivery.get_status_display()}\n"

            if delivery.runner:
                track_text += f"Runner: {delivery.runner.get_full_name()}\n"

            if delivery.estimated_delivery_time:
                track_text += f"ETA: {delivery.estimated_delivery_time.strftime('%Y-%m-%d %H:%M')}\n"

            track_text += "\n"

        return track_text

    def handle_payment_info(self, message, user, business):
        """Handle payment information request."""
        if not business:
            return "Sorry, payment information is not available."

        # Get business payment methods
        from payments.models import PaymentMethod

        payment_methods = PaymentMethod.objects.filter(
            business=business,
            is_active=True
        )

        if not payment_methods:
            return "No payment methods are currently available. Please contact the business directly."

        payment_text = f"💳 *Payment Methods - {business.name}*\n\n"

        for method in payment_methods:
            payment_text += f"*{method.name}*\n"
            payment_text += f"Type: {method.get_method_type_display()}\n"

            if method.instructions:
                payment_text += f"Instructions: {method.instructions}\n"

            if method.method_type == 'bank_transfer':
                if method.account_name:
                    payment_text += f"Account Name: {method.account_name}\n"
                if method.account_number:
                    payment_text += f"Account Number: {method.account_number}\n"
                if method.bank_name:
                    payment_text += f"Bank: {method.bank_name}\n"
            elif method.method_type == 'mobile_money':
                if method.mobile_number:
                    payment_text += f"Mobile Number: {method.mobile_number}\n"

            payment_text += "\n"

        payment_text += "After making payment, please upload your proof of payment."

        return payment_text

    def handle_feedback(self, message, user, business):
        """Handle customer feedback."""
        # Extract feedback from message
        content = message.content.lower().strip()
        feedback_text = content.replace('feedback', '').strip()

        if not feedback_text:
            return """
📝 *Feedback*

Thank you for wanting to share feedback! Please type your feedback after the command.

Example: feedback Great service, fast delivery!

Your feedback helps us improve our service.
            """.strip()

        # Store feedback (you could create a Feedback model for this)
        # For now, just acknowledge

        # Notify business owner
        try:
            if business:
                feedback_message = f"""
📝 *New Customer Feedback*

Customer: {user.get_full_name() or user.username}
Phone: {user.phone_number}

Feedback: {feedback_text}

Received: {message.created_at.strftime('%Y-%m-%d %H:%M')}
                """.strip()

                whatsapp_service.send_text_message(
                    business.owner.phone_number,
                    feedback_message,
                    business
                )
        except Exception:
            pass  # Don't fail if notification fails

        return """
Thank you for your feedback! 🙏

Your comments are valuable to us and help improve our service.
We appreciate you taking the time to share your thoughts.

Is there anything else I can help you with today?
        """.strip()

    def handle_business_hours(self, message, user, business):
        """Handle business hours inquiry."""
        if not business:
            return "Business hours information is not available."

        # This would typically come from a business hours model
        # For now, return a generic response
        return f"""
🕒 *{business.name} - Business Hours*

Monday - Friday: 8:00 AM - 6:00 PM
Saturday: 9:00 AM - 4:00 PM
Sunday: Closed

Please note: Orders placed outside business hours will be processed the next business day.

For urgent inquiries, you can still send us a message and we'll respond as soon as possible.
        """.strip()

    def handle_location(self, message, user, business):
        """Handle location/address inquiry."""
        if not business:
            return "Location information is not available."

        location_text = f"📍 *{business.name} - Location*\n\n"

        if business.address:
            location_text += f"Address: {business.address}\n\n"

        if business.business_phone:
            location_text += f"Phone: {business.business_phone}\n"

        location_text += "We offer delivery to your location. Type 'menu' to see our products and place an order!"

        return location_text

    def get_conversation_state(self, user_phone):
        """Get conversation state for user."""
        return self.conversation_states.get(user_phone, {})

    def set_conversation_state(self, user_phone, state):
        """Set conversation state for user."""
        self.conversation_states[user_phone] = state

    def clear_conversation_state(self, user_phone):
        """Clear conversation state for user."""
        self.conversation_states.pop(user_phone, None)

    def handle_multi_step_order(self, message, user, business):
        """Handle multi-step order process."""
        user_phone = message.sender_phone
        state = self.get_conversation_state(user_phone)

        if not state.get('step'):
            # Start order process
            if not business:
                return "Sorry, I couldn't process your order. Please contact support."

            products = Product.objects.filter(
                business=business,
                status='active'
            )[:5]  # Show first 5 products

            if not products:
                return f"{business.name} doesn't have any products available right now."

            menu_text = f"🛍️ *Start Your Order - {business.name}*\n\n"
            menu_text += "Available products:\n\n"

            for i, product in enumerate(products, 1):
                menu_text += f"{i}. *{product.name}* - R{product.price}\n"
                if product.description:
                    menu_text += f"   {product.description[:50]}...\n"
                menu_text += "\n"

            menu_text += "Reply with the number of the product you want to order."

            # Set conversation state
            self.set_conversation_state(user_phone, {
                'step': 'select_product',
                'business_id': business.id,
                'products': [p.id for p in products]
            })

            return menu_text

        elif state.get('step') == 'select_product':
            # Handle product selection
            try:
                selection = int(message.content.strip())
                product_ids = state.get('products', [])

                if 1 <= selection <= len(product_ids):
                    product_id = product_ids[selection - 1]
                    product = Product.objects.get(id=product_id)

                    # Update state
                    state.update({
                        'step': 'select_quantity',
                        'product_id': product_id
                    })
                    self.set_conversation_state(user_phone, state)

                    return f"""
Selected: *{product.name}* - R{product.price}

How many would you like to order?
Please reply with a number (e.g., 1, 2, 3...)
                    """.strip()
                else:
                    return "Invalid selection. Please choose a number from the list."

            except (ValueError, Product.DoesNotExist):
                return "Invalid selection. Please choose a number from the list."

        elif state.get('step') == 'select_quantity':
            # Handle quantity selection
            try:
                quantity = int(message.content.strip())

                if quantity <= 0:
                    return "Please enter a quantity greater than 0."

                if quantity > 10:
                    return "Maximum quantity is 10. Please enter a smaller number."

                product = Product.objects.get(id=state['product_id'])
                total = product.price * quantity

                # Update state
                state.update({
                    'step': 'confirm_order',
                    'quantity': quantity,
                    'total': float(total)
                })
                self.set_conversation_state(user_phone, state)

                return f"""
📋 *Order Summary*

Product: {product.name}
Quantity: {quantity}
Unit Price: R{product.price}
Total: R{total}

Please provide your delivery address to complete the order.
                """.strip()

            except (ValueError, Product.DoesNotExist):
                return "Please enter a valid quantity number."

        elif state.get('step') == 'confirm_order':
            # Handle address and create order
            address = message.content.strip()

            if len(address) < 10:
                return "Please provide a complete delivery address."

            try:
                # Create the order
                from orders.models import Order, OrderItem

                product = Product.objects.get(id=state['product_id'])
                business_obj = Business.objects.get(id=state['business_id'])

                order = Order.objects.create(
                    customer=user,
                    business=business_obj,
                    delivery_address=address,
                    delivery_phone=user.phone_number
                )

                OrderItem.objects.create(
                    order=order,
                    product=product,
                    quantity=state['quantity']
                )

                # Clear conversation state
                self.clear_conversation_state(user_phone)

                return f"""
✅ *Order Placed Successfully!*

Order Number: {order.order_number}
Total: R{state['total']}
Delivery Address: {address}

Your order has been sent to {business_obj.name} for confirmation.
You'll receive updates on your order status.

Thank you for your order! 🙏
                """.strip()

            except Exception as e:
                self.clear_conversation_state(user_phone)
                return "Sorry, there was an error processing your order. Please try again or contact support."

        return "Something went wrong. Please start over by typing 'order'."

    # Service Business Command Handlers
    def handle_services(self, message: WhatsAppMessage, user: User, business: Optional[Business]) -> str:
        """Handle services command for service businesses."""
        if not business or not business.is_service_business:
            return "This command is only available for service businesses."

        try:
            from services.models import Service
            services = Service.objects.filter(business=business, status='active')

            if not services.exists():
                return "No services are currently available. Please contact us for more information."

            response = f"🛍️ *{business.name} Services*\n\n"
            for service in services:
                response += f"*{service.name}*\n"
                if service.description:
                    response += f"{service.description}\n"
                response += f"💰 R{service.price}\n"
                response += f"⏱️ {service.duration_minutes} minutes\n\n"

            response += "To book an appointment, type: 'book [service name] [date] [time]'\n"
            response += "Example: 'book haircut tomorrow 2pm'"

            return response

        except Exception as e:
            logger.error(f"Error handling services command: {str(e)}")
            return "Sorry, I couldn't retrieve the services list. Please try again later."

    def handle_book_appointment(self, message: WhatsAppMessage, user: User, business: Optional[Business]) -> str:
        """Handle appointment booking command."""
        if not business or not business.is_service_business:
            return "Appointment booking is only available for service businesses."

        return """
📅 *Book an Appointment*

To book an appointment, please provide:
1. Service name
2. Preferred date
3. Preferred time

Example: "book haircut tomorrow 2pm"

Or contact us directly:
📞 {business_phone}
📧 {business_email}

We'll confirm your appointment shortly!
        """.format(
            business_phone=business.business_phone or "Contact us",
            business_email=business.business_email or "Contact us"
        ).strip()

    def handle_view_appointments(self, message: WhatsAppMessage, user: User, business: Optional[Business]) -> str:
        """Handle view appointments command."""
        if not business or not business.is_service_business:
            return "Appointments are only available for service businesses."

        try:
            from services.models import Appointment
            from django.utils import timezone

            appointments = Appointment.objects.filter(
                customer=user,
                business=business,
                appointment_datetime__gte=timezone.now()
            ).order_by('appointment_datetime')

            if not appointments.exists():
                return "You don't have any upcoming appointments."

            response = "📅 *Your Upcoming Appointments*\n\n"
            for appointment in appointments:
                response += f"*{appointment.service.name}*\n"
                response += f"📅 {appointment.appointment_datetime.strftime('%B %d, %Y')}\n"
                response += f"⏰ {appointment.appointment_datetime.strftime('%I:%M %p')}\n"
                response += f"📍 Status: {appointment.get_status_display()}\n"
                response += f"💰 R{appointment.service_price}\n\n"

            response += "To cancel or reschedule, please contact us."
            return response

        except Exception as e:
            logger.error(f"Error handling appointments command: {str(e)}")
            return "Sorry, I couldn't retrieve your appointments. Please try again later."

    def handle_reschedule_appointment(self, message: WhatsAppMessage, user: User, business: Optional[Business]) -> str:
        """Handle reschedule appointment command."""
        if not business or not business.is_service_business:
            return "Appointment rescheduling is only available for service businesses."

        return """
📅 *Reschedule Appointment*

To reschedule your appointment, please contact us with:
1. Your current appointment details
2. New preferred date and time

📞 {business_phone}
📧 {business_email}

We'll help you find a new suitable time slot!
        """.format(
            business_phone=business.business_phone or "Contact us",
            business_email=business.business_email or "Contact us"
        ).strip()

    # Food Business Command Handlers
    def handle_daily_specials(self, message: WhatsAppMessage, user: User, business: Optional[Business]) -> str:
        """Handle daily specials command for food businesses."""
        if not business or not business.is_food_business:
            return "Daily specials are only available for food businesses."

        try:
            from orders.models import DailyMenu, DailyMenuItem
            from django.utils import timezone

            today = timezone.now().date()
            daily_menu = DailyMenu.objects.filter(
                business=business,
                date=today,
                is_active=True
            ).first()

            if not daily_menu:
                return "No specials available today. Check back tomorrow!"

            # Get items with special prices
            special_items = daily_menu.menu_items.filter(
                is_available=True,
                special_price__isnull=False
            )

            if not special_items.exists():
                return "No special offers today, but check out our regular menu!"

            response = f"🌟 *Today's Specials at {business.name}*\n\n"
            for item in special_items:
                response += f"*{item.product.name}*\n"
                if item.product.description:
                    response += f"{item.product.description}\n"
                response += f"💰 Special Price: R{item.special_price} "
                response += f"(was R{item.product.price})\n"
                if item.notes:
                    response += f"📝 {item.notes}\n"
                response += "\n"

            response += "Type 'order' to place your order!"
            return response

        except Exception as e:
            logger.error(f"Error handling daily specials command: {str(e)}")
            return "Sorry, I couldn't retrieve today's specials. Please try again later."

    def handle_todays_menu(self, message: WhatsAppMessage, user: User, business: Optional[Business]) -> str:
        """Handle today's menu command for food businesses."""
        if not business or not business.is_food_business:
            return self.handle_menu(message, user, business)  # Fall back to regular menu

        try:
            from orders.models import DailyMenu
            from django.utils import timezone

            today = timezone.now().date()
            daily_menu = DailyMenu.objects.filter(
                business=business,
                date=today,
                is_active=True
            ).first()

            if not daily_menu:
                return f"No menu available for today. Please contact {business.name} for more information."

            response = f"🍽️ *Today's Menu at {business.name}*\n"
            response += f"📅 {today.strftime('%B %d, %Y')}\n\n"

            if daily_menu.notes:
                response += f"📝 {daily_menu.notes}\n\n"

            available_items = daily_menu.menu_items.filter(is_available=True)

            if not available_items.exists():
                return "Sorry, no items are available today. Please check back later!"

            for item in available_items:
                response += f"*{item.product.name}*\n"
                if item.product.description:
                    response += f"{item.product.description}\n"
                response += f"💰 R{item.effective_price}\n"

                if item.available_quantity > 0:
                    response += f"📦 {item.remaining_quantity} left\n"

                if item.notes:
                    response += f"📝 {item.notes}\n"
                response += "\n"

            prep_time = daily_menu.effective_preparation_time
            response += f"⏱️ Preparation time: {prep_time} minutes\n\n"
            response += "Type 'order' to place your order!"

            return response

        except Exception as e:
            logger.error(f"Error handling today's menu command: {str(e)}")
            return "Sorry, I couldn't retrieve today's menu. Please try again later."


# Global bot instance
luma_bot = LumaBot()
