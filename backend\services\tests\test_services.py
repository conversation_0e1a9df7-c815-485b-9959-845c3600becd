"""
Tests for services functionality.
"""

from django.test import TestCase
from django.core.exceptions import ValidationError
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal

from accounts.models import Business
from services.models import Service, ServiceAvailability, Appointment

User = get_user_model()


class ServiceModelTests(TestCase):
    """Test Service model functionality."""

    def setUp(self):
        self.service_owner = User.objects.create_user(
            username='serviceowner',
            email='<EMAIL>',
            password='testpass123',
            role='pod'
        )
        
        self.product_owner = User.objects.create_user(
            username='productowner',
            email='<EMAIL>',
            password='testpass123',
            role='pod'
        )
        
        self.service_business = Business.objects.create(
            owner=self.service_owner,
            name='Service Business',
            business_type=Business.BusinessType.SERVICE
        )
        
        self.product_business = Business.objects.create(
            owner=self.product_owner,
            name='Product Business',
            business_type=Business.BusinessType.PRODUCT_GOODS
        )

    def test_create_service_for_service_business(self):
        """Test creating a service for a service business."""
        service = Service.objects.create(
            business=self.service_business,
            name='Haircut',
            description='Professional haircut',
            price=Decimal('50.00'),
            duration_minutes=60
        )
        
        self.assertEqual(service.name, 'Haircut')
        self.assertEqual(service.business, self.service_business)
        self.assertEqual(service.total_duration_minutes, 60)  # No buffer time
        self.assertTrue(service.is_available)

    def test_service_with_buffer_time(self):
        """Test service with buffer time calculation."""
        service = Service.objects.create(
            business=self.service_business,
            name='Hair Styling',
            description='Professional hair styling',
            price=Decimal('75.00'),
            duration_minutes=90,
            buffer_time_minutes=15
        )
        
        self.assertEqual(service.total_duration_minutes, 105)  # 90 + 15

    def test_service_validation_for_non_service_business(self):
        """Test that services cannot be created for non-service businesses."""
        with self.assertRaises(ValidationError):
            service = Service(
                business=self.product_business,
                name='Invalid Service',
                price=Decimal('50.00'),
                duration_minutes=60
            )
            service.clean()

    def test_service_str_representation(self):
        """Test service string representation."""
        service = Service.objects.create(
            business=self.service_business,
            name='Manicure',
            price=Decimal('30.00'),
            duration_minutes=45
        )
        
        expected_str = "Manicure - Service Business"
        self.assertEqual(str(service), expected_str)


class ServiceAvailabilityTests(TestCase):
    """Test ServiceAvailability model functionality."""

    def setUp(self):
        self.service_owner = User.objects.create_user(
            username='serviceowner',
            email='<EMAIL>',
            password='testpass123',
            role='pod'
        )
        
        self.service_business = Business.objects.create(
            owner=self.service_owner,
            name='Service Business',
            business_type=Business.BusinessType.SERVICE
        )
        
        self.service = Service.objects.create(
            business=self.service_business,
            name='Haircut',
            price=Decimal('50.00'),
            duration_minutes=60
        )

    def test_create_service_availability(self):
        """Test creating service availability."""
        availability = ServiceAvailability.objects.create(
            service=self.service,
            day_of_week=1,  # Monday
            start_time='09:00',
            end_time='17:00',
            is_available=True
        )
        
        self.assertEqual(availability.service, self.service)
        self.assertEqual(availability.day_of_week, 1)
        self.assertTrue(availability.is_available)

    def test_service_availability_str_representation(self):
        """Test service availability string representation."""
        availability = ServiceAvailability.objects.create(
            service=self.service,
            day_of_week=1,  # Monday
            start_time='09:00',
            end_time='17:00'
        )
        
        expected_str = "Haircut - Monday: 09:00:00-17:00:00"
        self.assertEqual(str(availability), expected_str)


class AppointmentModelTests(TestCase):
    """Test Appointment model functionality."""

    def setUp(self):
        self.service_owner = User.objects.create_user(
            username='serviceowner',
            email='<EMAIL>',
            password='testpass123',
            role='pod'
        )
        
        self.customer = User.objects.create_user(
            username='customer',
            email='<EMAIL>',
            password='testpass123',
            role='pulse'
        )
        
        self.service_business = Business.objects.create(
            owner=self.service_owner,
            name='Service Business',
            business_type=Business.BusinessType.SERVICE
        )
        
        self.service = Service.objects.create(
            business=self.service_business,
            name='Haircut',
            price=Decimal('50.00'),
            duration_minutes=60
        )

    def test_create_appointment(self):
        """Test creating an appointment."""
        appointment_time = timezone.now() + timedelta(days=1)
        
        appointment = Appointment.objects.create(
            customer=self.customer,
            business=self.service_business,
            service=self.service,
            appointment_datetime=appointment_time
        )
        
        self.assertEqual(appointment.customer, self.customer)
        self.assertEqual(appointment.service, self.service)
        self.assertEqual(appointment.status, Appointment.AppointmentStatus.PENDING)
        self.assertEqual(appointment.service_price, Decimal('50.00'))
        self.assertEqual(appointment.duration_minutes, 60)
        self.assertTrue(appointment.appointment_number.startswith('APT'))

    def test_appointment_end_datetime(self):
        """Test appointment end datetime calculation."""
        appointment_time = timezone.now() + timedelta(days=1)
        
        appointment = Appointment.objects.create(
            customer=self.customer,
            business=self.service_business,
            service=self.service,
            appointment_datetime=appointment_time,
            duration_minutes=90
        )
        
        expected_end = appointment_time + timedelta(minutes=90)
        self.assertEqual(appointment.end_datetime, expected_end)

    def test_appointment_can_be_cancelled(self):
        """Test appointment cancellation logic."""
        future_appointment = Appointment.objects.create(
            customer=self.customer,
            business=self.service_business,
            service=self.service,
            appointment_datetime=timezone.now() + timedelta(days=1),
            status=Appointment.AppointmentStatus.CONFIRMED
        )
        
        self.assertTrue(future_appointment.can_be_cancelled)
        
        # Test completed appointment cannot be cancelled
        completed_appointment = Appointment.objects.create(
            customer=self.customer,
            business=self.service_business,
            service=self.service,
            appointment_datetime=timezone.now() + timedelta(days=2),
            status=Appointment.AppointmentStatus.COMPLETED
        )
        
        self.assertFalse(completed_appointment.can_be_cancelled)

    def test_appointment_str_representation(self):
        """Test appointment string representation."""
        appointment = Appointment.objects.create(
            customer=self.customer,
            business=self.service_business,
            service=self.service,
            appointment_datetime=timezone.now() + timedelta(days=1)
        )
        
        expected_str = f"Appointment {appointment.appointment_number} - customer - Haircut"
        self.assertEqual(str(appointment), expected_str)


class ServiceAPITests(APITestCase):
    """Test Service API functionality."""

    def setUp(self):
        self.service_owner = User.objects.create_user(
            username='serviceowner',
            email='<EMAIL>',
            password='testpass123',
            role='pod'
        )
        
        self.product_owner = User.objects.create_user(
            username='productowner',
            email='<EMAIL>',
            password='testpass123',
            role='pod'
        )
        
        self.customer = User.objects.create_user(
            username='customer',
            email='<EMAIL>',
            password='testpass123',
            role='pulse'
        )
        
        self.service_business = Business.objects.create(
            owner=self.service_owner,
            name='Service Business',
            business_type=Business.BusinessType.SERVICE
        )
        
        self.product_business = Business.objects.create(
            owner=self.product_owner,
            name='Product Business',
            business_type=Business.BusinessType.PRODUCT_GOODS
        )

    def test_create_service_as_service_business(self):
        """Test creating a service as a service business owner."""
        self.client.force_authenticate(user=self.service_owner)
        
        data = {
            'name': 'Haircut',
            'description': 'Professional haircut',
            'price': '50.00',
            'duration_minutes': 60,
            'category': 'Hair Services'
        }
        
        response = self.client.post('/api/services/', data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        service = Service.objects.get(id=response.data['id'])
        self.assertEqual(service.name, 'Haircut')
        self.assertEqual(service.business, self.service_business)

    def test_create_service_as_product_business_fails(self):
        """Test that product businesses cannot create services."""
        self.client.force_authenticate(user=self.product_owner)
        
        data = {
            'name': 'Invalid Service',
            'price': '50.00',
            'duration_minutes': 60
        }
        
        response = self.client.post('/api/services/', data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_list_services_as_customer(self):
        """Test that customers can view active services."""
        # Create a service
        service = Service.objects.create(
            business=self.service_business,
            name='Haircut',
            price=Decimal('50.00'),
            duration_minutes=60,
            status=Service.ServiceStatus.ACTIVE
        )
        
        self.client.force_authenticate(user=self.customer)
        response = self.client.get('/api/services/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['name'], 'Haircut')

    def test_available_time_slots(self):
        """Test getting available time slots for a service."""
        service = Service.objects.create(
            business=self.service_business,
            name='Haircut',
            price=Decimal('50.00'),
            duration_minutes=60
        )
        
        # Create availability
        ServiceAvailability.objects.create(
            service=service,
            day_of_week=1,  # Monday
            start_time='09:00',
            end_time='17:00',
            is_available=True
        )
        
        self.client.force_authenticate(user=self.customer)
        
        # Get next Monday's date
        today = timezone.now().date()
        days_ahead = 1 - today.weekday()  # Monday is 0
        if days_ahead <= 0:
            days_ahead += 7
        next_monday = today + timedelta(days=days_ahead)
        
        response = self.client.get(f'/api/services/{service.id}/available-slots/?date={next_monday}')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertEqual(data['service_name'], 'Haircut')
        self.assertGreater(len(data['available_slots']), 0)
