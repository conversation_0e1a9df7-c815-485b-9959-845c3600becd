version: '3.8'

services:
  db:
    image: postgres:15
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      POSTGRES_DB: mthunzi
      POSTGRES_USER: mthunzi
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U mthunzi"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  web:
    build: .
    command: gunicorn --bind 0.0.0.0:8000 --workers 3 mthunzi.wsgi:application
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - ./logs:/var/log/mthunzi
    ports:
      - "8000:8000"
    environment:
      - DJANGO_ENVIRONMENT=production
      - DATABASE_URL=postgresql://mthunzi:${POSTGRES_PASSWORD}@db:5432/mthunzi
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3

  celery:
    build: .
    command: celery -A mthunzi worker -l info
    volumes:
      - ./logs:/var/log/mthunzi
    environment:
      - DJANGO_ENVIRONMENT=production
      - DATABASE_URL=postgresql://mthunzi:${POSTGRES_PASSWORD}@db:5432/mthunzi
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    healthcheck:
      test: ["CMD", "celery", "-A", "mthunzi", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  celery-beat:
    build: .
    command: celery -A mthunzi beat -l info
    volumes:
      - ./logs:/var/log/mthunzi
    environment:
      - DJANGO_ENVIRONMENT=production
      - DATABASE_URL=postgresql://mthunzi:${POSTGRES_PASSWORD}@db:5432/mthunzi
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - web

volumes:
  postgres_data:
  redis_data:
  static_volume:
  media_volume:
