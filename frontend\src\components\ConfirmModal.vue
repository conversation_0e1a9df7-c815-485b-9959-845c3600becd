<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
    <!-- Backdrop -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" @click="handleBackdropClick"></div>
    
    <!-- Modal -->
    <div class="flex min-h-full items-center justify-center p-4">
      <div class="relative bg-white rounded-2xl shadow-xl max-w-md w-full transform transition-all">
        <!-- Header -->
        <div class="flex items-center justify-between p-6 border-b border-[#CCCCCC]/30">
          <div class="flex items-center space-x-3">
            <!-- Icon based on type -->
            <div 
              :class="iconClasses"
              class="w-10 h-10 rounded-full flex items-center justify-center"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path 
                  v-if="type === 'warning'" 
                  stroke-linecap="round" 
                  stroke-linejoin="round" 
                  stroke-width="2" 
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
                <path 
                  v-else-if="type === 'danger'" 
                  stroke-linecap="round" 
                  stroke-linejoin="round" 
                  stroke-width="2" 
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
                <path 
                  v-else-if="type === 'success'" 
                  stroke-linecap="round" 
                  stroke-linejoin="round" 
                  stroke-width="2" 
                  d="M5 13l4 4L19 7"
                />
                <path 
                  v-else 
                  stroke-linecap="round" 
                  stroke-linejoin="round" 
                  stroke-width="2" 
                  d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <h3 class="text-lg font-bold text-[#111111]">{{ title }}</h3>
          </div>
          
          <button 
            v-if="showCloseButton"
            @click="handleCancel" 
            class="p-2 hover:bg-[#F5F5F5] rounded-lg transition-colors"
          >
            <svg class="w-5 h-5 text-[#4B4B4B]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>

        <!-- Content -->
        <div class="p-6">
          <p class="text-[#4B4B4B] leading-relaxed">{{ message }}</p>
          
          <!-- Additional content slot -->
          <slot></slot>
        </div>

        <!-- Footer -->
        <div class="flex justify-end space-x-3 p-6 border-t border-[#CCCCCC]/30">
          <button 
            v-if="showCancelButton"
            @click="handleCancel"
            class="px-4 py-2 text-[#4B4B4B] hover:bg-[#F5F5F5] rounded-lg transition-colors"
          >
            {{ cancelText }}
          </button>
          
          <button 
            @click="handleConfirm"
            :disabled="isLoading"
            :class="confirmButtonClasses"
            class="px-4 py-2 rounded-lg transition-colors disabled:opacity-50 flex items-center space-x-2"
          >
            <div v-if="isLoading" class="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
            <span>{{ isLoading ? loadingText : confirmText }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'info', // 'info', 'warning', 'danger', 'success'
    validator: (value) => ['info', 'warning', 'danger', 'success'].includes(value)
  },
  title: {
    type: String,
    required: true
  },
  message: {
    type: String,
    required: true
  },
  confirmText: {
    type: String,
    default: 'Confirm'
  },
  cancelText: {
    type: String,
    default: 'Cancel'
  },
  loadingText: {
    type: String,
    default: 'Processing...'
  },
  showCancelButton: {
    type: Boolean,
    default: true
  },
  showCloseButton: {
    type: Boolean,
    default: true
  },
  closeOnBackdrop: {
    type: Boolean,
    default: true
  },
  isLoading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['confirm', 'cancel', 'close'])

// Computed properties
const iconClasses = computed(() => {
  switch (props.type) {
    case 'warning':
      return 'bg-[#FF8F00]/10 text-[#FF8F00]'
    case 'danger':
      return 'bg-[#C62828]/10 text-[#C62828]'
    case 'success':
      return 'bg-[#2E7D32]/10 text-[#2E7D32]'
    default:
      return 'bg-[#1E4E79]/10 text-[#1E4E79]'
  }
})

const confirmButtonClasses = computed(() => {
  switch (props.type) {
    case 'warning':
      return 'bg-[#FF8F00] text-white hover:bg-[#E65100]'
    case 'danger':
      return 'bg-[#C62828] text-white hover:bg-[#8E0000]'
    case 'success':
      return 'bg-[#2E7D32] text-white hover:bg-[#1B5E20]'
    default:
      return 'bg-[#1E4E79] text-white hover:bg-[#132F4C]'
  }
})

// Methods
const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
  emit('close')
}

const handleBackdropClick = () => {
  if (props.closeOnBackdrop) {
    handleCancel()
  }
}
</script>
