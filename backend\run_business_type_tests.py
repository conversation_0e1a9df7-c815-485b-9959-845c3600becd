#!/usr/bin/env python
"""
Test runner for business type functionality.
Run all tests related to the new business type system.
"""

import os
import sys
import django
from django.conf import settings
from django.test.utils import get_runner

if __name__ == "__main__":
    os.environ['DJANGO_SETTINGS_MODULE'] = 'mthunzi.settings.test'
    django.setup()
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    
    # Define test modules for business types
    test_modules = [
        'accounts.tests.test_business_types',
        'services.tests.test_services',
        'orders.tests.test_food_business',
        'whatsapp.tests.test_business_type_integration',
    ]
    
    print("🧪 Running Business Type System Tests")
    print("=" * 50)
    
    failures = test_runner.run_tests(test_modules)
    
    if failures:
        print(f"\n❌ {failures} test(s) failed")
        sys.exit(1)
    else:
        print("\n✅ All business type tests passed!")
        sys.exit(0)
