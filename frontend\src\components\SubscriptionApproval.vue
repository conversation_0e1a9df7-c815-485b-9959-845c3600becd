<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
      <h2 class="text-2xl font-bold text-[#111111]">Subscription Payment Approvals</h2>
      <div class="flex space-x-3">
        <!-- Filter Dropdown -->
        <select 
          v-model="statusFilter"
          @change="loadPayments"
          class="px-4 py-2 border border-[#CCCCCC] rounded-lg focus:ring-2 focus:ring-[#1E4E79] focus:border-transparent"
        >
          <option value="">All Payments</option>
          <option value="submitted">Pending Approval</option>
          <option value="approved">Approved</option>
          <option value="rejected">Rejected</option>
        </select>
        
        <button 
          @click="refreshData"
          :disabled="isLoading"
          class="px-4 py-2 bg-[#1E4E79] text-white rounded-lg hover:bg-[#132F4C] transition-colors disabled:opacity-50"
        >
          {{ isLoading ? 'Refreshing...' : 'Refresh' }}
        </button>
      </div>
    </div>

    <!-- Payments List -->
    <div class="bg-white rounded-2xl shadow-sm border border-[#CCCCCC]/30">
      <div class="p-6 border-b border-[#CCCCCC]/30">
        <h3 class="text-lg font-semibold text-[#111111]">Payment Requests</h3>
      </div>
      
      <div class="p-6">
        <div v-if="payments.length > 0" class="space-y-4">
          <div v-for="payment in payments" :key="payment.id" class="border border-[#CCCCCC]/30 rounded-lg p-6 hover:shadow-md transition-shadow">
            <div class="flex justify-between items-start">
              <div class="flex-1">
                <!-- Business Info -->
                <div class="flex items-center space-x-4 mb-4">
                  <div class="w-12 h-12 bg-gradient-to-br from-[#C1843E] to-[#704A1F] rounded-xl flex items-center justify-center">
                    <span class="text-white font-semibold">{{ payment.business_name?.charAt(0) || 'B' }}</span>
                  </div>
                  <div>
                    <h4 class="font-semibold text-[#111111]">{{ payment.business_name }}</h4>
                    <p class="text-sm text-[#4B4B4B]">{{ payment.business_owner }}</p>
                  </div>
                </div>
                
                <!-- Payment Details -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div>
                    <label class="block text-sm font-medium text-[#4B4B4B] mb-1">Plan</label>
                    <p class="text-[#111111]">{{ payment.plan_name }}</p>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-[#4B4B4B] mb-1">Amount</label>
                    <p class="text-[#111111] font-semibold">R{{ payment.amount }}</p>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-[#4B4B4B] mb-1">Status</label>
                    <span :class="getPaymentStatusClass(payment.status)" class="px-3 py-1 rounded-full text-xs font-medium">
                      {{ payment.status_display }}
                    </span>
                  </div>
                </div>
                
                <!-- Payment Method & Reference -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label class="block text-sm font-medium text-[#4B4B4B] mb-1">Payment Method</label>
                    <p class="text-[#111111]">{{ payment.payment_method || 'N/A' }}</p>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-[#4B4B4B] mb-1">Transaction Reference</label>
                    <p class="text-[#111111]">{{ payment.transaction_reference || 'N/A' }}</p>
                  </div>
                </div>
                
                <!-- Customer Notes -->
                <div v-if="payment.customer_notes" class="mb-4">
                  <label class="block text-sm font-medium text-[#4B4B4B] mb-1">Customer Notes</label>
                  <p class="text-[#111111] bg-[#F5F5F5] p-3 rounded-lg">{{ payment.customer_notes }}</p>
                </div>
                
                <!-- Payment Proofs -->
                <div v-if="payment.proofs?.length > 0" class="mb-4">
                  <label class="block text-sm font-medium text-[#4B4B4B] mb-2">Payment Proofs</label>
                  <div class="flex flex-wrap gap-2">
                    <a 
                      v-for="proof in payment.proofs" 
                      :key="proof.id"
                      :href="proof.file"
                      target="_blank"
                      class="inline-flex items-center space-x-2 px-3 py-2 bg-[#1E4E79]/10 text-[#1E4E79] rounded-lg hover:bg-[#1E4E79]/20 transition-colors"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.586-6.586a2 2 0 00-2.828-2.828l-6.586 6.586a2 2 0 102.828 2.828L19 9"/>
                      </svg>
                      <span>{{ proof.description || 'Payment Proof' }}</span>
                    </a>
                  </div>
                </div>
                
                <!-- Approval Notes (if approved/rejected) -->
                <div v-if="payment.approval_notes && (payment.status === 'approved' || payment.status === 'rejected')" class="mb-4">
                  <label class="block text-sm font-medium text-[#4B4B4B] mb-1">Admin Notes</label>
                  <p class="text-[#111111] bg-[#F5F5F5] p-3 rounded-lg">{{ payment.approval_notes }}</p>
                </div>
                
                <!-- Dates -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-[#4B4B4B]">
                  <div>
                    <span class="font-medium">Submitted:</span> {{ formatDate(payment.submitted_at) }}
                  </div>
                  <div v-if="payment.approved_at">
                    <span class="font-medium">Processed:</span> {{ formatDate(payment.approved_at) }}
                  </div>
                  <div v-if="payment.approved_by_name">
                    <span class="font-medium">Processed by:</span> {{ payment.approved_by_name }}
                  </div>
                </div>
              </div>
              
              <!-- Action Buttons -->
              <div v-if="payment.status === 'submitted'" class="ml-6 flex flex-col space-y-2">
                <button 
                  @click="openApprovalModal(payment, 'approved')"
                  class="px-4 py-2 bg-[#2E7D32] text-white rounded-lg hover:bg-[#1B5E20] transition-colors"
                >
                  Approve
                </button>
                <button 
                  @click="openApprovalModal(payment, 'rejected')"
                  class="px-4 py-2 bg-[#C62828] text-white rounded-lg hover:bg-[#8E0000] transition-colors"
                >
                  Reject
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Empty State -->
        <div v-else-if="!isLoading" class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-[#CCCCCC]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-[#111111]">No payments found</h3>
          <p class="mt-1 text-sm text-[#4B4B4B]">No subscription payments match your current filter.</p>
        </div>
        
        <!-- Loading State -->
        <div v-if="isLoading" class="flex justify-center items-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1E4E79]"></div>
          <span class="ml-2 text-[#4B4B4B]">Loading payments...</span>
        </div>
      </div>
    </div>

    <!-- Approval Modal -->
    <div v-if="approvalModal.isOpen" class="fixed inset-0 z-50 overflow-y-auto">
      <!-- Backdrop -->
      <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" @click="closeApprovalModal"></div>
      
      <!-- Modal -->
      <div class="flex min-h-full items-center justify-center p-4">
        <div class="relative bg-white rounded-2xl shadow-xl max-w-md w-full">
          <!-- Header -->
          <div class="flex items-center justify-between p-6 border-b border-[#CCCCCC]/30">
            <h3 class="text-lg font-bold text-[#111111]">
              {{ approvalModal.action === 'approved' ? 'Approve Payment' : 'Reject Payment' }}
            </h3>
            <button @click="closeApprovalModal" class="p-2 hover:bg-[#F5F5F5] rounded-lg transition-colors">
              <svg class="w-5 h-5 text-[#4B4B4B]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>

          <!-- Content -->
          <div class="p-6">
            <p class="text-[#4B4B4B] mb-4">
              Are you sure you want to {{ approvalModal.action === 'approved' ? 'approve' : 'reject' }} this payment from 
              <strong>{{ approvalModal.payment?.business_name }}</strong>?
            </p>
            
            <div class="mb-4">
              <label class="block text-sm font-medium text-[#4B4B4B] mb-2">Admin Notes (Optional)</label>
              <textarea 
                v-model="approvalModal.notes"
                rows="3"
                class="w-full px-3 py-2 border border-[#CCCCCC] rounded-lg focus:ring-2 focus:ring-[#1E4E79] focus:border-transparent"
                :placeholder="`Add notes about this ${approvalModal.action === 'approved' ? 'approval' : 'rejection'}...`"
              ></textarea>
            </div>
          </div>

          <!-- Footer -->
          <div class="flex justify-end space-x-3 p-6 border-t border-[#CCCCCC]/30">
            <button 
              @click="closeApprovalModal"
              class="px-4 py-2 text-[#4B4B4B] hover:bg-[#F5F5F5] rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button 
              @click="handleApproval"
              :disabled="isProcessing"
              :class="approvalModal.action === 'approved' 
                ? 'bg-[#2E7D32] hover:bg-[#1B5E20]' 
                : 'bg-[#C62828] hover:bg-[#8E0000]'"
              class="px-4 py-2 text-white rounded-lg transition-colors disabled:opacity-50"
            >
              {{ isProcessing ? 'Processing...' : (approvalModal.action === 'approved' ? 'Approve' : 'Reject') }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-if="error" class="bg-[#C62828]/5 border border-[#C62828]/20 rounded-lg p-4">
      <div class="flex items-center space-x-2">
        <svg class="w-5 h-5 text-[#C62828]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
        <p class="text-[#C62828] font-medium">Error loading payments</p>
      </div>
      <p class="text-[#C62828] text-sm mt-1">{{ error }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { subscriptionPaymentService } from '@/services/forgeApi'

// State
const payments = ref([])
const isLoading = ref(false)
const isProcessing = ref(false)
const error = ref(null)
const statusFilter = ref('submitted') // Default to pending approvals

// Approval modal state
const approvalModal = ref({
  isOpen: false,
  payment: null,
  action: 'approved', // 'approved' or 'rejected'
  notes: ''
})

// Methods
const loadPayments = async () => {
  isLoading.value = true
  error.value = null
  try {
    const filters = {}
    if (statusFilter.value) {
      filters.status = statusFilter.value
    }
    const data = await subscriptionPaymentService.getSubscriptionPayments(filters)
    payments.value = data
  } catch (err) {
    error.value = err.message
    console.error('Failed to load subscription payments:', err)
  } finally {
    isLoading.value = false
  }
}

const refreshData = () => {
  loadPayments()
}

const openApprovalModal = (payment, action) => {
  approvalModal.value = {
    isOpen: true,
    payment,
    action,
    notes: ''
  }
}

const closeApprovalModal = () => {
  approvalModal.value = {
    isOpen: false,
    payment: null,
    action: 'approved',
    notes: ''
  }
}

const handleApproval = async () => {
  isProcessing.value = true
  try {
    const approvalData = {
      status: approvalModal.value.action,
      approval_notes: approvalModal.value.notes
    }
    
    await subscriptionPaymentService.approvePayment(approvalModal.value.payment.id, approvalData)
    
    // Refresh payments list
    await loadPayments()
    closeApprovalModal()
    
    // Show success message (you could add a toast notification here)
    console.log(`Payment ${approvalModal.value.action} successfully`)
  } catch (err) {
    console.error('Failed to process payment approval:', err)
    error.value = err.message
  } finally {
    isProcessing.value = false
  }
}

const getPaymentStatusClass = (status) => {
  switch (status) {
    case 'approved':
      return 'bg-[#2E7D32]/10 text-[#2E7D32]'
    case 'submitted':
      return 'bg-[#FF8F00]/10 text-[#FF8F00]'
    case 'pending':
      return 'bg-[#4B4B4B]/10 text-[#4B4B4B]'
    case 'rejected':
      return 'bg-[#C62828]/10 text-[#C62828]'
    default:
      return 'bg-[#4B4B4B]/10 text-[#4B4B4B]'
  }
}

const formatDate = (dateString) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Lifecycle
onMounted(() => {
  loadPayments()
})
</script>
