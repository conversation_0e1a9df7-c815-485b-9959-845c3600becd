"""
Tests for the accounts app.
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from rest_framework.authtoken.models import Token

from .models import Business, UserProfile

User = get_user_model()


class UserModelTest(TestCase):
    """Test the custom User model."""

    def setUp(self):
        self.user_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'phone_number': '+***********',
            'role': 'pulse',
            'password': 'testpass123'
        }

    def test_create_user(self):
        """Test creating a user."""
        user = User.objects.create_user(**self.user_data)

        self.assertEqual(user.username, 'testuser')
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.phone_number, '+***********')
        self.assertEqual(user.role, 'pulse')
        self.assertTrue(user.check_password('testpass123'))
        self.assertFalse(user.is_verified)

    def test_user_role_properties(self):
        """Test user role properties."""
        # Test Pulse user
        pulse_user = User.objects.create_user(
            username='pulse', phone_number='+27111111111', role='pulse'
        )
        self.assertTrue(pulse_user.is_pulse)
        self.assertFalse(pulse_user.is_pod)
        self.assertFalse(pulse_user.is_runner)
        self.assertFalse(pulse_user.is_forge)

        # Test Pod user
        pod_user = User.objects.create_user(
            username='pod', phone_number='+27222222222', role='pod'
        )
        self.assertTrue(pod_user.is_pod)
        self.assertFalse(pod_user.is_pulse)

        # Test Runner user
        runner_user = User.objects.create_user(
            username='runner', phone_number='+***********', role='runner'
        )
        self.assertTrue(runner_user.is_runner)
        self.assertFalse(runner_user.is_pulse)

    def test_user_str_representation(self):
        """Test user string representation."""
        user = User.objects.create_user(**self.user_data)
        expected = f"{user.username} ({user.get_role_display()})"
        self.assertEqual(str(user), expected)


class BusinessModelTest(TestCase):
    """Test the Business model."""

    def setUp(self):
        self.pod_user = User.objects.create_user(
            username='business_owner',
            phone_number='+***********',
            role='pod'
        )

        self.business_data = {
            'owner': self.pod_user,
            'name': 'Test Business',
            'description': 'A test business',
            'business_phone': '+***********',
            'business_email': '<EMAIL>',
            'address': '123 Business St, City'
        }

    def test_create_business(self):
        """Test creating a business."""
        business = Business.objects.create(**self.business_data)

        self.assertEqual(business.name, 'Test Business')
        self.assertEqual(business.owner, self.pod_user)
        self.assertEqual(business.status, 'active')  # Default status
        self.assertEqual(str(business), 'Test Business')

    def test_business_welcome_message_default(self):
        """Test business default welcome message."""
        business = Business.objects.create(**self.business_data)
        self.assertIn('Welcome to our business', business.welcome_message)
