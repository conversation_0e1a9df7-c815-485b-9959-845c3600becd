from django.db import models
from django.conf import settings
from django.core.validators import MinValueValidator
from decimal import Decimal
import uuid


class PaymentMethod(models.Model):
    """
    Available payment methods for the business.
    """

    class MethodType(models.TextChoices):
        BANK_TRANSFER = 'bank_transfer', 'Bank Transfer'
        MOBILE_MONEY = 'mobile_money', 'Mobile Money'
        CASH_ON_DELIVERY = 'cash_on_delivery', 'Cash on Delivery'
        CARD = 'card', 'Card Payment'

    business = models.ForeignKey(
        'accounts.Business',
        on_delete=models.CASCADE,
        related_name='payment_methods'
    )

    method_type = models.CharField(max_length=20, choices=MethodType.choices)
    name = models.CharField(max_length=100, help_text="Display name for this payment method")

    # Payment details (flexible JSON field would be better, but keeping simple)
    account_name = models.CharField(max_length=200, blank=True)
    account_number = models.CharField(max_length=100, blank=True)
    bank_name = models.Char<PERSON>ield(max_length=100, blank=True)
    mobile_number = models.CharField(max_length=17, blank=True)

    instructions = models.TextField(
        blank=True,
        help_text="Instructions to show customers for this payment method"
    )

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} - {self.business.name}"

    class Meta:
        unique_together = ['business', 'method_type', 'name']


class Payment(models.Model):
    """
    Payment records for orders.
    """

    class PaymentStatus(models.TextChoices):
        PENDING = 'pending', 'Pending'
        SUBMITTED = 'submitted', 'Proof Submitted'
        VERIFIED = 'verified', 'Verified'
        REJECTED = 'rejected', 'Rejected'
        REFUNDED = 'refunded', 'Refunded'

    # Payment identification
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    payment_reference = models.CharField(max_length=50, unique=True)

    # Relationships
    order = models.OneToOneField(
        'orders.Order',
        on_delete=models.CASCADE,
        related_name='payment'
    )
    payment_method = models.ForeignKey(
        PaymentMethod,
        on_delete=models.CASCADE,
        related_name='payments'
    )

    # Payment details
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))]
    )
    status = models.CharField(
        max_length=10,
        choices=PaymentStatus.choices,
        default=PaymentStatus.PENDING
    )

    # Customer provided information
    customer_reference = models.CharField(
        max_length=100,
        blank=True,
        help_text="Reference number provided by customer"
    )
    customer_notes = models.TextField(
        blank=True,
        help_text="Additional notes from customer about payment"
    )

    # Verification
    verified_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        limit_choices_to={'role': 'pod'},
        related_name='verified_payments'
    )
    verification_notes = models.TextField(
        blank=True,
        help_text="Notes from business owner during verification"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    submitted_at = models.DateTimeField(null=True, blank=True)
    verified_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"Payment {self.payment_reference} - Order {self.order.order_number}"

    def save(self, *args, **kwargs):
        if not self.payment_reference:
            # Generate payment reference
            import random
            import string
            self.payment_reference = 'PAY-' + ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
        super().save(*args, **kwargs)

    class Meta:
        ordering = ['-created_at']


class PaymentProof(models.Model):
    """
    Proof of payment files uploaded by customers.
    """

    class ProofType(models.TextChoices):
        SCREENSHOT = 'screenshot', 'Screenshot'
        RECEIPT = 'receipt', 'Receipt'
        BANK_STATEMENT = 'bank_statement', 'Bank Statement'
        OTHER = 'other', 'Other'

    payment = models.ForeignKey(
        Payment,
        on_delete=models.CASCADE,
        related_name='proofs'
    )

    proof_type = models.CharField(
        max_length=15,
        choices=ProofType.choices,
        default=ProofType.SCREENSHOT
    )

    file = models.FileField(
        upload_to='payment_proofs/',
        help_text="Upload proof of payment (image or PDF)"
    )

    description = models.CharField(
        max_length=200,
        blank=True,
        help_text="Brief description of the proof"
    )

    # WhatsApp integration
    whatsapp_media_id = models.CharField(max_length=100, blank=True)

    uploaded_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Proof for {self.payment.payment_reference}"

    class Meta:
        ordering = ['-uploaded_at']
