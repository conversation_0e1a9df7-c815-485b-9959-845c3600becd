"""
Admin dashboard views for business analytics and management.
"""

from rest_framework import permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.db.models import Count, Sum, Avg, Q, F
from django.utils import timezone
from datetime import timedelta

from .models import User, Business
from orders.models import Order, Product
from payments.models import Payment
from deliveries.models import Delivery
from whatsapp.models import WhatsAppMessage
from .permissions import IsPod, IsForge


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated, IsPod])
def business_dashboard(request):
    """
    Main dashboard data for business owners.
    """
    user = request.user
    business = getattr(user, 'business', None)
    
    if not business:
        return Response({'error': 'No business found for user'}, status=400)
    
    # Date range for analytics (last 30 days)
    end_date = timezone.now()
    start_date = end_date - timedelta(days=30)
    
    # Order statistics
    orders = Order.objects.filter(business=business)
    recent_orders = orders.filter(created_at__gte=start_date)
    
    order_stats = {
        'total_orders': orders.count(),
        'recent_orders': recent_orders.count(),
        'pending_orders': orders.filter(status='pending').count(),
        'confirmed_orders': orders.filter(status='confirmed').count(),
        'delivered_orders': orders.filter(status='delivered').count(),
        'cancelled_orders': orders.filter(status='cancelled').count(),
    }
    
    # Revenue statistics
    revenue_stats = {
        'total_revenue': orders.filter(status='delivered').aggregate(
            total=Sum('total_amount')
        )['total'] or 0,
        'recent_revenue': recent_orders.filter(status='delivered').aggregate(
            total=Sum('total_amount')
        )['total'] or 0,
        'average_order_value': orders.filter(status='delivered').aggregate(
            avg=Avg('total_amount')
        )['avg'] or 0,
    }
    
    # Payment statistics
    payments = Payment.objects.filter(order__business=business)
    payment_stats = {
        'pending_payments': payments.filter(status='pending').count(),
        'submitted_payments': payments.filter(status='submitted').count(),
        'verified_payments': payments.filter(status='verified').count(),
        'rejected_payments': payments.filter(status='rejected').count(),
    }
    
    # Product statistics
    products = Product.objects.filter(business=business)
    product_stats = {
        'total_products': products.count(),
        'active_products': products.filter(status='active').count(),
        'low_stock_products': products.filter(
            stock_quantity__lte=F('low_stock_threshold')
        ).count(),
        'out_of_stock_products': products.filter(stock_quantity=0).count(),
    }
    
    # Customer statistics
    customer_ids = orders.values_list('customer_id', flat=True).distinct()
    customer_stats = {
        'total_customers': len(customer_ids),
        'new_customers': orders.filter(
            created_at__gte=start_date
        ).values('customer').distinct().count(),
        'repeat_customers': orders.values('customer').annotate(
            order_count=Count('id')
        ).filter(order_count__gt=1).count(),
    }
    
    # Recent activity
    recent_orders_data = recent_orders.order_by('-created_at')[:5].values(
        'id', 'order_number', 'customer__first_name', 'customer__last_name',
        'total_amount', 'status', 'created_at'
    )
    
    recent_payments_data = payments.filter(
        created_at__gte=start_date
    ).order_by('-created_at')[:5].values(
        'id', 'payment_reference', 'order__order_number',
        'amount', 'status', 'created_at'
    )
    
    return Response({
        'business': {
            'id': business.id,
            'name': business.name,
            'status': business.status,
        },
        'order_stats': order_stats,
        'revenue_stats': revenue_stats,
        'payment_stats': payment_stats,
        'product_stats': product_stats,
        'customer_stats': customer_stats,
        'recent_orders': list(recent_orders_data),
        'recent_payments': list(recent_payments_data),
        'date_range': {
            'start_date': start_date.isoformat(),
            'end_date': end_date.isoformat(),
        }
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated, IsPod])
def sales_analytics(request):
    """
    Sales analytics for business owners.
    """
    user = request.user
    business = getattr(user, 'business', None)
    
    if not business:
        return Response({'error': 'No business found for user'}, status=400)
    
    # Get date range from query params
    days = int(request.GET.get('days', 30))
    end_date = timezone.now()
    start_date = end_date - timedelta(days=days)
    
    orders = Order.objects.filter(
        business=business,
        created_at__gte=start_date,
        status='delivered'
    )
    
    # Daily sales data
    daily_sales = []
    current_date = start_date.date()
    while current_date <= end_date.date():
        day_orders = orders.filter(created_at__date=current_date)
        daily_sales.append({
            'date': current_date.isoformat(),
            'orders': day_orders.count(),
            'revenue': day_orders.aggregate(total=Sum('total_amount'))['total'] or 0,
        })
        current_date += timedelta(days=1)
    
    # Top products
    top_products = Product.objects.filter(
        business=business,
        orderitem__order__in=orders
    ).annotate(
        total_sold=Sum('orderitem__quantity'),
        total_revenue=Sum('orderitem__total_price')
    ).order_by('-total_sold')[:10]
    
    top_products_data = [{
        'id': product.id,
        'name': product.name,
        'total_sold': product.total_sold,
        'total_revenue': float(product.total_revenue or 0),
    } for product in top_products]
    
    # Order status distribution
    status_distribution = orders.values('status').annotate(
        count=Count('id')
    ).order_by('-count')
    
    return Response({
        'daily_sales': daily_sales,
        'top_products': top_products_data,
        'status_distribution': list(status_distribution),
        'summary': {
            'total_orders': orders.count(),
            'total_revenue': orders.aggregate(total=Sum('total_amount'))['total'] or 0,
            'average_order_value': orders.aggregate(avg=Avg('total_amount'))['avg'] or 0,
        }
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated, IsForge])
def system_dashboard(request):
    """
    System-wide dashboard for Forge users.
    """
    # Date range for analytics (last 30 days)
    end_date = timezone.now()
    start_date = end_date - timedelta(days=30)
    
    # Business statistics
    businesses = Business.objects.all()
    business_stats = {
        'total_businesses': businesses.count(),
        'active_businesses': businesses.filter(status='active').count(),
        'inactive_businesses': businesses.filter(status='inactive').count(),
        'suspended_businesses': businesses.filter(status='suspended').count(),
    }
    
    # User statistics
    users = User.objects.all()
    user_stats = {
        'total_users': users.count(),
        'forge_users': users.filter(role='forge').count(),
        'pod_users': users.filter(role='pod').count(),
        'pulse_users': users.filter(role='pulse').count(),
        'runner_users': users.filter(role='runner').count(),
        'verified_users': users.filter(is_verified=True).count(),
    }
    
    # Order statistics
    orders = Order.objects.all()
    recent_orders = orders.filter(created_at__gte=start_date)
    order_stats = {
        'total_orders': orders.count(),
        'recent_orders': recent_orders.count(),
        'total_revenue': orders.filter(status='delivered').aggregate(
            total=Sum('total_amount')
        )['total'] or 0,
        'recent_revenue': recent_orders.filter(status='delivered').aggregate(
            total=Sum('total_amount')
        )['total'] or 0,
    }
    
    # WhatsApp statistics
    messages = WhatsAppMessage.objects.all()
    recent_messages = messages.filter(created_at__gte=start_date)
    whatsapp_stats = {
        'total_messages': messages.count(),
        'recent_messages': recent_messages.count(),
        'inbound_messages': messages.filter(direction='inbound').count(),
        'outbound_messages': messages.filter(direction='outbound').count(),
    }
    
    # Top businesses by revenue
    top_businesses = Business.objects.annotate(
        total_revenue=Sum('orders__total_amount', filter=Q(orders__status='delivered'))
    ).order_by('-total_revenue')[:10]
    
    top_businesses_data = [{
        'id': business.id,
        'name': business.name,
        'owner': business.owner.get_full_name(),
        'total_revenue': float(business.total_revenue or 0),
        'order_count': business.orders.count(),
    } for business in top_businesses]
    
    return Response({
        'business_stats': business_stats,
        'user_stats': user_stats,
        'order_stats': order_stats,
        'whatsapp_stats': whatsapp_stats,
        'top_businesses': top_businesses_data,
        'date_range': {
            'start_date': start_date.isoformat(),
            'end_date': end_date.isoformat(),
        }
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated, IsPod])
def inventory_alerts(request):
    """
    Inventory alerts for business owners.
    """
    user = request.user
    business = getattr(user, 'business', None)
    
    if not business:
        return Response({'error': 'No business found for user'}, status=400)
    
    products = Product.objects.filter(business=business)
    
    # Low stock products
    low_stock = products.filter(
        stock_quantity__lte=F('low_stock_threshold'),
        stock_quantity__gt=0
    ).values('id', 'name', 'stock_quantity', 'low_stock_threshold')
    
    # Out of stock products
    out_of_stock = products.filter(stock_quantity=0).values(
        'id', 'name', 'stock_quantity'
    )
    
    # Products with no stock threshold set
    no_threshold = products.filter(low_stock_threshold=0).values(
        'id', 'name', 'stock_quantity'
    )
    
    return Response({
        'low_stock_products': list(low_stock),
        'out_of_stock_products': list(out_of_stock),
        'no_threshold_products': list(no_threshold),
        'alerts_count': {
            'low_stock': len(low_stock),
            'out_of_stock': len(out_of_stock),
            'no_threshold': len(no_threshold),
        }
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_activity(request):
    """
    User activity dashboard based on role.
    """
    user = request.user
    
    if user.role == 'pulse':
        # Customer activity
        orders = Order.objects.filter(customer=user)
        payments = Payment.objects.filter(order__customer=user)
        
        return Response({
            'role': 'customer',
            'orders': {
                'total': orders.count(),
                'pending': orders.filter(status='pending').count(),
                'delivered': orders.filter(status='delivered').count(),
                'total_spent': orders.filter(status='delivered').aggregate(
                    total=Sum('total_amount')
                )['total'] or 0,
            },
            'payments': {
                'total': payments.count(),
                'verified': payments.filter(status='verified').count(),
                'pending': payments.filter(status='pending').count(),
            }
        })
    
    elif user.role == 'runner':
        # Runner activity
        deliveries = Delivery.objects.filter(runner=user)
        
        return Response({
            'role': 'runner',
            'deliveries': {
                'total': deliveries.count(),
                'completed': deliveries.filter(status='delivered').count(),
                'in_progress': deliveries.filter(
                    status__in=['assigned', 'picked_up', 'in_transit']
                ).count(),
                'failed': deliveries.filter(status='failed').count(),
            },
            'earnings': {
                'total': deliveries.filter(status='delivered').aggregate(
                    total=Sum('delivery_fee')
                )['total'] or 0,
            }
        })
    
    else:
        return Response({'error': 'Dashboard not available for this role'}, status=400)
