from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.utils import timezone
from datetime import datetime, timedelta
from django.db.models import Q

from .models import Service, ServiceAvailability, Appointment
from .serializers import (
    ServiceSerializer, ServiceAvailabilitySerializer, AppointmentSerializer,
    AppointmentUpdateSerializer, ServiceWithAvailabilitySerializer
)
from accounts.permissions import IsPod, IsPulse
from accounts.validators import require_business_type


class ServiceListCreateView(generics.ListCreateAPIView):
    """
    API view for listing and creating services.
    """
    serializer_class = ServiceSerializer
    permission_classes = [permissions.IsAuthenticated]

    @require_business_type('service')
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)

    def get_queryset(self):
        user = self.request.user

        if user.role == 'forge':
            return Service.objects.all()
        elif user.role == 'pod':
            business = getattr(user, 'business', None)
            if business and business.is_service_business:
                return Service.objects.filter(business=business)
        elif user.role == 'pulse':
            # Customers can see all active services
            return Service.objects.filter(status='active')

        return Service.objects.none()

    def perform_create(self, serializer):
        # Only Pod users can create services
        if self.request.user.role != 'pod':
            raise permissions.PermissionDenied("Only business owners can create services.")

        business = getattr(self.request.user, 'business', None)
        if not business or not business.is_service_business:
            raise permissions.PermissionDenied("Only service businesses can create services.")

        serializer.save(business=business)


class ServiceDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API view for service detail, update, and delete.
    """
    serializer_class = ServiceWithAvailabilitySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user

        if user.role == 'forge':
            return Service.objects.all()
        elif user.role == 'pod':
            business = getattr(user, 'business', None)
            if business:
                return Service.objects.filter(business=business)
        elif user.role == 'pulse':
            return Service.objects.filter(status='active')

        return Service.objects.none()

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return ServiceSerializer
        return ServiceWithAvailabilitySerializer


class ServiceAvailabilityListCreateView(generics.ListCreateAPIView):
    """
    API view for managing service availability schedules.
    """
    serializer_class = ServiceAvailabilitySerializer
    permission_classes = [permissions.IsAuthenticated, IsPod]

    def get_queryset(self):
        service_id = self.kwargs.get('service_id')
        business = getattr(self.request.user, 'business', None)

        if business:
            return ServiceAvailability.objects.filter(
                service_id=service_id,
                service__business=business
            )
        return ServiceAvailability.objects.none()


class AppointmentListCreateView(generics.ListCreateAPIView):
    """
    API view for listing and creating appointments.
    """
    serializer_class = AppointmentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user

        if user.role == 'forge':
            return Appointment.objects.all()
        elif user.role == 'pod':
            business = getattr(user, 'business', None)
            if business:
                return Appointment.objects.filter(business=business)
        elif user.role == 'pulse':
            return Appointment.objects.filter(customer=user)

        return Appointment.objects.none()

    def perform_create(self, serializer):
        # Only Pulse users can create appointments
        if self.request.user.role != 'pulse':
            raise permissions.PermissionDenied("Only customers can book appointments.")

        serializer.save()


class AppointmentDetailView(generics.RetrieveUpdateAPIView):
    """
    API view for appointment detail and update.
    """
    serializer_class = AppointmentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return AppointmentUpdateSerializer
        return AppointmentSerializer

    def get_queryset(self):
        user = self.request.user

        if user.role == 'forge':
            return Appointment.objects.all()
        elif user.role == 'pod':
            business = getattr(user, 'business', None)
            if business:
                return Appointment.objects.filter(business=business)
        elif user.role == 'pulse':
            return Appointment.objects.filter(customer=user)

        return Appointment.objects.none()


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated, IsPod])
def confirm_appointment(request, appointment_id):
    """
    API endpoint for confirming an appointment.
    """
    try:
        business = getattr(request.user, 'business', None)
        appointment = Appointment.objects.get(id=appointment_id, business=business)

        if appointment.status != 'pending':
            return Response({
                'error': 'Appointment can only be confirmed from pending status'
            }, status=status.HTTP_400_BAD_REQUEST)

        appointment.status = 'confirmed'
        appointment.confirmed_at = timezone.now()
        appointment.save()

        serializer = AppointmentSerializer(appointment)
        return Response(serializer.data)

    except Appointment.DoesNotExist:
        return Response({
            'error': 'Appointment not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def cancel_appointment(request, appointment_id):
    """
    API endpoint for cancelling an appointment.
    """
    try:
        user = request.user

        # Get appointment based on user role
        if user.role == 'pod':
            business = getattr(user, 'business', None)
            appointment = Appointment.objects.get(id=appointment_id, business=business)
        elif user.role == 'pulse':
            appointment = Appointment.objects.get(id=appointment_id, customer=user)
        else:
            raise permissions.PermissionDenied("You don't have permission to cancel this appointment.")

        if not appointment.can_be_cancelled:
            return Response({
                'error': 'Appointment cannot be cancelled in its current status'
            }, status=status.HTTP_400_BAD_REQUEST)

        appointment.status = 'cancelled'
        appointment.cancelled_at = timezone.now()
        appointment.save()

        serializer = AppointmentSerializer(appointment)
        return Response(serializer.data)

    except Appointment.DoesNotExist:
        return Response({
            'error': 'Appointment not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def available_time_slots(request, service_id):
    """
    API endpoint for getting available time slots for a service.
    """
    try:
        service = Service.objects.get(id=service_id, status='active')
        date_str = request.GET.get('date')

        if not date_str:
            return Response({
                'error': 'Date parameter is required (YYYY-MM-DD format)'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            requested_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return Response({
                'error': 'Invalid date format. Use YYYY-MM-DD'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if date is in the past
        if requested_date < timezone.now().date():
            return Response({
                'error': 'Cannot book appointments for past dates'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if date is too far in advance
        max_advance_date = timezone.now().date() + timedelta(days=service.business.booking_advance_days)
        if requested_date > max_advance_date:
            return Response({
                'error': f'Cannot book appointments more than {service.business.booking_advance_days} days in advance'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get day of week (1=Monday, 7=Sunday)
        day_of_week = requested_date.isoweekday()

        # Get service availability for this day
        try:
            availability = ServiceAvailability.objects.get(
                service=service,
                day_of_week=day_of_week,
                is_available=True
            )
        except ServiceAvailability.DoesNotExist:
            return Response({
                'available_slots': [],
                'message': 'Service is not available on this day'
            })

        # Generate time slots
        available_slots = []
        current_time = datetime.combine(requested_date, availability.start_time)
        end_time = datetime.combine(requested_date, availability.end_time)

        # Get existing appointments for this date
        existing_appointments = Appointment.objects.filter(
            service=service,
            appointment_datetime__date=requested_date,
            status__in=['pending', 'confirmed', 'in_progress']
        )

        while current_time + timedelta(minutes=service.total_duration_minutes) <= end_time:
            slot_end_time = current_time + timedelta(minutes=service.total_duration_minutes)

            # Check if this slot conflicts with existing appointments
            is_available = True
            for appointment in existing_appointments:
                appointment_end = appointment.appointment_datetime + timedelta(minutes=appointment.duration_minutes)

                # Check for overlap
                if (current_time < appointment_end and slot_end_time > appointment.appointment_datetime):
                    is_available = False
                    break

            available_slots.append({
                'start_time': current_time.strftime('%H:%M'),
                'end_time': slot_end_time.strftime('%H:%M'),
                'datetime': current_time.isoformat(),
                'is_available': is_available
            })

            # Move to next slot (with buffer time)
            current_time += timedelta(minutes=service.business.booking_buffer_minutes or 15)

        return Response({
            'service_name': service.name,
            'date': date_str,
            'available_slots': available_slots
        })

    except Service.DoesNotExist:
        return Response({
            'error': 'Service not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def reschedule_appointment(request, appointment_id):
    """
    API endpoint for rescheduling an appointment.
    """
    try:
        user = request.user
        new_datetime_str = request.data.get('new_datetime')

        if not new_datetime_str:
            return Response({
                'error': 'new_datetime is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            new_datetime = datetime.fromisoformat(new_datetime_str.replace('Z', '+00:00'))
        except ValueError:
            return Response({
                'error': 'Invalid datetime format'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get appointment based on user role
        if user.role == 'pod':
            business = getattr(user, 'business', None)
            appointment = Appointment.objects.get(id=appointment_id, business=business)
        elif user.role == 'pulse':
            appointment = Appointment.objects.get(id=appointment_id, customer=user)
        else:
            raise permissions.PermissionDenied("You don't have permission to reschedule this appointment.")

        if not appointment.can_be_rescheduled:
            return Response({
                'error': 'Appointment cannot be rescheduled in its current status'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate new datetime
        if new_datetime <= timezone.now():
            return Response({
                'error': 'New appointment time cannot be in the past'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check availability for new time slot
        service = appointment.service
        new_date = new_datetime.date()
        day_of_week = new_date.isoweekday()

        try:
            availability = ServiceAvailability.objects.get(
                service=service,
                day_of_week=day_of_week,
                is_available=True
            )
        except ServiceAvailability.DoesNotExist:
            return Response({
                'error': 'Service is not available on the requested day'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if new time is within service hours
        new_time = new_datetime.time()
        if new_time < availability.start_time or new_time > availability.end_time:
            return Response({
                'error': 'Requested time is outside service hours'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check for conflicts with other appointments
        conflicting_appointments = Appointment.objects.filter(
            service=service,
            appointment_datetime__date=new_date,
            status__in=['pending', 'confirmed', 'in_progress']
        ).exclude(id=appointment_id)

        appointment_end = new_datetime + timedelta(minutes=appointment.duration_minutes)

        for other_appointment in conflicting_appointments:
            other_end = other_appointment.appointment_datetime + timedelta(minutes=other_appointment.duration_minutes)

            # Check for overlap
            if (new_datetime < other_end and appointment_end > other_appointment.appointment_datetime):
                return Response({
                    'error': 'The requested time slot conflicts with another appointment'
                }, status=status.HTTP_400_BAD_REQUEST)

        # Update appointment
        appointment.appointment_datetime = new_datetime
        appointment.status = 'pending'  # Reset to pending for confirmation
        appointment.save()

        serializer = AppointmentSerializer(appointment)
        return Response(serializer.data)

    except Appointment.DoesNotExist:
        return Response({
            'error': 'Appointment not found'
        }, status=status.HTTP_404_NOT_FOUND)
