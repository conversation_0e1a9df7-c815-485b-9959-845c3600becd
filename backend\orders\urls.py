"""
URL patterns for the orders app.
"""

from django.urls import path
from . import views

app_name = 'orders'

urlpatterns = [
    # Product endpoints
    path('products/', views.ProductListCreateView.as_view(), name='product-list'),
    path('products/<int:pk>/', views.ProductDetailView.as_view(), name='product-detail'),
    path('businesses/<int:business_id>/products/', views.BusinessProductListView.as_view(), name='business-products'),

    # Order endpoints
    path('', views.OrderListCreateView.as_view(), name='order-list'),
    path('<uuid:pk>/', views.OrderDetailView.as_view(), name='order-detail'),
    path('<uuid:order_id>/confirm/', views.confirm_order, name='order-confirm'),

    # Daily menu management (for food businesses)
    path('daily-menus/', views.DailyMenuListCreateView.as_view(), name='daily-menu-list-create'),
    path('daily-menus/<int:pk>/', views.DailyMenuDetailView.as_view(), name='daily-menu-detail'),
    path('daily-menus/<int:menu_id>/items/', views.DailyMenuItemListCreateView.as_view(), name='daily-menu-items'),

    # Today's menu
    path('todays-menu/', views.todays_menu, name='todays-menu'),
    path('businesses/<int:business_id>/todays-menu/', views.todays_menu, name='business-todays-menu'),
]
