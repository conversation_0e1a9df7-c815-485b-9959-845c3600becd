# Mthunzi User Guide

Welcome to Mthunzi - Your WhatsApp-based sales and delivery automation system! This comprehensive guide will help you get started and make the most of all features.

## Table of Contents

1. [Getting Started](#getting-started)
2. [For Business Owners (Pod Users)](#for-business-owners-pod-users)
3. [For Customers (Pulse Users)](#for-customers-pulse-users)
4. [For Delivery Agents (Runner Users)](#for-delivery-agents-runner-users)
5. [For System Administrators (Forge Users)](#for-system-administrators-forge-users)
6. [WhatsApp Bot (Luma) Guide](#whatsapp-bot-luma-guide)
7. [Troubleshooting](#troubleshooting)

---

## Getting Started

### What is Mthunzi?

Mthunzi is a comprehensive WhatsApp-based sales and delivery automation system designed for Small and Medium Enterprises (SMEs). It allows businesses to:

- Manage product catalogs
- Process orders through WhatsApp
- Handle payments and verification
- Track deliveries in real-time
- Communicate with customers automatically

### User Roles

Mthunzi has four distinct user roles:

- **🔥 Forge**: System administrators with full access
- **🏢 Pod**: Business owners who manage their stores
- **💫 Pulse**: Customers who place orders
- **🏃 Runner**: Delivery agents who fulfill orders

### First Time Setup

1. **Account Creation**: Your account will be created by a system administrator
2. **Login**: Use your username and password to access the system
3. **Profile Setup**: Complete your profile information
4. **Phone Verification**: Verify your phone number for WhatsApp integration

---

## For Business Owners (Pod Users)

### Initial Business Setup

#### 1. Complete Your Business Profile

1. Log into the dashboard at `https://yourdomain.com`
2. Navigate to **Settings** → **Business Profile**
3. Fill in all required information:
   - Business name and description
   - Contact information (phone, email, address)
   - Business hours
   - Welcome message for customers

#### 2. Set Up Payment Methods

1. Go to **Settings** → **Payment Methods**
2. Click **Add Payment Method**
3. Configure your payment options:
   - **Bank Transfer**: Add bank details
   - **Mobile Money**: Add mobile money numbers
   - **Cash on Delivery**: Enable if applicable
4. Add clear payment instructions for customers

#### 3. Configure Delivery Zones

1. Navigate to **Deliveries** → **Delivery Zones**
2. Create delivery zones for your service areas:
   - Zone name (e.g., "City Center", "Suburbs")
   - Delivery fee for each zone
   - Estimated delivery time
3. Set zones as active/inactive as needed

### Product Management

#### Adding Products

1. Go to **Products** → **Add Product**
2. Fill in product details:
   - **Name**: Clear, descriptive product name
   - **Description**: Detailed product description
   - **Price**: Set your selling price
   - **SKU**: Unique product identifier (optional)
   - **Category**: Organize products by category
   - **Stock Quantity**: Current inventory level
   - **Low Stock Threshold**: When to get low stock alerts
   - **Image**: Upload product photo

#### Managing Inventory

1. **Stock Updates**: Regularly update stock quantities
2. **Low Stock Alerts**: Monitor inventory alerts in dashboard
3. **Product Status**: Activate/deactivate products as needed
4. **Bulk Updates**: Use CSV import for large inventories (if available)

### Order Management

#### Processing New Orders

1. **Order Notifications**: Receive WhatsApp notifications for new orders
2. **Order Review**: Check order details in **Orders** section
3. **Order Confirmation**: 
   - Review customer details and items
   - Confirm or reject the order
   - Customer receives automatic notification

#### Order Workflow

1. **Pending** → Customer places order
2. **Confirmed** → You confirm the order
3. **Processing** → You prepare the order
4. **Ready for Delivery** → Order is ready for pickup
5. **Out for Delivery** → Assigned to delivery agent
6. **Delivered** → Customer receives order

### Payment Verification

#### Payment Process

1. **Payment Request**: System sends payment instructions to customer
2. **Proof Submission**: Customer uploads payment proof
3. **Verification**: You verify the payment:
   - Check payment details against your records
   - Mark as **Verified** or **Rejected**
   - Add verification notes if needed
4. **Notification**: Customer receives verification result

#### Payment Management Tips

- Check payments promptly to avoid delays
- Use clear rejection reasons to help customers
- Keep payment records for accounting
- Set up multiple payment methods for convenience

### Delivery Management

#### Creating Deliveries

1. **Order Ready**: Mark order as "Ready for Delivery"
2. **Create Delivery**: System creates delivery automatically
3. **Assign Runner**: Choose available delivery agent
4. **Track Progress**: Monitor delivery status in real-time

#### Working with Runners

- **Runner Assignment**: Choose reliable runners for your area
- **Communication**: Runners receive automatic notifications
- **Performance Tracking**: Monitor delivery success rates
- **Payment**: Arrange runner payment terms separately

### Analytics and Reporting

#### Dashboard Overview

Your dashboard shows:
- **Order Statistics**: Total, recent, and status breakdown
- **Revenue Metrics**: Total revenue and recent earnings
- **Payment Status**: Pending and verified payments
- **Product Performance**: Stock levels and alerts
- **Customer Insights**: New and returning customers

#### Sales Analytics

1. **Sales Trends**: View daily/weekly/monthly sales
2. **Top Products**: Identify best-selling items
3. **Customer Analysis**: Understand customer behavior
4. **Performance Metrics**: Track business growth

### Customer Communication

#### WhatsApp Integration

- **Automatic Messages**: System sends order updates automatically
- **Custom Messages**: Send personalized messages to customers
- **Templates**: Create message templates for common responses
- **Bot Responses**: Luma bot handles basic customer inquiries

#### Best Practices

- Respond to customer inquiries promptly
- Use professional, friendly language
- Keep customers updated on order status
- Handle complaints professionally

---

## For Customers (Pulse Users)

### Getting Started with WhatsApp Ordering

#### First Contact

1. **Find the Business**: Get the business WhatsApp number
2. **Send Greeting**: Send "Hi" or "Hello" to start
3. **Bot Response**: Luma bot will welcome you and show options
4. **Browse Menu**: Type "menu" to see available products

#### Placing Your First Order

1. **Browse Products**: Use "menu" command to see what's available
2. **Start Order**: Type "order" for interactive ordering
3. **Select Items**: Follow bot prompts to choose products
4. **Provide Address**: Give your delivery address
5. **Confirm Order**: Review and confirm your order

### Using the WhatsApp Bot (Luma)

#### Available Commands

- **help** - Show all available commands
- **menu** - Browse products and prices
- **order** - Start interactive order process
- **status** - Check your order status
- **track** - Track your delivery
- **payment** - View payment methods
- **cancel** - Cancel your latest order
- **contact** - Get business contact information
- **hours** - Check business hours
- **location** - Get business address
- **feedback** - Share your experience

#### Natural Conversation

You don't need to use exact commands. Luma understands:
- "I want to buy something" → Shows menu
- "Where is my order?" → Shows order status
- "How do I pay?" → Shows payment methods
- "What time do you close?" → Shows business hours

### Payment Process

#### Making Payments

1. **Payment Instructions**: Receive payment details via WhatsApp
2. **Choose Method**: Use bank transfer, mobile money, or other options
3. **Make Payment**: Complete payment using provided details
4. **Upload Proof**: Send payment receipt/screenshot to WhatsApp
5. **Wait for Verification**: Business owner will verify your payment

#### Payment Tips

- Use the exact reference number provided
- Take clear photos of receipts
- Keep payment confirmations for your records
- Contact business if payment verification is delayed

### Order Tracking

#### Tracking Your Order

1. **Order Status**: Type "status" to check current order status
2. **Delivery Tracking**: Type "track" to see delivery progress
3. **Automatic Updates**: Receive notifications at each stage
4. **Delivery Completion**: Get confirmation when order is delivered

#### Order Statuses

- **Pending**: Order received, awaiting confirmation
- **Confirmed**: Business confirmed your order
- **Processing**: Order is being prepared
- **Ready for Delivery**: Order ready for pickup
- **Out for Delivery**: On the way to you
- **Delivered**: Order completed

### Getting Help

#### Customer Support

- **WhatsApp Bot**: Ask Luma for immediate help
- **Business Contact**: Use "contact" command for direct contact
- **Feedback**: Share your experience using "feedback" command
- **Complaints**: Contact business directly for issues

---

## For Delivery Agents (Runner Users)

### Getting Started as a Runner

#### Account Setup

1. **Account Creation**: Business owner creates your account
2. **Profile Completion**: Add your details and vehicle information
3. **Availability**: Set your availability status
4. **Training**: Learn the delivery process and areas

#### Mobile Access

- Use the mobile-responsive dashboard on your phone
- Bookmark the login page for quick access
- Keep your phone charged for GPS tracking
- Ensure good internet connection

### Delivery Process

#### Receiving Assignments

1. **Notification**: Receive WhatsApp notification for new delivery
2. **Assignment Details**: Check pickup and delivery addresses
3. **Accept/Decline**: Confirm if you can handle the delivery
4. **Pickup Instructions**: Get details about order pickup

#### Delivery Workflow

1. **Assignment** → Receive delivery assignment
2. **Pickup** → Collect order from business
3. **In Transit** → On the way to customer
4. **Delivered** → Complete delivery with proof

#### Using the Mobile Dashboard

1. **Login**: Access dashboard on your mobile device
2. **Deliveries**: View assigned deliveries
3. **Status Updates**: Update delivery status in real-time
4. **GPS Tracking**: Share your location during delivery
5. **Proof Upload**: Take photos for delivery confirmation

### Delivery Best Practices

#### Before Pickup

- Confirm delivery details
- Plan your route efficiently
- Check vehicle condition
- Bring necessary equipment (bags, phone charger)

#### During Delivery

- Handle items with care
- Follow GPS directions
- Communicate with customer if needed
- Take clear delivery photos
- Get customer confirmation

#### After Delivery

- Update status immediately
- Upload delivery proof
- Report any issues
- Prepare for next delivery

### Communication

#### With Customers

- Be professional and courteous
- Call if you can't find the address
- Confirm delivery with customer
- Handle complaints calmly

#### With Business

- Report pickup delays
- Communicate delivery issues
- Confirm successful deliveries
- Provide feedback on processes

### Earnings and Performance

#### Tracking Earnings

- View completed deliveries in dashboard
- Track delivery fees earned
- Monitor performance metrics
- Arrange payment with business owner

#### Performance Tips

- Maintain high delivery success rate
- Be punctual for pickups and deliveries
- Provide excellent customer service
- Keep accurate delivery records

---

## For System Administrators (Forge Users)

### System Overview

#### Administrative Responsibilities

- **User Management**: Create and manage all user accounts
- **Business Oversight**: Monitor all businesses on the platform
- **System Configuration**: Configure global settings
- **Performance Monitoring**: Track system-wide metrics
- **Support**: Provide technical support to users

#### Dashboard Access

- **System Dashboard**: View platform-wide statistics
- **User Analytics**: Monitor user activity and engagement
- **Business Performance**: Track business success metrics
- **Technical Metrics**: Monitor system health and performance

### User Management

#### Creating User Accounts

1. **Navigate to Users**: Go to admin panel or user management
2. **Add New User**: Click "Add User" button
3. **User Details**: Fill in required information:
   - Username and password
   - Email and phone number
   - Role assignment (Pod, Pulse, Runner)
   - Profile information
4. **Account Activation**: Activate account and notify user

#### Managing User Roles

- **Role Changes**: Modify user roles as needed
- **Permission Management**: Adjust user permissions
- **Account Status**: Activate/deactivate accounts
- **Password Resets**: Help users with password issues

### Business Management

#### Business Onboarding

1. **Business Registration**: Help new businesses get started
2. **Account Setup**: Create Pod user accounts for business owners
3. **Configuration**: Assist with initial setup
4. **Training**: Provide training on system usage

#### Business Monitoring

- **Performance Tracking**: Monitor business success metrics
- **Issue Resolution**: Help resolve business problems
- **Feature Requests**: Collect and prioritize feature requests
- **Compliance**: Ensure businesses follow platform guidelines

### System Configuration

#### Global Settings

- **WhatsApp Configuration**: Manage Twilio integration
- **Email Settings**: Configure email notifications
- **Security Settings**: Manage security policies
- **Feature Flags**: Enable/disable platform features

#### Monitoring and Maintenance

- **Health Checks**: Monitor system health regularly
- **Performance Optimization**: Optimize system performance
- **Backup Management**: Ensure regular backups
- **Security Updates**: Apply security patches

### Support and Troubleshooting

#### User Support

- **Technical Issues**: Resolve technical problems
- **Training**: Provide user training and guidance
- **Documentation**: Maintain user documentation
- **Feedback**: Collect and act on user feedback

#### System Maintenance

- **Regular Updates**: Apply system updates
- **Database Maintenance**: Maintain database performance
- **Log Analysis**: Analyze system logs for issues
- **Capacity Planning**: Plan for system growth

---

## WhatsApp Bot (Luma) Guide

### Introduction to Luma

Luma is your intelligent WhatsApp assistant that helps customers interact with businesses naturally. It understands multiple languages and can handle complex conversations.

### Bot Capabilities

#### Natural Language Understanding

Luma understands:
- **Multiple Languages**: English, Afrikaans, Zulu greetings
- **Casual Conversation**: "I want to buy something" vs "order"
- **Context Awareness**: Remembers conversation history
- **Intent Recognition**: Understands what customers want

#### Interactive Features

- **Step-by-Step Ordering**: Guides customers through order process
- **Smart Responses**: Provides relevant information based on context
- **Error Handling**: Helps customers when they make mistakes
- **Fallback Options**: Offers alternatives when confused

### Customer Interaction Examples

#### Greeting and Welcome

**Customer**: "Hi"
**Luma**: "Hello! 👋 Welcome to [Business Name]! I'm Luma, your WhatsApp assistant. I can help you browse our menu, place orders, track deliveries, and answer questions. Type 'menu' to see our products or 'help' for more options. How can I assist you today?"

#### Browsing Products

**Customer**: "What do you have?"
**Luma**: "🛍️ **[Business Name] - Menu**

*Product 1*
Price: R50.00
Status: ✅ In Stock
Description: [Product description]

*Product 2*
Price: R75.00
Status: ✅ In Stock
Description: [Product description]

To place an order, type 'order' followed by the product name and quantity.
Example: order Product1 2"

#### Interactive Ordering

**Customer**: "order"
**Luma**: "🛍️ **Start Your Order - [Business Name]**

Available products:

1. **Product 1** - R50.00
   Description here...

2. **Product 2** - R75.00
   Description here...

Reply with the number of the product you want to order."

**Customer**: "1"
**Luma**: "Selected: **Product 1** - R50.00

How many would you like to order?
Please reply with a number (e.g., 1, 2, 3...)"

**Customer**: "2"
**Luma**: "📋 **Order Summary**

Product: Product 1
Quantity: 2
Unit Price: R50.00
Total: R100.00

Please provide your delivery address to complete the order."

#### Order Tracking

**Customer**: "Where is my order?"
**Luma**: "📋 **Your Recent Orders**

✅ **Order #ORD001**
Status: Out for Delivery
Total: R100.00
Date: 2023-12-01 14:30

🚚 Your order is currently being delivered by our delivery agent. You should receive it within the next 30 minutes."

### Bot Configuration for Businesses

#### Customizing Responses

Businesses can customize:
- **Welcome Messages**: Personalized greeting for customers
- **Product Descriptions**: Detailed product information
- **Business Information**: Hours, location, contact details
- **Payment Instructions**: Custom payment guidance

#### Template Messages

Create templates for:
- **Order Confirmations**: Standardized order confirmation messages
- **Payment Requests**: Consistent payment instruction format
- **Delivery Updates**: Professional delivery status updates
- **Customer Service**: Common customer service responses

### Advanced Features

#### Conversation State Management

Luma remembers:
- **Current Conversation**: What the customer is doing
- **Order Progress**: Where they are in the ordering process
- **Previous Interactions**: Past conversations for context
- **Preferences**: Customer preferences and history

#### Smart Keyword Detection

Automatically responds to:
- **Order Keywords**: "buy", "purchase", "want", "need"
- **Payment Keywords**: "pay", "payment", "bank", "money"
- **Delivery Keywords**: "track", "delivery", "where", "location"
- **Info Keywords**: "hours", "time", "address", "contact"

#### Error Recovery

When customers make mistakes:
- **Invalid Selections**: Guides them to correct options
- **Unclear Requests**: Asks for clarification
- **System Errors**: Provides helpful error messages
- **Timeout Handling**: Resumes conversations gracefully

---

## Troubleshooting

### Common Issues and Solutions

#### Login Problems

**Issue**: Can't log into dashboard
**Solutions**:
1. Check username and password spelling
2. Ensure account is activated
3. Try password reset if available
4. Contact system administrator
5. Clear browser cache and cookies

**Issue**: Forgot password
**Solutions**:
1. Use "Forgot Password" link if available
2. Contact business owner or administrator
3. Verify email address is correct
4. Check spam folder for reset emails

#### WhatsApp Integration Issues

**Issue**: Not receiving WhatsApp messages
**Solutions**:
1. Check phone number is correct in profile
2. Ensure WhatsApp is installed and working
3. Check if number is blocked
4. Verify business WhatsApp number
5. Contact system administrator

**Issue**: Bot not responding
**Solutions**:
1. Try sending "help" command
2. Check if you're messaging correct number
3. Restart WhatsApp application
4. Try different command format
5. Contact business directly

#### Order Issues

**Issue**: Order not showing in system
**Solutions**:
1. Check order confirmation message
2. Verify you're logged into correct account
3. Check order status in WhatsApp
4. Contact business owner
5. Provide order number for tracking

**Issue**: Payment verification delayed
**Solutions**:
1. Ensure payment proof is clear
2. Check reference number is correct
3. Contact business owner directly
4. Provide additional payment details
5. Be patient during business hours

#### Delivery Problems

**Issue**: Delivery agent can't find address
**Solutions**:
1. Provide clear, detailed address
2. Include landmarks and directions
3. Share location pin if possible
4. Be available by phone
5. Meet delivery agent at main road if needed

**Issue**: Delivery delayed
**Solutions**:
1. Check delivery tracking status
2. Contact delivery agent directly
3. Consider traffic and weather conditions
4. Contact business if significantly delayed
5. Be flexible with delivery times

### Getting Help

#### Self-Service Options

1. **User Guide**: Read this comprehensive guide
2. **FAQ Section**: Check frequently asked questions
3. **WhatsApp Bot**: Ask Luma for immediate help
4. **Dashboard Help**: Look for help icons in interface

#### Contact Support

1. **Business Owner**: Contact your business directly
2. **WhatsApp**: Message business WhatsApp number
3. **Phone**: Call business phone number
4. **Email**: Send email to business
5. **System Admin**: Contact platform administrator

#### Reporting Issues

When reporting problems, include:
- **User Role**: Your role in the system
- **Issue Description**: Clear description of problem
- **Steps to Reproduce**: What you were doing when issue occurred
- **Screenshots**: Visual evidence of the problem
- **Error Messages**: Exact error messages received
- **Browser/Device**: What device and browser you're using

### Best Practices

#### For All Users

- **Keep Information Updated**: Maintain current profile information
- **Regular Communication**: Stay in touch with relevant parties
- **Follow Processes**: Use established workflows
- **Report Issues**: Report problems promptly
- **Provide Feedback**: Share suggestions for improvement

#### Security Tips

- **Strong Passwords**: Use secure, unique passwords
- **Regular Logout**: Log out when finished
- **Secure Devices**: Keep devices secure and updated
- **Verify Contacts**: Ensure you're communicating with legitimate parties
- **Report Suspicious Activity**: Report any unusual activity immediately

---

## Conclusion

Mthunzi is designed to make WhatsApp-based business operations simple and efficient. This guide covers the essential features and processes for all user types. 

For additional support:
- Contact your business owner or system administrator
- Use the WhatsApp bot for immediate assistance
- Refer to the API documentation for technical details
- Check for system updates and new features regularly

**Welcome to the future of WhatsApp business automation with Mthunzi!** 🚀
