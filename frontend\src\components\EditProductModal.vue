<template>
  <div v-if="isOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
    <div class="bg-[#FFFFFF] rounded-2xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-[#CCCCCC]/30">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-[#1E4E79]/10 rounded-xl flex items-center justify-center">
            <svg class="w-5 h-5 text-[#1E4E79]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
            </svg>
          </div>
          <div>
            <h2 class="text-xl font-bold text-[#111111]">Edit Product</h2>
            <p class="text-sm text-[#4B4B4B]">{{ product?.name }}</p>
          </div>
        </div>
        <button @click="closeModal" class="p-2 hover:bg-[#F5F5F5] rounded-xl transition-colors duration-300">
          <svg class="w-5 h-5 text-[#4B4B4B]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>

      <!-- Form -->
      <form @submit.prevent="handleSubmit" class="p-6 space-y-6">
        <!-- Product Name -->
        <div class="space-y-2">
          <label for="editProductName" class="block text-sm font-medium text-[#111111]">
            Product Name *
          </label>
          <input
            id="editProductName"
            v-model="formData.name"
            type="text"
            placeholder="Enter product name"
            class="w-full px-4 py-3 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] placeholder-[#4B4B4B] focus:bg-[#FFFFFF] focus:ring-2 focus:ring-[#1E4E79]/20 focus:border-[#1E4E79] transition-all duration-300 outline-none"
            required
          />
        </div>

        <!-- Description -->
        <div class="space-y-2">
          <label for="editProductDescription" class="block text-sm font-medium text-[#111111]">
            Description
          </label>
          <textarea
            id="editProductDescription"
            v-model="formData.description"
            rows="3"
            placeholder="Enter product description"
            class="w-full px-4 py-3 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] placeholder-[#4B4B4B] focus:bg-[#FFFFFF] focus:ring-2 focus:ring-[#1E4E79]/20 focus:border-[#1E4E79] transition-all duration-300 outline-none resize-none"
          ></textarea>
        </div>

        <!-- Price and Stock -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <label for="editProductPrice" class="block text-sm font-medium text-[#111111]">
              Price (R) *
            </label>
            <input
              id="editProductPrice"
              v-model.number="formData.price"
              type="number"
              step="0.01"
              min="0"
              placeholder="0.00"
              class="w-full px-4 py-3 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] placeholder-[#4B4B4B] focus:bg-[#FFFFFF] focus:ring-2 focus:ring-[#1E4E79]/20 focus:border-[#1E4E79] transition-all duration-300 outline-none"
              required
            />
          </div>
          <div class="space-y-2">
            <label for="editProductStock" class="block text-sm font-medium text-[#111111]">
              Stock Quantity *
            </label>
            <input
              id="editProductStock"
              v-model.number="formData.stock_quantity"
              type="number"
              min="0"
              placeholder="0"
              class="w-full px-4 py-3 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] placeholder-[#4B4B4B] focus:bg-[#FFFFFF] focus:ring-2 focus:ring-[#1E4E79]/20 focus:border-[#1E4E79] transition-all duration-300 outline-none"
              required
            />
          </div>
        </div>

        <!-- Low Stock Threshold and Category -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <label for="editLowStockThreshold" class="block text-sm font-medium text-[#111111]">
              Low Stock Alert Threshold
            </label>
            <input
              id="editLowStockThreshold"
              v-model.number="formData.low_stock_threshold"
              type="number"
              min="0"
              placeholder="10"
              class="w-full px-4 py-3 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] placeholder-[#4B4B4B] focus:bg-[#FFFFFF] focus:ring-2 focus:ring-[#1E4E79]/20 focus:border-[#1E4E79] transition-all duration-300 outline-none"
            />
          </div>
          <div class="space-y-2">
            <label for="editProductCategory" class="block text-sm font-medium text-[#111111]">
              Category
            </label>
            <select
              id="editProductCategory"
              v-model="formData.category"
              class="w-full px-4 py-3 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] focus:bg-[#FFFFFF] focus:ring-2 focus:ring-[#1E4E79]/20 focus:border-[#1E4E79] transition-all duration-300 outline-none"
            >
              <option value="">Select Category</option>
              <option value="fruits">Fruits</option>
              <option value="vegetables">Vegetables</option>
              <option value="dairy">Dairy</option>
              <option value="bakery">Bakery</option>
              <option value="meat">Meat & Poultry</option>
              <option value="beverages">Beverages</option>
              <option value="snacks">Snacks</option>
              <option value="other">Other</option>
            </select>
          </div>
        </div>

        <!-- Product Status -->
        <div class="space-y-2">
          <label class="block text-sm font-medium text-[#111111]">
            Product Status
          </label>
          <div class="flex space-x-4">
            <label class="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                value="active"
                v-model="formData.status"
                class="w-4 h-4 text-[#2E7D32] bg-[#F5F5F5] border-[#CCCCCC] focus:ring-[#2E7D32] focus:ring-2"
              />
              <span class="text-[#111111]">Active</span>
            </label>
            <label class="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                value="inactive"
                v-model="formData.status"
                class="w-4 h-4 text-[#FF8F00] bg-[#F5F5F5] border-[#CCCCCC] focus:ring-[#FF8F00] focus:ring-2"
              />
              <span class="text-[#111111]">Inactive</span>
            </label>
          </div>
        </div>

        <!-- Current Images Preview -->
        <div v-if="product?.images && product.images.length > 0" class="space-y-2">
          <label class="block text-sm font-medium text-[#111111]">
            Current Images
          </label>
          <div class="grid grid-cols-4 gap-3">
            <div v-for="(image, index) in product.images" :key="index" class="relative group">
              <img :src="image" :alt="`Product image ${index + 1}`" class="w-full h-20 object-cover rounded-lg"/>
              <div v-if="index === 0" class="absolute bottom-1 left-1 bg-[#1E4E79] text-white text-xs px-1 py-0.5 rounded">
                Primary
              </div>
            </div>
          </div>
          <button
            type="button"
            @click="$emit('manage-images')"
            class="text-sm text-[#1E4E79] hover:text-[#132F4C] transition-colors duration-300"
          >
            Manage Images
          </button>
        </div>

        <!-- Error Message -->
        <div v-if="errorMessage" class="p-4 bg-[#C62828]/10 border border-[#C62828]/30 rounded-xl">
          <p class="text-sm text-[#C62828]">{{ errorMessage }}</p>
        </div>

        <!-- Changes Summary -->
        <div v-if="hasChanges" class="bg-gradient-to-br from-[#1E4E79]/5 to-[#C1843E]/5 rounded-xl p-4 border border-[#1E4E79]/20">
          <h3 class="font-semibold text-[#111111] mb-2">Changes Summary</h3>
          <div class="space-y-1 text-sm">
            <div v-for="change in changesSummary" :key="change.field" class="flex justify-between">
              <span class="text-[#4B4B4B]">{{ change.label }}:</span>
              <span class="font-medium text-[#1E4E79]">{{ change.from }} → {{ change.to }}</span>
            </div>
          </div>
        </div>
      </form>

      <!-- Action Buttons -->
      <div class="flex space-x-4 p-6 border-t border-[#CCCCCC]/30 bg-[#F5F5F5]/50">
        <button
          @click="closeModal"
          class="flex-1 px-6 py-3 bg-[#FFFFFF] text-[#4B4B4B] border border-[#CCCCCC]/30 rounded-xl hover:bg-[#F5F5F5] transition-colors duration-300"
        >
          Cancel
        </button>
        <button
          @click="handleSubmit"
          :disabled="!hasChanges || isSubmitting"
          class="flex-1 px-6 py-3 bg-gradient-to-r from-[#1E4E79] to-[#132F4C] text-white rounded-xl hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center"
        >
          <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ isSubmitting ? 'Saving Changes...' : 'Save Changes' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  product: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close', 'product-updated', 'manage-images'])

// Form data
const formData = reactive({
  name: '',
  description: '',
  price: 0,
  stock_quantity: 0,
  low_stock_threshold: 10,
  category: '',
  status: 'active'
})

const originalData = ref({})
const isSubmitting = ref(false)
const errorMessage = ref('')

// Computed properties
const hasChanges = computed(() => {
  if (!props.product) return false
  
  return (
    formData.name !== originalData.value.name ||
    formData.description !== (originalData.value.description || '') ||
    formData.price !== originalData.value.price ||
    formData.stock_quantity !== originalData.value.stock ||
    formData.low_stock_threshold !== (originalData.value.low_stock_threshold || 10) ||
    formData.category !== (originalData.value.category || '') ||
    formData.status !== (originalData.value.status || 'active')
  )
})

const changesSummary = computed(() => {
  if (!props.product) return []
  
  const changes = []
  
  if (formData.name !== originalData.value.name) {
    changes.push({ field: 'name', label: 'Name', from: originalData.value.name, to: formData.name })
  }
  if (formData.price !== originalData.value.price) {
    changes.push({ field: 'price', label: 'Price', from: `R${originalData.value.price}`, to: `R${formData.price}` })
  }
  if (formData.stock_quantity !== originalData.value.stock) {
    changes.push({ field: 'stock', label: 'Stock', from: `${originalData.value.stock} units`, to: `${formData.stock_quantity} units` })
  }
  
  return changes
})

// Methods
const loadProductData = () => {
  if (!props.product) return
  
  const product = props.product
  formData.name = product.name || ''
  formData.description = product.description || ''
  formData.price = product.price || 0
  formData.stock_quantity = product.stock || 0
  formData.low_stock_threshold = product.low_stock_threshold || 10
  formData.category = product.category || ''
  formData.status = product.status || 'active'
  
  // Store original data for comparison
  originalData.value = {
    name: product.name || '',
    description: product.description || '',
    price: product.price || 0,
    stock: product.stock || 0,
    low_stock_threshold: product.low_stock_threshold || 10,
    category: product.category || '',
    status: product.status || 'active'
  }
}

const handleSubmit = async () => {
  if (isSubmitting.value || !hasChanges.value) return
  
  errorMessage.value = ''
  isSubmitting.value = true
  
  try {
    const updatedProduct = {
      id: props.product.id,
      name: formData.name,
      description: formData.description,
      price: formData.price,
      stock_quantity: formData.stock_quantity,
      low_stock_threshold: formData.low_stock_threshold,
      category: formData.category,
      status: formData.status
    }
    
    emit('product-updated', updatedProduct)
    closeModal()
  } catch (error) {
    errorMessage.value = error.message || 'Failed to update product'
  } finally {
    isSubmitting.value = false
  }
}

const closeModal = () => {
  emit('close')
  resetForm()
}

const resetForm = () => {
  errorMessage.value = ''
  isSubmitting.value = false
}

// Watch for product changes
watch(() => props.product, loadProductData, { immediate: true })

// Reset form when modal opens
watch(() => props.isOpen, (newValue) => {
  if (newValue) {
    loadProductData()
  } else {
    resetForm()
  }
})
</script>
