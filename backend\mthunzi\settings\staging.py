"""
Staging settings for mthun<PERSON> project.
"""

from .production import *

# Staging-specific settings
DEBUG = config('DEBUG', default=False, cast=bool)

# Allow more permissive CORS for staging
CORS_ALLOW_ALL_ORIGINS = config('CORS_ALLOW_ALL_ORIGINS', default=False, cast=bool)

# Staging database (can be same as production or separate)
DATABASES = {
    'default': dj_database_url.config(
        default=config('STAGING_DATABASE_URL', default=config('DATABASE_URL')),
        conn_max_age=600,
        conn_health_checks=True,
    )
}

# Staging-specific logging
LOGGING['handlers']['file']['filename'] = '/var/log/mthunzi/staging.log'
LOGGING['loggers']['django']['level'] = 'INFO'
LOGGING['loggers']['mthunzi']['level'] = 'DEBUG'

# Staging email configuration (might use different SMTP)
EMAIL_HOST = config('STAGING_EMAIL_HOST', default=config('EMAIL_HOST'))
EMAIL_HOST_USER = config('STAGING_EMAIL_HOST_USER', default=config('EMAIL_HOST_USER'))
EMAIL_HOST_PASSWORD = config('STAGING_EMAIL_HOST_PASSWORD', default=config('EMAIL_HOST_PASSWORD'))

# Staging WhatsApp configuration
TWILIO_ACCOUNT_SID = config('STAGING_TWILIO_ACCOUNT_SID', default=config('TWILIO_ACCOUNT_SID'))
TWILIO_AUTH_TOKEN = config('STAGING_TWILIO_AUTH_TOKEN', default=config('TWILIO_AUTH_TOKEN'))
WHATSAPP_WEBHOOK_URL = config('STAGING_WHATSAPP_WEBHOOK_URL', default=config('WHATSAPP_WEBHOOK_URL'))

# Sentry configuration for staging
SENTRY_DSN = config('STAGING_SENTRY_DSN', default='')
if SENTRY_DSN:
    import sentry_sdk
    from sentry_sdk.integrations.django import DjangoIntegration
    from sentry_sdk.integrations.celery import CeleryIntegration
    
    sentry_sdk.init(
        dsn=SENTRY_DSN,
        integrations=[
            DjangoIntegration(auto_enabling=True),
            CeleryIntegration(auto_enabling=True),
        ],
        traces_sample_rate=0.5,  # Higher sampling for staging
        send_default_pii=True,
        environment='staging',
    )
