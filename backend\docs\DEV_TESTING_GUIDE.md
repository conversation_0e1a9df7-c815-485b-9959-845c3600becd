# 🧪 Development Testing Guide

## 🚀 System Status: RUNNING ✅

Your Mthunzi development environment is now running successfully!

### 🌐 Access Points

- **Backend API**: http://localhost:8000/
- **Admin Interface**: http://localhost:8000/admin/
- **Health Check**: http://localhost:8000/health/
- **API Documentation**: Available through Django REST framework

### 🔑 Development Login Credentials

#### System Administrator (Forge)
- **Username**: `admin`
- **Password**: `admin123`
- **Role**: Full system access
- **Use for**: System administration, user management

#### Business Owner (Pod)
- **Username**: `business_owner`
- **Password**: `business123`
- **Role**: Business management
- **Use for**: Managing Demo Pizza Palace

#### Customer (Pulse)
- **Username**: `customer`
- **Password**: `customer123`
- **Role**: Customer features
- **Use for**: Testing customer workflows

#### Delivery Agent (Runner)
- **Username**: `delivery_guy`
- **Password**: `runner123`
- **Role**: Delivery management
- **Use for**: Testing delivery workflows

---

## 🧪 Quick API Tests

### 1. Authentication Test ✅
```bash
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

**Expected Response**: User object with authentication token

### 2. Health Check Test ✅
```bash
curl http://localhost:8000/health/
```

**Expected Response**: System health status (may show warnings for WhatsApp/filesystem)

### 3. Business Dashboard Test
```bash
# First get auth token
TOKEN=$(curl -s -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "business_owner", "password": "business123"}' | \
  python -c "import sys, json; print(json.load(sys.stdin)['token'])")

# Then access dashboard
curl -H "Authorization: Token $TOKEN" \
  http://localhost:8000/api/auth/dashboard/business/
```

### 4. Products API Test
```bash
curl -H "Authorization: Token $TOKEN" \
  http://localhost:8000/api/orders/products/
```

### 5. Orders API Test
```bash
curl -H "Authorization: Token $TOKEN" \
  http://localhost:8000/api/orders/orders/
```

---

## 🏢 Sample Business Data

### Demo Pizza Palace
- **Owner**: business_owner
- **Phone**: +***********
- **Email**: <EMAIL>
- **Address**: 123 Main Street, City Center, Cape Town, 8001

### Sample Products
1. **Margherita Pizza** - R85.00
2. **Pepperoni Pizza** - R95.00
3. **Hawaiian Pizza** - R90.00
4. **Chicken Burger** - R65.00
5. **Beef Burger** - R70.00
6. **Caesar Salad** - R45.00
7. **Coca Cola** - R15.00
8. **Orange Juice** - R20.00

### Payment Methods
1. **Bank Transfer** - FNB Account **********
2. **Mobile Money** - +***********

### Delivery Zones
1. **City Center** - R15.00 (30 min)
2. **Northern Suburbs** - R25.00 (45 min)
3. **Southern Suburbs** - R30.00 (60 min)

---

## 🎯 Testing Workflows

### 1. Admin Workflow
1. Login to admin: http://localhost:8000/admin/
2. Use credentials: `admin` / `admin123`
3. Explore:
   - Users management
   - Business management
   - Orders overview
   - System statistics

### 2. Business Owner Workflow
1. Login via API or admin interface
2. Use credentials: `business_owner` / `business123`
3. Test:
   - View business dashboard
   - Manage products
   - Process orders
   - Verify payments

### 3. Customer Workflow (API)
1. Login as customer
2. Browse products
3. Create order
4. Submit payment proof
5. Track order status

### 4. WhatsApp Bot Testing (Manual)
Since WhatsApp integration requires Twilio setup, you can test the bot logic directly:

```python
# In Django shell
python manage.py shell

from whatsapp.bot import process_message
from accounts.models import Business

business = Business.objects.first()
response = process_message("help", "+***********", business)
print(response)

# Test other commands
commands = ["menu", "order", "status", "contact", "hours"]
for cmd in commands:
    response = process_message(cmd, "+***********", business)
    print(f"\n{cmd.upper()}:")
    print(response)
```

---

## 🔧 Development Commands

### Backend Management
```bash
# Activate virtual environment
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows

# Run development server
python manage.py runserver

# Run on different port
python manage.py runserver 8080

# Django shell
python manage.py shell

# Create migrations
python manage.py makemigrations

# Apply migrations
python manage.py migrate

# Run tests
python manage.py test

# Collect static files
python manage.py collectstatic
```

### Database Management
```bash
# Reset database (CAREFUL!)
rm db.sqlite3
python manage.py migrate
python setup_dev.py

# Database shell
python manage.py dbshell

# Create superuser
python manage.py createsuperuser
```

### Frontend Development (Future)
```bash
# Navigate to frontend
cd frontend

# Install dependencies
npm install

# Start development server
npm start

# Run tests
npm test

# Build for production
npm run build
```

---

## 📊 API Endpoints Reference

### Authentication (`/api/auth/`)
- `POST /register/` - User registration
- `POST /login/` - User login
- `POST /logout/` - User logout
- `GET /profile/` - Get user profile
- `PATCH /profile/` - Update profile
- `POST /password/change/` - Change password
- `POST /phone/verify/` - Verify phone number

### Business Management
- `GET /businesses/` - List businesses
- `POST /businesses/` - Create business
- `GET /businesses/{id}/` - Get business details
- `PATCH /businesses/{id}/` - Update business

### Dashboard
- `GET /dashboard/business/` - Business dashboard
- `GET /dashboard/sales/` - Sales analytics
- `GET /dashboard/system/` - System dashboard (Forge only)
- `GET /dashboard/inventory/` - Inventory alerts
- `GET /dashboard/activity/` - User activity

### Orders (`/api/orders/`)
- Products, Orders, Order Items management

### Payments (`/api/payments/`)
- Payment methods, Payment processing

### Deliveries (`/api/deliveries/`)
- Delivery zones, Delivery management

### WhatsApp (`/api/whatsapp/`)
- Message handling, Bot interactions

---

## 🐛 Common Development Issues

### Issue: Server won't start
```bash
# Check if port is in use
netstat -ano | findstr :8000  # Windows
lsof -ti:8000                 # Linux/Mac

# Use different port
python manage.py runserver 8080
```

### Issue: Database errors
```bash
# Reset database
rm db.sqlite3
python manage.py migrate
python setup_dev.py
```

### Issue: Import errors
```bash
# Reinstall dependencies
pip install -r requirements.txt

# Check virtual environment is activated
which python  # Should show venv path
```

### Issue: Permission errors
```bash
# Create media directory
mkdir -p media

# Fix permissions (Linux/Mac)
chmod 755 media
```

---

## 🎯 Next Development Steps

### 1. Frontend Development
```bash
# Set up React frontend
cd frontend
npm install
npm start
```

### 2. WhatsApp Integration
- Set up Twilio account
- Configure webhook URL (use ngrok for local development)
- Test bot interactions

### 3. Testing
```bash
# Run comprehensive tests
python manage.py test

# Test specific apps
python manage.py test accounts
python manage.py test orders
```

### 4. Performance Optimization
- Add database indexes
- Implement caching
- Optimize API queries

---

## 📱 Mobile Testing

### Responsive Design
- Test admin interface on mobile
- Verify API responses on mobile browsers
- Check WhatsApp integration on mobile devices

### Mobile-First Development
- Use mobile-first CSS approach
- Test touch interactions
- Optimize for mobile performance

---

## 🔒 Security Testing

### Authentication
- Test token expiration
- Verify role-based permissions
- Check input validation

### API Security
- Test rate limiting
- Verify CORS settings
- Check for SQL injection vulnerabilities

---

## 📈 Performance Monitoring

### Database Performance
```python
# In Django shell
from django.db import connection
print(connection.queries)  # See executed queries
```

### API Performance
```bash
# Test API response times
time curl http://localhost:8000/api/auth/profile/
```

### Memory Usage
```bash
# Monitor memory usage
ps aux | grep python
```

---

## 🎉 Success Indicators

### ✅ Backend Working
- Server starts without errors
- API endpoints respond correctly
- Authentication works
- Database operations succeed

### ✅ Sample Data Loaded
- Demo business exists
- Sample products available
- Test users created
- Payment methods configured

### ✅ Admin Interface
- Admin login works
- Enhanced interface displays
- User management functional
- Business management operational

### ✅ API Integration
- All endpoints accessible
- Proper error handling
- Authentication required where needed
- Role-based access working

---

## 🚀 Ready for Development!

Your Mthunzi development environment is fully operational. You can now:

1. **Develop new features** using the existing codebase
2. **Test API endpoints** with the provided credentials
3. **Explore the admin interface** for data management
4. **Add WhatsApp integration** when ready
5. **Build the React frontend** for better UX

**Happy coding! 🎯**

---

*For more detailed information, refer to DEVELOPMENT_GUIDE.md and USER_GUIDE.md*
