#!/usr/bin/env python
"""
Development setup script for Mthunzi project.
This script sets up the development environment and creates sample data.
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

def setup_django():
    """Setup Django environment."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mthunzi.settings.development')
    django.setup()

def create_sample_data():
    """Create sample data for development."""
    from django.contrib.auth import get_user_model
    from accounts.models import Business
    from orders.models import Product, Order, OrderItem
    from payments.models import PaymentMethod
    from deliveries.models import DeliveryZone
    
    User = get_user_model()
    
    print("🔧 Creating sample users...")
    
    # Create Forge user (System Admin)
    forge_user, created = User.objects.get_or_create(
        username='admin',
        defaults={
            'email': '<EMAIL>',
            'phone_number': '+***********',
            'role': 'forge',
            'is_staff': True,
            'is_superuser': True,
            'is_verified': True,
            'first_name': 'System',
            'last_name': 'Administrator'
        }
    )
    if created:
        forge_user.set_password('admin123')
        forge_user.save()
        print(f"✅ Created Forge user: {forge_user.username}")
    
    # Create Pod user (Business Owner)
    pod_user, created = User.objects.get_or_create(
        username='business_owner',
        defaults={
            'email': '<EMAIL>',
            'phone_number': '+***********',
            'role': 'pod',
            'is_verified': True,
            'first_name': 'John',
            'last_name': 'Business'
        }
    )
    if created:
        pod_user.set_password('business123')
        pod_user.save()
        print(f"✅ Created Pod user: {pod_user.username}")
    
    # Create Pulse user (Customer)
    pulse_user, created = User.objects.get_or_create(
        username='customer',
        defaults={
            'email': '<EMAIL>',
            'phone_number': '+***********',
            'role': 'pulse',
            'is_verified': True,
            'first_name': 'Jane',
            'last_name': 'Customer'
        }
    )
    if created:
        pulse_user.set_password('customer123')
        pulse_user.save()
        print(f"✅ Created Pulse user: {pulse_user.username}")
    
    # Create Runner user (Delivery Agent)
    runner_user, created = User.objects.get_or_create(
        username='delivery_guy',
        defaults={
            'email': '<EMAIL>',
            'phone_number': '+***********',
            'role': 'runner',
            'is_verified': True,
            'first_name': 'Mike',
            'last_name': 'Runner'
        }
    )
    if created:
        runner_user.set_password('runner123')
        runner_user.save()
        print(f"✅ Created Runner user: {runner_user.username}")
    
    print("\n🏢 Creating sample business...")
    
    # Create sample business
    business, created = Business.objects.get_or_create(
        owner=pod_user,
        defaults={
            'name': 'Demo Pizza Palace',
            'description': 'The best pizza in town! Fresh ingredients, fast delivery.',
            'business_phone': '+***********',
            'business_email': '<EMAIL>',
            'address': '123 Main Street, City Center, Cape Town, 8001',
            'status': 'active',
            'welcome_message': '''Welcome to Demo Pizza Palace! 🍕

We're delighted to serve you through WhatsApp!

🛍️ What we offer:
• Delicious pizzas with fresh ingredients
• Quick delivery to your door
• Great prices and excellent service

📱 How to order:
Type 'menu' to see our products
Type 'help' for assistance

📞 Contact us:
Phone: +***********
Email: <EMAIL>

Thank you for choosing Demo Pizza Palace! 🙏'''
        }
    )
    if created:
        print(f"✅ Created business: {business.name}")
    
    print("\n💳 Creating payment methods...")
    
    # Create payment methods
    bank_method, created = PaymentMethod.objects.get_or_create(
        business=business,
        name='Bank Transfer',
        defaults={
            'method_type': 'bank_transfer',
            'is_active': True,
            'bank_name': 'First National Bank',
            'account_number': '**********',
            'account_name': 'Demo Pizza Palace',
            'instructions': '''Please transfer the exact amount to our bank account:

Bank: First National Bank
Account: **********
Name: Demo Pizza Palace
Branch: 250655

IMPORTANT: Use your ORDER NUMBER as the payment reference!

After payment:
1. Take a screenshot of the confirmation
2. Send it to this WhatsApp number
3. We'll verify and confirm your payment

Payment verification takes 15-30 minutes during business hours.'''
        }
    )
    if created:
        print(f"✅ Created payment method: {bank_method.name}")
    
    mobile_method, created = PaymentMethod.objects.get_or_create(
        business=business,
        name='Mobile Money',
        defaults={
            'method_type': 'mobile_money',
            'is_active': True,
            'mobile_number': '+***********',
            'instructions': '''Send money to our mobile money account:

Number: +***********
Reference: Your ORDER NUMBER

After sending money, please send us a screenshot of the confirmation.'''
        }
    )
    if created:
        print(f"✅ Created payment method: {mobile_method.name}")
    
    print("\n🚚 Creating delivery zones...")
    
    # Create delivery zones
    zones_data = [
        {
            'name': 'City Center',
            'description': 'CBD, Gardens, Tamboerskloof',
            'delivery_fee': 15.00,
            'estimated_delivery_time': 30,
        },
        {
            'name': 'Northern Suburbs',
            'description': 'Bellville, Parow, Goodwood',
            'delivery_fee': 25.00,
            'estimated_delivery_time': 45,
        },
        {
            'name': 'Southern Suburbs',
            'description': 'Claremont, Rondebosch, Wynberg',
            'delivery_fee': 30.00,
            'estimated_delivery_time': 60,
        }
    ]
    
    for zone_data in zones_data:
        zone, created = DeliveryZone.objects.get_or_create(
            business=business,
            name=zone_data['name'],
            defaults=zone_data
        )
        if created:
            print(f"✅ Created delivery zone: {zone.name}")
    
    print("\n🍕 Creating sample products...")
    
    # Create sample products
    products_data = [
        {
            'name': 'Margherita Pizza',
            'description': 'Classic pizza with fresh tomato base, mozzarella cheese, and basil leaves',
            'price': 85.00,
            'category': 'Pizza',
            'stock_quantity': 50,
            'sku': 'PIZZA001',
        },
        {
            'name': 'Pepperoni Pizza',
            'description': 'Delicious pepperoni with mozzarella cheese on tomato base',
            'price': 95.00,
            'category': 'Pizza',
            'stock_quantity': 45,
            'sku': 'PIZZA002',
        },
        {
            'name': 'Hawaiian Pizza',
            'description': 'Ham and pineapple with mozzarella cheese',
            'price': 90.00,
            'category': 'Pizza',
            'stock_quantity': 40,
            'sku': 'PIZZA003',
        },
        {
            'name': 'Chicken Burger',
            'description': 'Grilled chicken breast with lettuce, tomato, and mayo',
            'price': 65.00,
            'category': 'Burgers',
            'stock_quantity': 30,
            'sku': 'BURGER001',
        },
        {
            'name': 'Beef Burger',
            'description': 'Juicy beef patty with cheese, lettuce, and special sauce',
            'price': 70.00,
            'category': 'Burgers',
            'stock_quantity': 25,
            'sku': 'BURGER002',
        },
        {
            'name': 'Caesar Salad',
            'description': 'Fresh romaine lettuce with parmesan, croutons, and caesar dressing',
            'price': 45.00,
            'category': 'Salads',
            'stock_quantity': 20,
            'sku': 'SALAD001',
        },
        {
            'name': 'Coca Cola',
            'description': 'Refreshing Coca Cola 330ml can',
            'price': 15.00,
            'category': 'Beverages',
            'stock_quantity': 100,
            'sku': 'DRINK001',
        },
        {
            'name': 'Orange Juice',
            'description': 'Fresh orange juice 300ml',
            'price': 20.00,
            'category': 'Beverages',
            'stock_quantity': 50,
            'sku': 'DRINK002',
        }
    ]
    
    for product_data in products_data:
        product, created = Product.objects.get_or_create(
            business=business,
            name=product_data['name'],
            defaults=product_data
        )
        if created:
            print(f"✅ Created product: {product.name} - R{product.price}")
    
    print("\n📦 Creating sample order...")
    
    # Create a sample order
    order, created = Order.objects.get_or_create(
        customer=pulse_user,
        business=business,
        defaults={
            'delivery_address': '456 Oak Street, Suburb, Cape Town, 7700',
            'delivery_phone': pulse_user.phone_number,
            'status': 'pending',
            'delivery_notes': 'Please ring the doorbell twice'
        }
    )
    
    if created:
        # Add items to the order
        pizza = Product.objects.get(business=business, name='Margherita Pizza')
        drink = Product.objects.get(business=business, name='Coca Cola')
        
        OrderItem.objects.create(
            order=order,
            product=pizza,
            quantity=2,
            unit_price=pizza.price
        )
        
        OrderItem.objects.create(
            order=order,
            product=drink,
            quantity=2,
            unit_price=drink.price
        )
        
        print(f"✅ Created sample order: {order.order_number}")
    
    print("\n🎉 Sample data creation completed!")
    print("\n📋 Development Login Credentials:")
    print("=" * 50)
    print("🔥 System Admin (Forge):")
    print("   Username: admin")
    print("   Password: admin123")
    print("   Role: System Administrator")
    print()
    print("🏢 Business Owner (Pod):")
    print("   Username: business_owner")
    print("   Password: business123")
    print("   Role: Business Owner")
    print()
    print("💫 Customer (Pulse):")
    print("   Username: customer")
    print("   Password: customer123")
    print("   Role: Customer")
    print()
    print("🏃 Delivery Agent (Runner):")
    print("   Username: delivery_guy")
    print("   Password: runner123")
    print("   Role: Delivery Agent")
    print("=" * 50)

def main():
    """Main setup function."""
    print("🚀 Setting up Mthunzi Development Environment...")
    print("=" * 60)
    
    # Setup Django
    setup_django()
    
    # Run migrations
    print("\n📊 Running database migrations...")
    execute_from_command_line(['manage.py', 'migrate'])
    
    # Create sample data
    print("\n🔧 Creating sample data...")
    create_sample_data()
    
    print("\n✅ Development setup completed!")
    print("\n🌐 Next steps:")
    print("1. Start the development server: python manage.py runserver")
    print("2. Access the admin: http://localhost:8000/admin/")
    print("3. Access the API: http://localhost:8000/api/")
    print("4. Start the frontend: cd frontend && npm start")
    print("\n🎯 Happy coding!")

if __name__ == '__main__':
    main()
