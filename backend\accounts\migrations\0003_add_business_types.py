# Generated by Django 4.2.7 on 2025-07-10 14:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0002_auto_20250708_1619'),
    ]

    operations = [
        migrations.AddField(
            model_name='business',
            name='accepts_preorders',
            field=models.BooleanField(default=True, help_text='Whether the business accepts advance orders'),
        ),
        migrations.AddField(
            model_name='business',
            name='booking_advance_days',
            field=models.PositiveIntegerField(default=30, help_text='How many days in advance customers can book appointments'),
        ),
        migrations.AddField(
            model_name='business',
            name='booking_buffer_minutes',
            field=models.PositiveIntegerField(default=15, help_text='Buffer time between appointments in minutes'),
        ),
        migrations.AddField(
            model_name='business',
            name='business_type',
            field=models.CharField(choices=[('product_goods', 'Product & Goods'), ('service', 'Service Business'), ('food_restaurant', 'Food & Restaurant')], default='product_goods', help_text='Type of business: Product/Goods, Service, or Food/Restaurant', max_length=20),
        ),
        migrations.AddField(
            model_name='business',
            name='daily_menu_enabled',
            field=models.BooleanField(default=False, help_text='Enable daily menu management for food businesses'),
        ),
        migrations.AddField(
            model_name='business',
            name='preparation_time_minutes',
            field=models.PositiveIntegerField(default=30, help_text='Average preparation time for food orders in minutes'),
        ),
        migrations.AddField(
            model_name='business',
            name='service_hours_end',
            field=models.TimeField(default='17:00', help_text='Business closing time for services'),
        ),
        migrations.AddField(
            model_name='business',
            name='service_hours_start',
            field=models.TimeField(default='09:00', help_text='Business opening time for services'),
        ),
    ]
