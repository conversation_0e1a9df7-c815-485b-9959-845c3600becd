<template>
  <div v-if="isOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
    <div class="bg-[#FFFFFF] rounded-2xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-[#CCCCCC]/30">
        <div>
          <h2 class="text-xl font-bold text-[#111111]">Order Details</h2>
          <p class="text-sm text-[#4B4B4B]">Order #{{ order?.id }}</p>
        </div>
        <button @click="closeModal" class="p-2 hover:bg-[#F5F5F5] rounded-xl transition-colors duration-300">
          <svg class="w-5 h-5 text-[#4B4B4B]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>

      <!-- Content -->
      <div class="p-6 space-y-6">
        <!-- Order Status -->
        <div class="flex items-center justify-between p-4 bg-[#F5F5F5] rounded-xl">
          <div>
            <p class="text-sm font-medium text-[#4B4B4B]">Status</p>
            <span :class="getOrderStatusClass(order?.status)" class="inline-block px-3 py-1 rounded-full text-sm font-medium mt-1">
              {{ order?.status }}
            </span>
          </div>
          <div class="text-right">
            <p class="text-sm font-medium text-[#4B4B4B]">Order Date</p>
            <p class="text-[#111111] font-semibold">{{ formatDate(order?.created_at) }}</p>
          </div>
        </div>

        <!-- Customer Information -->
        <div class="bg-[#F5F5F5] rounded-xl p-4">
          <h3 class="font-semibold text-[#111111] mb-3">Customer Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p class="text-sm text-[#4B4B4B]">Name</p>
              <p class="font-medium text-[#111111]">{{ order?.customer_name || order?.customer }}</p>
            </div>
            <div>
              <p class="text-sm text-[#4B4B4B]">Phone</p>
              <p class="font-medium text-[#111111]">{{ order?.customer_phone || 'N/A' }}</p>
            </div>
            <div class="md:col-span-2">
              <p class="text-sm text-[#4B4B4B]">Delivery Address</p>
              <p class="font-medium text-[#111111]">{{ order?.delivery_address || 'N/A' }}</p>
            </div>
          </div>
        </div>

        <!-- Order Items -->
        <div>
          <h3 class="font-semibold text-[#111111] mb-3">Order Items</h3>
          <div class="space-y-3">
            <div v-for="item in order?.order_items || mockOrderItems" :key="item.id" 
                 class="flex items-center justify-between p-3 bg-[#F5F5F5] rounded-xl">
              <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-[#CCCCCC] rounded-lg flex items-center justify-center">
                  <svg class="w-6 h-6 text-[#4B4B4B]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4-8-4m16 0v10l-8 4-8-4V7"/>
                  </svg>
                </div>
                <div>
                  <p class="font-medium text-[#111111]">{{ item.product_name || item.name }}</p>
                  <p class="text-sm text-[#4B4B4B]">R{{ item.price }} each</p>
                </div>
              </div>
              <div class="text-right">
                <p class="font-semibold text-[#111111]">{{ item.quantity }}x</p>
                <p class="text-sm text-[#4B4B4B]">R{{ (item.price * item.quantity).toFixed(2) }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Order Summary -->
        <div class="bg-gradient-to-br from-[#1E4E79]/5 to-[#C1843E]/5 rounded-xl p-4 border border-[#1E4E79]/20">
          <h3 class="font-semibold text-[#111111] mb-3">Order Summary</h3>
          <div class="space-y-2">
            <div class="flex justify-between">
              <span class="text-[#4B4B4B]">Subtotal:</span>
              <span class="font-medium text-[#111111]">R{{ calculateSubtotal() }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-[#4B4B4B]">Delivery Fee:</span>
              <span class="font-medium text-[#111111]">R{{ order?.delivery_fee || 25 }}</span>
            </div>
            <div class="flex justify-between border-t border-[#CCCCCC]/30 pt-2">
              <span class="font-semibold text-[#111111]">Total:</span>
              <span class="font-bold text-[#1E4E79] text-lg">R{{ order?.amount || calculateTotal() }}</span>
            </div>
          </div>
        </div>

        <!-- Notes -->
        <div v-if="order?.notes" class="bg-[#F5F5F5] rounded-xl p-4">
          <h3 class="font-semibold text-[#111111] mb-2">Order Notes</h3>
          <p class="text-[#4B4B4B]">{{ order.notes }}</p>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex space-x-4 p-6 border-t border-[#CCCCCC]/30 bg-[#F5F5F5]/50">
        <button
          @click="closeModal"
          class="flex-1 px-6 py-3 bg-[#FFFFFF] text-[#4B4B4B] border border-[#CCCCCC]/30 rounded-xl hover:bg-[#F5F5F5] transition-colors duration-300"
        >
          Close
        </button>
        
        <button
          v-if="order?.status === 'Pending'"
          @click="$emit('accept-order', order.id)"
          :disabled="isProcessing"
          class="flex-1 px-6 py-3 bg-gradient-to-r from-[#2E7D32] to-[#1B5E20] text-white rounded-xl hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center"
        >
          <svg v-if="isProcessing" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ isProcessing ? 'Accepting...' : 'Accept Order' }}
        </button>
        
        <button
          v-if="order?.status === 'Pending' || order?.status === 'Confirmed'"
          @click="$emit('cancel-order', order.id)"
          :disabled="isProcessing"
          class="flex-1 px-6 py-3 bg-gradient-to-r from-[#C62828] to-[#B71C1C] text-white rounded-xl hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center"
        >
          <svg v-if="isProcessing" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ isProcessing ? 'Cancelling...' : 'Cancel Order' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  order: {
    type: Object,
    default: null
  },
  isProcessing: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'accept-order', 'cancel-order'])

// Mock order items for demo
const mockOrderItems = computed(() => [
  { id: 1, name: 'Fresh Apples', price: 25, quantity: 2 },
  { id: 2, name: 'Organic Bananas', price: 18, quantity: 3 },
  { id: 3, name: 'Premium Bread', price: 35, quantity: 1 }
])

const closeModal = () => {
  emit('close')
}

const getOrderStatusClass = (status) => {
  switch (status) {
    case 'Pending':
      return 'bg-[#FF8F00]/10 text-[#FF8F00]'
    case 'Confirmed':
      return 'bg-[#1E4E79]/10 text-[#1E4E79]'
    case 'Delivered':
      return 'bg-[#2E7D32]/10 text-[#2E7D32]'
    case 'Cancelled':
      return 'bg-[#C62828]/10 text-[#C62828]'
    default:
      return 'bg-[#4B4B4B]/10 text-[#4B4B4B]'
  }
}

const formatDate = (dateString) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const calculateSubtotal = () => {
  const items = props.order?.order_items || mockOrderItems.value
  return items.reduce((total, item) => total + (item.price * item.quantity), 0).toFixed(2)
}

const calculateTotal = () => {
  const subtotal = parseFloat(calculateSubtotal())
  const deliveryFee = props.order?.delivery_fee || 25
  return (subtotal + deliveryFee).toFixed(2)
}
</script>
