import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

const API_BASE_URL = 'http://localhost:8000/api'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref(null)
  const token = ref(localStorage.getItem('authToken'))
  const isLoading = ref(false)
  const error = ref(null)

  // Getters
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const userRole = computed(() => user.value?.role || null)
  const isForge = computed(() => userRole.value === 'forge')
  const isPod = computed(() => userRole.value === 'pod')

  // Helper function to get auth headers
  const getAuthHeaders = () => ({
    'Content-Type': 'application/json',
    'Authorization': token.value ? `Token ${token.value}` : ''
  })

  // Actions
  const login = async (credentials) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await fetch(`${API_BASE_URL}/auth/login/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(credentials)
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || data.error || 'Login failed')
      }

      // Store authentication data
      token.value = data.token
      user.value = data.user
      
      // Persist to localStorage
      localStorage.setItem('authToken', data.token)
      localStorage.setItem('userRole', data.user.role)
      localStorage.setItem('userData', JSON.stringify(data.user))

      return { success: true, user: data.user }
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const register = async (registrationData) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await fetch(`${API_BASE_URL}/auth/register/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(registrationData)
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || data.error || 'Registration failed')
      }

      // Store authentication data
      token.value = data.token
      user.value = data.user
      
      // Persist to localStorage
      localStorage.setItem('authToken', data.token)
      localStorage.setItem('userRole', data.user.role)
      localStorage.setItem('userData', JSON.stringify(data.user))

      return { success: true, user: data.user }
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    isLoading.value = true
    error.value = null

    try {
      // Call logout endpoint if token exists
      if (token.value) {
        await fetch(`${API_BASE_URL}/auth/logout/`, {
          method: 'POST',
          headers: getAuthHeaders()
        })
      }
    } catch (err) {
      console.warn('Logout API call failed:', err)
      // Continue with local logout even if API call fails
    } finally {
      // Clear all authentication data
      token.value = null
      user.value = null
      error.value = null
      
      // Clear localStorage
      localStorage.removeItem('authToken')
      localStorage.removeItem('userRole')
      localStorage.removeItem('userData')
      
      isLoading.value = false
    }
  }

  const checkAuth = async () => {
    // Check if we have stored auth data
    const storedToken = localStorage.getItem('authToken')
    const storedUserData = localStorage.getItem('userData')

    if (!storedToken || !storedUserData) {
      return false
    }

    try {
      // Verify token with backend
      const response = await fetch(`${API_BASE_URL}/auth/profile/`, {
        headers: {
          'Authorization': `Token ${storedToken}`
        }
      })

      if (response.ok) {
        const userData = await response.json()
        token.value = storedToken
        user.value = userData
        return true
      } else {
        // Token is invalid, clear stored data
        await logout()
        return false
      }
    } catch (err) {
      console.error('Auth check failed:', err)
      await logout()
      return false
    }
  }

  const updateProfile = async (profileData) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await fetch(`${API_BASE_URL}/auth/profile/`, {
        method: 'PATCH',
        headers: getAuthHeaders(),
        body: JSON.stringify(profileData)
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || data.error || 'Profile update failed')
      }

      // Update user data
      user.value = { ...user.value, ...data }
      localStorage.setItem('userData', JSON.stringify(user.value))

      return { success: true, user: user.value }
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  // Initialize auth state from localStorage on store creation
  const initializeAuth = () => {
    const storedUserData = localStorage.getItem('userData')
    if (storedUserData && token.value) {
      try {
        user.value = JSON.parse(storedUserData)
      } catch (err) {
        console.error('Failed to parse stored user data:', err)
        logout()
      }
    }
  }

  // Initialize on store creation
  initializeAuth()

  return {
    // State
    user,
    token,
    isLoading,
    error,
    
    // Getters
    isAuthenticated,
    userRole,
    isForge,
    isPod,
    
    // Actions
    login,
    register,
    logout,
    checkAuth,
    updateProfile,
    clearError,
    getAuthHeaders
  }
})
