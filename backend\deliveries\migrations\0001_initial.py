# Generated by Django 5.2.4 on 2025-07-08 14:13

import django.core.validators
import django.db.models.deletion
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
        ('orders', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Delivery',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('delivery_number', models.CharField(max_length=20, unique=True)),
                ('status', models.CharField(choices=[('pending', 'Pending Assignment'), ('assigned', 'Assigned to Runner'), ('picked_up', 'Picked Up'), ('in_transit', 'In Transit'), ('delivered', 'Delivered'), ('failed', 'Delivery Failed'), ('returned', 'Returned to Business')], default='pending', max_length=15)),
                ('pickup_address', models.TextField(help_text='Business pickup address')),
                ('delivery_address', models.TextField(help_text='Customer delivery address')),
                ('pickup_contact', models.CharField(blank=True, max_length=17)),
                ('delivery_contact', models.CharField(blank=True, max_length=17)),
                ('pickup_instructions', models.TextField(blank=True)),
                ('delivery_instructions', models.TextField(blank=True)),
                ('delivery_fee', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('assigned_at', models.DateTimeField(blank=True, null=True)),
                ('picked_up_at', models.DateTimeField(blank=True, null=True)),
                ('delivered_at', models.DateTimeField(blank=True, null=True)),
                ('estimated_delivery_time', models.DateTimeField(blank=True, null=True)),
                ('order', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='delivery', to='orders.order')),
                ('runner', models.ForeignKey(blank=True, limit_choices_to={'role': 'runner'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='deliveries', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Deliveries',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DeliveryProof',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('proof_type', models.CharField(choices=[('photo', 'Photo'), ('signature', 'Signature'), ('receipt', 'Receipt'), ('other', 'Other')], default='photo', max_length=10)),
                ('file', models.FileField(help_text='Upload proof of delivery', upload_to='delivery_proofs/')),
                ('description', models.CharField(blank=True, help_text='Brief description of the proof', max_length=200)),
                ('latitude', models.DecimalField(blank=True, decimal_places=6, max_digits=9, null=True)),
                ('longitude', models.DecimalField(blank=True, decimal_places=6, max_digits=9, null=True)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('delivery', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='proofs', to='deliveries.delivery')),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='uploaded_delivery_proofs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.CreateModel(
            name='DeliveryUpdate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'Pending Assignment'), ('assigned', 'Assigned to Runner'), ('picked_up', 'Picked Up'), ('in_transit', 'In Transit'), ('delivered', 'Delivered'), ('failed', 'Delivery Failed'), ('returned', 'Returned to Business')], max_length=15)),
                ('message', models.TextField(help_text='Update message or notes')),
                ('latitude', models.DecimalField(blank=True, decimal_places=6, max_digits=9, null=True)),
                ('longitude', models.DecimalField(blank=True, decimal_places=6, max_digits=9, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='delivery_updates', to=settings.AUTH_USER_MODEL)),
                ('delivery', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='updates', to='deliveries.delivery')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DeliveryZone',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="Zone name (e.g., 'City Center', 'Suburbs')", max_length=100)),
                ('description', models.TextField(blank=True)),
                ('delivery_fee', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('estimated_delivery_time', models.PositiveIntegerField(help_text='Estimated delivery time in minutes')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='delivery_zones', to='accounts.business')),
            ],
            options={
                'unique_together': {('business', 'name')},
            },
        ),
        migrations.AddField(
            model_name='delivery',
            name='delivery_zone',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='deliveries', to='deliveries.deliveryzone'),
        ),
    ]
