"""
Serializers for the accounts app.
"""

from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from .models import User, Business, UserProfile, SubscriptionPlan, BusinessSubscription, SubscriptionPayment, SubscriptionPaymentProof


class UserRegistrationSerializer(serializers.ModelSerializer):
    """
    Serializer for user registration.
    """
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = (
            'username', 'email', 'password', 'password_confirm',
            'first_name', 'last_name', 'phone_number', 'role'
        )
    
    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match.")
        return attrs
    
    def create(self, validated_data):
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        
        # Create user profile
        UserProfile.objects.create(user=user)
        
        return user


class UserLoginSerializer(serializers.Serializer):
    """
    Serializer for user login.
    """
    username = serializers.CharField()
    password = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        username = attrs.get('username')
        password = attrs.get('password')
        
        if username and password:
            user = authenticate(username=username, password=password)
            if not user:
                raise serializers.ValidationError('Invalid credentials.')
            if not user.is_active:
                raise serializers.ValidationError('User account is disabled.')
            
            attrs['user'] = user
            return attrs
        else:
            raise serializers.ValidationError('Must include username and password.')


class UserProfileSerializer(serializers.ModelSerializer):
    """
    Serializer for user profile.
    """
    
    class Meta:
        model = UserProfile
        fields = (
            'date_of_birth', 'avatar', 'street_address', 'city', 'state',
            'postal_code', 'country', 'preferred_language', 'timezone',
            'vehicle_type', 'license_number', 'is_available'
        )
    
    def validate(self, attrs):
        user = self.context['request'].user
        
        # Only runners should have vehicle and license information
        if user.role != 'runner':
            attrs.pop('vehicle_type', None)
            attrs.pop('license_number', None)
            attrs.pop('is_available', None)
        
        return attrs


class UserSerializer(serializers.ModelSerializer):
    """
    Serializer for user details.
    """
    profile = UserProfileSerializer(read_only=True)
    role_display = serializers.CharField(source='get_role_display', read_only=True)
    
    class Meta:
        model = User
        fields = (
            'id', 'username', 'email', 'first_name', 'last_name',
            'phone_number', 'role', 'role_display', 'whatsapp_id',
            'is_verified', 'is_active', 'date_joined', 'profile'
        )
        read_only_fields = ('id', 'date_joined', 'is_verified', 'whatsapp_id')


class BusinessSerializer(serializers.ModelSerializer):
    """
    Serializer for business details.
    """
    owner = UserSerializer(read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    business_type_display = serializers.CharField(source='get_business_type_display', read_only=True)

    # Business type properties
    is_product_business = serializers.BooleanField(read_only=True)
    is_service_business = serializers.BooleanField(read_only=True)
    is_food_business = serializers.BooleanField(read_only=True)
    requires_inventory_management = serializers.BooleanField(read_only=True)
    supports_appointments = serializers.BooleanField(read_only=True)
    supports_daily_menu = serializers.BooleanField(read_only=True)

    class Meta:
        model = Business
        fields = (
            'id', 'owner', 'name', 'description', 'business_type', 'business_type_display',
            'business_phone', 'business_email', 'address', 'status', 'status_display',
            'whatsapp_business_number', 'welcome_message',
            # Service business settings
            'booking_advance_days', 'booking_buffer_minutes',
            'service_hours_start', 'service_hours_end',
            # Food business settings
            'daily_menu_enabled', 'preparation_time_minutes', 'accepts_preorders',
            # Business type properties
            'is_product_business', 'is_service_business', 'is_food_business',
            'requires_inventory_management', 'supports_appointments', 'supports_daily_menu',
            'created_at', 'updated_at'
        )
        read_only_fields = (
            'id', 'owner', 'created_at', 'updated_at',
            'is_product_business', 'is_service_business', 'is_food_business',
            'requires_inventory_management', 'supports_appointments', 'supports_daily_menu'
        )
    
    def create(self, validated_data):
        # Set the owner to the current user
        validated_data['owner'] = self.context['request'].user
        return super().create(validated_data)


class BusinessCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating a business (simplified).
    """
    
    class Meta:
        model = Business
        fields = (
            'name', 'description', 'business_phone', 'business_email',
            'address', 'whatsapp_business_number', 'welcome_message'
        )
    
    def create(self, validated_data):
        # Set the owner to the current user
        validated_data['owner'] = self.context['request'].user
        return super().create(validated_data)


class PasswordChangeSerializer(serializers.Serializer):
    """
    Serializer for changing password.
    """
    old_password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(write_only=True, validators=[validate_password])
    new_password_confirm = serializers.CharField(write_only=True)
    
    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError('Old password is incorrect.')
        return value
    
    def validate(self, attrs):
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("New passwords don't match.")
        return attrs
    
    def save(self):
        user = self.context['request'].user
        user.set_password(self.validated_data['new_password'])
        user.save()
        return user


class PhoneVerificationSerializer(serializers.Serializer):
    """
    Serializer for phone number verification.
    """
    verification_code = serializers.CharField(max_length=6)
    
    def validate_verification_code(self, value):
        # Here you would implement actual verification logic
        # For now, we'll just check if it's a 6-digit number
        if not value.isdigit() or len(value) != 6:
            raise serializers.ValidationError('Verification code must be 6 digits.')
        return value


class SubscriptionPlanSerializer(serializers.ModelSerializer):
    """
    Serializer for subscription plans.
    """

    class Meta:
        model = SubscriptionPlan
        fields = (
            'id', 'name', 'plan_type', 'description',
            'monthly_price', 'yearly_price',
            'max_products', 'max_orders_per_month',
            'whatsapp_integration', 'analytics_dashboard', 'priority_support',
            'is_active', 'created_at', 'updated_at'
        )


class BusinessSubscriptionSerializer(serializers.ModelSerializer):
    """
    Serializer for business subscriptions.
    """
    business_name = serializers.CharField(source='business.name', read_only=True)
    business_owner = serializers.CharField(source='business.owner.get_full_name', read_only=True)
    business_email = serializers.EmailField(source='business.business_email', read_only=True)
    business_phone = serializers.CharField(source='business.business_phone', read_only=True)
    plan_name = serializers.CharField(source='plan.name', read_only=True)
    plan_type = serializers.CharField(source='plan.plan_type', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    billing_cycle_display = serializers.CharField(source='get_billing_cycle_display', read_only=True)
    is_active = serializers.BooleanField(read_only=True)
    days_remaining = serializers.IntegerField(read_only=True)

    class Meta:
        model = BusinessSubscription
        fields = (
            'id', 'subscription_number', 'business', 'business_name', 'business_owner',
            'business_email', 'business_phone', 'plan', 'plan_name', 'plan_type',
            'status', 'status_display', 'billing_cycle', 'billing_cycle_display',
            'amount', 'start_date', 'end_date', 'next_billing_date',
            'auto_renew', 'is_active', 'days_remaining',
            'created_at', 'updated_at', 'activated_at', 'cancelled_at'
        )
        read_only_fields = (
            'id', 'subscription_number', 'is_active', 'days_remaining',
            'created_at', 'updated_at', 'activated_at', 'cancelled_at'
        )


class SubscriptionPaymentProofSerializer(serializers.ModelSerializer):
    """
    Serializer for subscription payment proofs.
    """

    class Meta:
        model = SubscriptionPaymentProof
        fields = ('id', 'file', 'description', 'uploaded_at')
        read_only_fields = ('id', 'uploaded_at')


class SubscriptionPaymentSerializer(serializers.ModelSerializer):
    """
    Serializer for subscription payments.
    """
    business_name = serializers.CharField(source='subscription.business.name', read_only=True)
    business_owner = serializers.CharField(source='subscription.business.owner.get_full_name', read_only=True)
    plan_name = serializers.CharField(source='subscription.plan.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.get_full_name', read_only=True)
    proofs = SubscriptionPaymentProofSerializer(many=True, read_only=True)

    class Meta:
        model = SubscriptionPayment
        fields = (
            'id', 'payment_reference', 'subscription', 'business_name', 'business_owner',
            'plan_name', 'amount', 'status', 'status_display',
            'payment_method', 'transaction_reference', 'customer_notes',
            'approved_by', 'approved_by_name', 'approval_notes', 'proofs',
            'created_at', 'updated_at', 'submitted_at', 'approved_at'
        )
        read_only_fields = (
            'id', 'payment_reference', 'approved_by', 'approved_by_name',
            'created_at', 'updated_at', 'submitted_at', 'approved_at'
        )


class SubscriptionPaymentApprovalSerializer(serializers.ModelSerializer):
    """
    Serializer for approving subscription payments.
    """

    class Meta:
        model = SubscriptionPayment
        fields = ('status', 'approval_notes')

    def validate_status(self, value):
        if value not in [SubscriptionPayment.PaymentStatus.APPROVED, SubscriptionPayment.PaymentStatus.REJECTED]:
            raise serializers.ValidationError('Status must be either approved or rejected.')
        return value
