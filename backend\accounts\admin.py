from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from django.urls import reverse
from django.db import models
from .models import User, Business, UserProfile


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """Admin configuration for custom User model."""

    list_display = ('username', 'email', 'phone_number', 'role_badge', 'is_verified', 'is_active', 'date_joined')
    list_filter = ('role', 'is_verified', 'is_active', 'is_staff', 'date_joined')
    search_fields = ('username', 'email', 'phone_number', 'first_name', 'last_name')
    ordering = ('-date_joined',)

    fieldsets = BaseUserAdmin.fieldsets + (
        ('Mthunzi Info', {
            'fields': ('role', 'phone_number', 'whatsapp_id', 'is_verified')
        }),
    )

    add_fieldsets = BaseUserAdmin.add_fieldsets + (
        ('Mthunzi Info', {
            'fields': ('role', 'phone_number', 'whatsapp_id', 'is_verified')
        }),
    )

    def role_badge(self, obj):
        """Display role as colored badge."""
        colors = {
            'forge': '#dc2626',  # red
            'pod': '#059669',    # green
            'pulse': '#2563eb',  # blue
            'runner': '#7c3aed', # purple
        }
        color = colors.get(obj.role, '#6b7280')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 2px 8px; '
            'border-radius: 12px; font-size: 11px; font-weight: bold;">{}</span>',
            color, obj.get_role_display()
        )
    role_badge.short_description = 'Role'


@admin.register(Business)
class BusinessAdmin(admin.ModelAdmin):
    """Admin configuration for Business model."""

    list_display = ('name', 'owner_link', 'status_badge', 'whatsapp_business_number', 'created_at')
    list_filter = ('status', 'created_at')
    search_fields = ('name', 'owner__username', 'owner__email', 'business_phone', 'business_email')
    ordering = ('-created_at',)

    fieldsets = (
        ('Basic Information', {
            'fields': ('owner', 'name', 'description', 'status')
        }),
        ('Contact Information', {
            'fields': ('business_phone', 'business_email', 'address')
        }),
        ('WhatsApp Settings', {
            'fields': ('whatsapp_business_number', 'welcome_message')
        }),
    )

    readonly_fields = ('created_at', 'updated_at')

    def owner_link(self, obj):
        """Link to owner's user page."""
        url = reverse('admin:accounts_user_change', args=[obj.owner.pk])
        return format_html('<a href="{}">{}</a>', url, obj.owner.username)
    owner_link.short_description = 'Owner'

    def status_badge(self, obj):
        """Display status as colored badge."""
        colors = {
            'active': '#059669',    # green
            'inactive': '#dc2626',  # red
            'suspended': '#d97706', # orange
        }
        color = colors.get(obj.status, '#6b7280')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 2px 8px; '
            'border-radius: 12px; font-size: 11px; font-weight: bold;">{}</span>',
            color, obj.get_status_display()
        )
    status_badge.short_description = 'Status'


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """Admin configuration for UserProfile model."""

    list_display = ('user', 'city', 'country', 'preferred_language', 'is_available')
    list_filter = ('country', 'preferred_language', 'is_available', 'created_at')
    search_fields = ('user__username', 'user__email', 'city', 'state')
    ordering = ('-created_at',)

    fieldsets = (
        ('User', {
            'fields': ('user',)
        }),
        ('Personal Information', {
            'fields': ('date_of_birth', 'avatar')
        }),
        ('Address', {
            'fields': ('street_address', 'city', 'state', 'postal_code', 'country')
        }),
        ('Preferences', {
            'fields': ('preferred_language', 'timezone')
        }),
        ('Runner Information', {
            'fields': ('vehicle_type', 'license_number', 'is_available'),
            'description': 'Only applicable for Runner role users'
        }),
    )

    def is_available(self, obj):
        return obj.is_available if obj.user.role == 'runner' else 'N/A'
    is_available.short_description = 'Available (Runners only)'


# Customize admin site
admin.site.site_header = "Mthunzi Administration"
admin.site.site_title = "Mthunzi Admin"
admin.site.index_title = "Welcome to Mthunzi Administration"
