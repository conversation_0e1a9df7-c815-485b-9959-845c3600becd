#!/bin/bash

# Mthunzi Restore Script
# This script restores database and media files from backups

set -e

if [ $# -lt 1 ]; then
    echo "Usage: $0 <backup_date> [restore_media]"
    echo "Example: $0 20231201_143000 true"
    exit 1
fi

BACKUP_DATE=$1
RESTORE_MEDIA=${2:-false}
BACKUP_DIR="backups"

echo "🔄 Starting Mthunzi restore..."
echo "Backup date: $BACKUP_DATE"
echo "Restore media: $RESTORE_MEDIA"

# Check if backup files exist
DB_BACKUP_FILE="$BACKUP_DIR/db_backup_$BACKUP_DATE.sql.gz"
MEDIA_BACKUP_FILE="$BACKUP_DIR/media_backup_$BACKUP_DATE.tar.gz"

if [ ! -f "$DB_BACKUP_FILE" ]; then
    echo "❌ Database backup file not found: $DB_BACKUP_FILE"
    exit 1
fi

if [ "$RESTORE_MEDIA" = "true" ] && [ ! -f "$MEDIA_BACKUP_FILE" ]; then
    echo "❌ Media backup file not found: $MEDIA_BACKUP_FILE"
    exit 1
fi

# Confirmation
echo "⚠️ WARNING: This will overwrite the current database and media files!"
read -p "Are you sure you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Restore cancelled"
    exit 1
fi

# Stop services
echo "🛑 Stopping services..."
docker-compose stop web celery celery-beat

# Restore database
echo "🗄️ Restoring database..."
gunzip -c $DB_BACKUP_FILE | docker-compose exec -T db psql -U mthunzi -d mthunzi
echo "✅ Database restored"

# Restore media files
if [ "$RESTORE_MEDIA" = "true" ]; then
    echo "📁 Restoring media files..."
    docker run --rm -v mthunzi_media_volume:/data -v $(pwd)/$BACKUP_DIR:/backup alpine sh -c "cd /data && rm -rf * && tar xzf /backup/media_backup_$BACKUP_DATE.tar.gz"
    echo "✅ Media files restored"
fi

# Start services
echo "🚀 Starting services..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Health check
echo "🏥 Performing health check..."
if curl -f http://localhost/health/ > /dev/null 2>&1; then
    echo "✅ Health check passed!"
else
    echo "❌ Health check failed!"
    docker-compose logs web
    exit 1
fi

echo "🎉 Restore completed successfully!"
