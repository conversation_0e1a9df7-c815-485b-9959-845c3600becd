<template>
  <div v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 overflow-auto">
    <div class="bg-[#FFFFFF] rounded-2xl shadow-xl max-w-md w-full">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-[#CCCCCC]/30">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-[#C1843E]/10 rounded-xl flex items-center justify-center">
            <svg class="w-5 h-5 text-[#C1843E]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M20 7l-8-4-8 4m16 0l-8 4-8-4m16 0v10l-8 4-8-4V7" />
            </svg>
          </div>
          <div>
            <h2 class="text-lg font-bold text-[#111111]">Restock Product</h2>
            <p class="text-sm text-[#4B4B4B]">{{ product?.name }}</p>
          </div>
        </div>
        <button @click="closeModal" class="p-2 hover:bg-[#F5F5F5] rounded-xl transition-colors duration-300">
          <svg class="w-5 h-5 text-[#4B4B4B]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Content -->
      <div class="p-6">
        <!-- Current Stock Info -->
        <div class="bg-[#F5F5F5] rounded-xl p-4 mb-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-[#4B4B4B]">Current Stock</p>
              <p class="text-2xl font-bold text-[#111111]">{{ product?.stock || 0 }} units</p>
            </div>
            <div class="text-right">
              <p class="text-sm text-[#4B4B4B]">Stock Status</p>
              <span :class="getStockStatusClass(product?.stock)"
                class="inline-block px-2 py-1 rounded-full text-xs font-medium">
                {{ getStockStatus(product?.stock) }}
              </span>
            </div>
          </div>
        </div>

        <!-- Restock Form -->
        <div class="space-y-4">
          <!-- Quantity to Add -->
          <div class="space-y-2">
            <label for="restockQuantity" class="block text-sm font-medium text-[#111111]">
              Quantity to Add *
            </label>
            <div class="relative">
              <input id="restockQuantity" v-model.number="restockQuantity" type="number" min="1"
                placeholder="Enter quantity"
                class="w-full px-4 py-3 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] placeholder-[#4B4B4B] focus:bg-[#FFFFFF] focus:ring-2 focus:ring-[#C1843E]/20 focus:border-[#C1843E] transition-all duration-300 outline-none"
                required />
              <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                <span class="text-[#4B4B4B] text-sm">units</span>
              </div>
            </div>
          </div>

          <!-- Quick Add Buttons -->
          <div class="space-y-2">
            <p class="text-sm font-medium text-[#111111]">Quick Add</p>
            <div class="grid grid-cols-4 gap-2">
              <button v-for="amount in [10, 25, 50, 100]" :key="amount" @click="restockQuantity = amount"
                class="px-3 py-2 bg-[#F5F5F5] text-[#4B4B4B] rounded-lg hover:bg-[#C1843E] hover:text-white transition-colors duration-300 text-sm font-medium">
                +{{ amount }}
              </button>
            </div>
          </div>

          <!-- Cost per Unit (Optional) -->
          <div class="space-y-2">
            <label for="costPerUnit" class="block text-sm font-medium text-[#111111]">
              Cost per Unit (Optional)
            </label>
            <div class="relative">
              <input id="costPerUnit" v-model.number="costPerUnit" type="number" step="0.01" min="0" placeholder="0.00"
                class="w-full px-4 py-3 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] placeholder-[#4B4B4B] focus:bg-[#FFFFFF] focus:ring-2 focus:ring-[#C1843E]/20 focus:border-[#C1843E] transition-all duration-300 outline-none pl-8" />
              <div class="absolute inset-y-0 left-0 flex items-center pl-3">
                <span class="text-[#4B4B4B] text-sm">R</span>
              </div>
            </div>
            <p class="text-xs text-[#4B4B4B]">For inventory cost tracking</p>
          </div>

          <!-- Notes -->
          <div class="space-y-2">
            <label for="restockNotes" class="block text-sm font-medium text-[#111111]">
              Notes (Optional)
            </label>
            <textarea id="restockNotes" v-model="notes" rows="3" placeholder="Add any notes about this restock..."
              class="w-full px-4 py-3 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] placeholder-[#4B4B4B] focus:bg-[#FFFFFF] focus:ring-2 focus:ring-[#C1843E]/20 focus:border-[#C1843E] transition-all duration-300 outline-none resize-none"></textarea>
          </div>
        </div>

        <!-- Summary -->
        <div v-if="restockQuantity > 0"
          class="mt-6 bg-gradient-to-br from-[#C1843E]/5 to-[#704A1F]/5 rounded-xl p-4 border border-[#C1843E]/20">
          <h3 class="font-semibold text-[#111111] mb-2">Restock Summary</h3>
          <div class="space-y-1 text-sm">
            <div class="flex justify-between">
              <span class="text-[#4B4B4B]">Current Stock:</span>
              <span class="font-medium text-[#111111]">{{ product?.stock || 0 }} units</span>
            </div>
            <div class="flex justify-between">
              <span class="text-[#4B4B4B]">Adding:</span>
              <span class="font-medium text-[#C1843E]">+{{ restockQuantity }} units</span>
            </div>
            <div class="flex justify-between border-t border-[#CCCCCC]/30 pt-1">
              <span class="font-semibold text-[#111111]">New Stock:</span>
              <span class="font-bold text-[#2E7D32]">{{ (product?.stock || 0) + restockQuantity }} units</span>
            </div>
            <div v-if="costPerUnit > 0" class="flex justify-between">
              <span class="text-[#4B4B4B]">Total Cost:</span>
              <span class="font-medium text-[#111111]">R{{ (costPerUnit * restockQuantity).toFixed(2) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex space-x-4 p-6 border-t border-[#CCCCCC]/30 bg-[#F5F5F5]/50">
        <button @click="closeModal"
          class="flex-1 px-6 py-3 bg-[#FFFFFF] text-[#4B4B4B] border border-[#CCCCCC]/30 rounded-xl hover:bg-[#F5F5F5] transition-colors duration-300">
          Cancel
        </button>
        <button @click="confirmRestock" :disabled="!restockQuantity || restockQuantity <= 0 || isProcessing"
          class="flex-1 px-6 py-3 bg-gradient-to-r from-[#C1843E] to-[#704A1F] text-white rounded-xl hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center">
          <svg v-if="isProcessing" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg"
            fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
            </path>
          </svg>
          {{ isProcessing ? 'Restocking...' : 'Confirm Restock' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  product: {
    type: Object,
    default: null
  },
  isProcessing: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'confirm'])

const restockQuantity = ref(0)
const costPerUnit = ref(0)
const notes = ref('')

const closeModal = () => {
  emit('close')
  resetForm()
}

const confirmRestock = () => {
  emit('confirm', {
    productId: props.product?.id,
    quantity: restockQuantity.value,
    costPerUnit: costPerUnit.value || null,
    notes: notes.value.trim() || null
  })
}

const resetForm = () => {
  restockQuantity.value = 0
  costPerUnit.value = 0
  notes.value = ''
}

const getStockStatus = (stock) => {
  if (stock <= 10) return 'Low Stock'
  if (stock <= 25) return 'Medium'
  return 'In Stock'
}

const getStockStatusClass = (stock) => {
  if (stock <= 10) return 'bg-[#C62828]/10 text-[#C62828]'
  if (stock <= 25) return 'bg-[#FF8F00]/10 text-[#FF8F00]'
  return 'bg-[#2E7D32]/10 text-[#2E7D32]'
}

// Reset form when modal opens
watch(() => props.isOpen, (newValue) => {
  if (!newValue) {
    resetForm()
  }
})
</script>
