"""
Business type validation utilities for the Mthunzi system.
"""

from django.core.exceptions import ValidationError
from rest_framework import serializers


class BusinessTypeValidator:
    """
    Utility class for validating business type compatibility.
    """
    
    @staticmethod
    def validate_product_business(business, operation="manage products"):
        """Validate that business can perform product operations."""
        if not business:
            raise ValidationError("Business is required.")
        
        if business.is_service_business:
            raise ValidationError(
                f"Service businesses cannot {operation}. Use services instead."
            )
    
    @staticmethod
    def validate_service_business(business, operation="manage services"):
        """Validate that business can perform service operations."""
        if not business:
            raise ValidationError("Business is required.")
        
        if not business.is_service_business:
            raise ValidationError(
                f"Only service businesses can {operation}."
            )
    
    @staticmethod
    def validate_food_business(business, operation="manage daily menus"):
        """Validate that business can perform food business operations."""
        if not business:
            raise ValidationError("Business is required.")
        
        if not business.is_food_business:
            raise ValidationError(
                f"Only food/restaurant businesses can {operation}."
            )
    
    @staticmethod
    def validate_inventory_operations(business, operation="manage inventory"):
        """Validate that business requires inventory management."""
        if not business:
            raise ValidationError("Business is required.")
        
        if not business.requires_inventory_management:
            raise ValidationError(
                f"This business type does not support inventory management."
            )
    
    @staticmethod
    def validate_appointment_operations(business, operation="manage appointments"):
        """Validate that business supports appointments."""
        if not business:
            raise ValidationError("Business is required.")
        
        if not business.supports_appointments:
            raise ValidationError(
                f"This business type does not support appointments."
            )


class BusinessTypePermissionMixin:
    """
    Mixin for DRF serializers to validate business type permissions.
    """
    
    def validate_business_type_permission(self, required_type, operation):
        """Validate business type permission in serializer context."""
        request = self.context.get('request')
        if not request or not hasattr(request.user, 'business'):
            raise serializers.ValidationError("Business context is required.")
        
        business = request.user.business
        
        if required_type == 'product':
            BusinessTypeValidator.validate_product_business(business, operation)
        elif required_type == 'service':
            BusinessTypeValidator.validate_service_business(business, operation)
        elif required_type == 'food':
            BusinessTypeValidator.validate_food_business(business, operation)
        elif required_type == 'inventory':
            BusinessTypeValidator.validate_inventory_operations(business, operation)
        elif required_type == 'appointment':
            BusinessTypeValidator.validate_appointment_operations(business, operation)


def validate_business_operation(business, operation_type, operation_name="perform this operation"):
    """
    Standalone function to validate business operations.
    
    Args:
        business: Business instance
        operation_type: Type of operation ('product', 'service', 'food', 'inventory', 'appointment')
        operation_name: Human-readable operation name for error messages
    """
    try:
        if operation_type == 'product':
            BusinessTypeValidator.validate_product_business(business, operation_name)
        elif operation_type == 'service':
            BusinessTypeValidator.validate_service_business(business, operation_name)
        elif operation_type == 'food':
            BusinessTypeValidator.validate_food_business(business, operation_name)
        elif operation_type == 'inventory':
            BusinessTypeValidator.validate_inventory_operations(business, operation_name)
        elif operation_type == 'appointment':
            BusinessTypeValidator.validate_appointment_operations(business, operation_name)
        else:
            raise ValidationError(f"Unknown operation type: {operation_type}")
    except ValidationError as e:
        # Convert Django ValidationError to DRF ValidationError if needed
        if hasattr(serializers, 'ValidationError'):
            raise serializers.ValidationError(str(e))
        raise


# Decorator for view methods
def require_business_type(*allowed_types):
    """
    Decorator to require specific business types for view methods.
    
    Usage:
        @require_business_type('service', 'food')
        def my_view(request):
            # Only service and food businesses can access this view
            pass
    """
    def decorator(func):
        def wrapper(self, request, *args, **kwargs):
            business = getattr(request.user, 'business', None)
            if not business:
                from rest_framework.response import Response
                from rest_framework import status
                return Response({
                    'error': 'Business context is required'
                }, status=status.HTTP_403_FORBIDDEN)
            
            business_type = business.business_type
            allowed = False
            
            for allowed_type in allowed_types:
                if allowed_type == 'product' and business.is_product_business:
                    allowed = True
                    break
                elif allowed_type == 'service' and business.is_service_business:
                    allowed = True
                    break
                elif allowed_type == 'food' and business.is_food_business:
                    allowed = True
                    break
            
            if not allowed:
                from rest_framework.response import Response
                from rest_framework import status
                allowed_names = []
                for t in allowed_types:
                    if t == 'product':
                        allowed_names.append('Product/Goods')
                    elif t == 'service':
                        allowed_names.append('Service')
                    elif t == 'food':
                        allowed_names.append('Food/Restaurant')
                
                return Response({
                    'error': f'This operation is only available for {", ".join(allowed_names)} businesses'
                }, status=status.HTTP_403_FORBIDDEN)
            
            return func(self, request, *args, **kwargs)
        return wrapper
    return decorator
