"""
Views for WhatsApp integration.
"""

import logging
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.db import models
from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response

from .models import WhatsAppMessage, WhatsAppTemplate, WhatsAppWebhook
from .services import whatsapp_service, webhook_processor
from .bot import luma_bot
from .serializers import (
    WhatsAppMessageSerializer, WhatsAppTemplateSerializer,
    SendMessageSerializer
)
from accounts.permissions import IsPod, IsBusinessOwnerOrReadOnly

logger = logging.getLogger(__name__)


@csrf_exempt
@require_http_methods(["POST"])
def whatsapp_webhook(request):
    """
    Webhook endpoint for receiving WhatsApp messages and status updates.
    """
    try:
        # Log the webhook data
        webhook_data = dict(request.POST)

        # Create webhook log entry
        webhook_log = WhatsAppWebhook.objects.create(
            event_type='message',
            raw_data=webhook_data
        )

        # Process the webhook based on type
        if 'MessageSid' in webhook_data and 'Body' in webhook_data:
            # Incoming message
            message = webhook_processor.process_incoming_message(webhook_data)
            if message:
                webhook_log.message = message
                webhook_log.processed = True
                webhook_log.save()

                # Process message with bot
                luma_bot.process_message(message)
        elif 'MessageSid' in webhook_data and 'MessageStatus' in webhook_data:
            # Status update
            success = webhook_processor.process_status_update(webhook_data)
            webhook_log.processed = success
            webhook_log.save()

        return HttpResponse(status=200)

    except Exception as e:
        logger.error(f"Webhook processing error: {str(e)}")
        return HttpResponse(status=500)


class WhatsAppMessageListView(generics.ListAPIView):
    """
    API view for listing WhatsApp messages.
    """
    serializer_class = WhatsAppMessageSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user

        if user.role == 'forge':
            return WhatsAppMessage.objects.all()
        elif user.role == 'pod':
            # Business owners can see messages for their business
            business = getattr(user, 'business', None)
            if business:
                return WhatsAppMessage.objects.filter(business=business)
        elif user.role == 'pulse':
            # Customers can see their own messages
            return WhatsAppMessage.objects.filter(
                models.Q(sender_phone=user.phone_number) |
                models.Q(recipient_phone=user.phone_number)
            )

        return WhatsAppMessage.objects.none()


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated, IsPod])
def send_message(request):
    """
    API endpoint for sending WhatsApp messages.
    """
    serializer = SendMessageSerializer(data=request.data)
    if serializer.is_valid():
        to_number = serializer.validated_data['to_number']
        message = serializer.validated_data['message']
        media_url = serializer.validated_data.get('media_url')

        # Get business
        business = getattr(request.user, 'business', None)

        # Send message
        if media_url:
            whatsapp_message = whatsapp_service.send_media_message(
                to_number, message, media_url, business
            )
        else:
            whatsapp_message = whatsapp_service.send_text_message(
                to_number, message, business
            )

        if whatsapp_message:
            return Response({
                'message': 'Message sent successfully',
                'whatsapp_message': WhatsAppMessageSerializer(whatsapp_message).data
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'error': 'Failed to send message'
            }, status=status.HTTP_400_BAD_REQUEST)

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class WhatsAppTemplateListCreateView(generics.ListCreateAPIView):
    """
    API view for listing and creating WhatsApp templates.
    """
    serializer_class = WhatsAppTemplateSerializer
    permission_classes = [permissions.IsAuthenticated, IsPod]

    def get_queryset(self):
        user = self.request.user
        business = getattr(user, 'business', None)
        if business:
            return WhatsAppTemplate.objects.filter(business=business)
        return WhatsAppTemplate.objects.none()

    def perform_create(self, serializer):
        business = getattr(self.request.user, 'business', None)
        serializer.save(business=business)


class WhatsAppTemplateDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API view for WhatsApp template detail, update, and delete.
    """
    serializer_class = WhatsAppTemplateSerializer
    permission_classes = [permissions.IsAuthenticated, IsBusinessOwnerOrReadOnly]

    def get_queryset(self):
        user = self.request.user
        business = getattr(user, 'business', None)
        if business:
            return WhatsAppTemplate.objects.filter(business=business)
        return WhatsAppTemplate.objects.none()


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated, IsPod])
def send_template_message(request, template_id):
    """
    API endpoint for sending template messages.
    """
    try:
        business = getattr(request.user, 'business', None)
        template = WhatsAppTemplate.objects.get(id=template_id, business=business)

        to_number = request.data.get('to_number')
        context = request.data.get('context', {})

        if not to_number:
            return Response({
                'error': 'to_number is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Send template message
        whatsapp_message = whatsapp_service.send_template_message(
            to_number, template, context, business
        )

        if whatsapp_message:
            return Response({
                'message': 'Template message sent successfully',
                'whatsapp_message': WhatsAppMessageSerializer(whatsapp_message).data
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'error': 'Failed to send template message'
            }, status=status.HTTP_400_BAD_REQUEST)

    except WhatsAppTemplate.DoesNotExist:
        return Response({
            'error': 'Template not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)
