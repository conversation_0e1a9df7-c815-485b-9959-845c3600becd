<template>
  <div v-if="isOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
    <div class="bg-[#FFFFFF] rounded-2xl shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-[#CCCCCC]/30">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-[#1E4E79]/10 rounded-xl flex items-center justify-center">
            <svg class="w-5 h-5 text-[#1E4E79]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
            </svg>
          </div>
          <div>
            <h2 class="text-lg font-bold text-[#111111]">Manage Product Images</h2>
            <p class="text-sm text-[#4B4B4B]">{{ product?.name }}</p>
          </div>
        </div>
        <button @click="closeModal" class="p-2 hover:bg-[#F5F5F5] rounded-xl transition-colors duration-300">
          <svg class="w-5 h-5 text-[#4B4B4B]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>

      <!-- Content -->
      <div class="p-6">
        <!-- Current Images -->
        <div v-if="currentImages.length > 0" class="mb-6">
          <h3 class="text-lg font-semibold text-[#111111] mb-4">Current Images</h3>
          <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            <div v-for="(image, index) in currentImages" :key="index" class="relative group">
              <div class="aspect-square bg-[#F5F5F5] rounded-xl overflow-hidden">
                <img :src="image" :alt="`Product image ${index + 1}`" class="w-full h-full object-cover"/>
              </div>
              <button
                @click="removeCurrentImage(index)"
                class="absolute -top-2 -right-2 w-6 h-6 bg-[#C62828] text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-[#B71C1C]"
              >
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
              </button>
              <div v-if="index === 0" class="absolute bottom-2 left-2 bg-[#1E4E79] text-white text-xs px-2 py-1 rounded-full">
                Primary
              </div>
            </div>
          </div>
        </div>

        <!-- Upload New Images -->
        <div class="mb-6">
          <h3 class="text-lg font-semibold text-[#111111] mb-4">Add New Images</h3>
          
          <!-- Drop Zone -->
          <div 
            @drop="handleDrop"
            @dragover.prevent
            @dragenter.prevent
            :class="[
              'border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300',
              isDragging ? 'border-[#1E4E79] bg-[#1E4E79]/5' : 'border-[#CCCCCC] hover:border-[#1E4E79]'
            ]"
          >
            <input
              ref="fileInput"
              type="file"
              multiple
              accept="image/*"
              @change="handleFileSelect"
              class="hidden"
            />
            
            <div v-if="newImages.length === 0">
              <svg class="w-16 h-16 text-[#4B4B4B] mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
              </svg>
              <p class="text-[#4B4B4B] mb-2 font-medium">Drop images here or click to browse</p>
              <p class="text-sm text-[#4B4B4B] mb-4">PNG, JPG, GIF up to 10MB each</p>
              <button
                @click="$refs.fileInput.click()"
                class="px-6 py-3 bg-[#1E4E79] text-white rounded-xl hover:bg-[#132F4C] transition-colors duration-300"
              >
                Choose Images
              </button>
            </div>
          </div>

          <!-- New Image Previews -->
          <div v-if="newImages.length > 0" class="mt-6">
            <div class="flex items-center justify-between mb-4">
              <h4 class="font-semibold text-[#111111]">New Images ({{ newImages.length }})</h4>
              <button
                @click="$refs.fileInput.click()"
                class="px-4 py-2 bg-[#F5F5F5] text-[#4B4B4B] rounded-lg hover:bg-[#1E4E79] hover:text-white transition-colors duration-300 text-sm"
              >
                Add More
              </button>
            </div>
            
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              <div v-for="(image, index) in newImages" :key="index" class="relative group">
                <div class="aspect-square bg-[#F5F5F5] rounded-xl overflow-hidden border-2 border-[#2E7D32]">
                  <img :src="image.preview" :alt="`New image ${index + 1}`" class="w-full h-full object-cover"/>
                </div>
                <button
                  @click="removeNewImage(index)"
                  class="absolute -top-2 -right-2 w-6 h-6 bg-[#C62828] text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-[#B71C1C]"
                >
                  <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                  </svg>
                </button>
                <div class="absolute bottom-2 left-2 bg-[#2E7D32] text-white text-xs px-2 py-1 rounded-full">
                  New
                </div>
                <div class="absolute top-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded-full">
                  {{ formatFileSize(image.file.size) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Upload Progress -->
        <div v-if="uploadProgress > 0 && uploadProgress < 100" class="mb-6">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-[#111111]">Uploading images...</span>
            <span class="text-sm text-[#4B4B4B]">{{ uploadProgress }}%</span>
          </div>
          <div class="w-full bg-[#F5F5F5] rounded-full h-2">
            <div 
              class="bg-gradient-to-r from-[#1E4E79] to-[#2E7D32] h-2 rounded-full transition-all duration-300"
              :style="{ width: uploadProgress + '%' }"
            ></div>
          </div>
        </div>

        <!-- Error Message -->
        <div v-if="errorMessage" class="mb-6 p-4 bg-[#C62828]/10 border border-[#C62828]/30 rounded-xl">
          <div class="flex items-start space-x-3">
            <svg class="w-5 h-5 text-[#C62828] mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
            </svg>
            <div>
              <p class="text-sm font-medium text-[#C62828]">Upload Error</p>
              <p class="text-sm text-[#C62828]/80">{{ errorMessage }}</p>
            </div>
          </div>
        </div>

        <!-- Tips -->
        <div class="bg-[#1E4E79]/5 border border-[#1E4E79]/20 rounded-xl p-4">
          <h4 class="font-semibold text-[#1E4E79] mb-2">Tips for better images:</h4>
          <ul class="text-sm text-[#1E4E79]/80 space-y-1">
            <li>• Use high-quality images (at least 800x800 pixels)</li>
            <li>• The first image will be used as the primary product image</li>
            <li>• Show your product from different angles</li>
            <li>• Use good lighting and clear backgrounds</li>
            <li>• Maximum file size: 10MB per image</li>
          </ul>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex space-x-4 p-6 border-t border-[#CCCCCC]/30 bg-[#F5F5F5]/50">
        <button
          @click="closeModal"
          class="flex-1 px-6 py-3 bg-[#FFFFFF] text-[#4B4B4B] border border-[#CCCCCC]/30 rounded-xl hover:bg-[#F5F5F5] transition-colors duration-300"
        >
          Cancel
        </button>
        <button
          @click="uploadImages"
          :disabled="newImages.length === 0 || isUploading"
          class="flex-1 px-6 py-3 bg-gradient-to-r from-[#1E4E79] to-[#132F4C] text-white rounded-xl hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center"
        >
          <svg v-if="isUploading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ isUploading ? 'Uploading...' : `Upload ${newImages.length} Image${newImages.length !== 1 ? 's' : ''}` }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  product: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close', 'images-updated'])

const fileInput = ref(null)
const newImages = ref([])
const isDragging = ref(false)
const isUploading = ref(false)
const uploadProgress = ref(0)
const errorMessage = ref('')

const currentImages = computed(() => {
  return props.product?.images || []
})

const handleFileSelect = (event) => {
  const files = Array.from(event.target.files)
  processFiles(files)
  event.target.value = '' // Reset input
}

const handleDrop = (event) => {
  event.preventDefault()
  isDragging.value = false
  const files = Array.from(event.dataTransfer.files)
  processFiles(files)
}

const processFiles = (files) => {
  errorMessage.value = ''
  
  files.forEach(file => {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      errorMessage.value = 'Please select only image files'
      return
    }
    
    // Validate file size (10MB)
    if (file.size > 10 * 1024 * 1024) {
      errorMessage.value = 'File size must be less than 10MB'
      return
    }
    
    const reader = new FileReader()
    reader.onload = (e) => {
      newImages.value.push({
        file: file,
        preview: e.target.result
      })
    }
    reader.readAsDataURL(file)
  })
}

const removeNewImage = (index) => {
  newImages.value.splice(index, 1)
}

const removeCurrentImage = (index) => {
  // In a real app, you'd call an API to delete the image
  console.log('Remove current image at index:', index)
  // For demo, we'll just emit an event
  emit('images-updated', {
    action: 'remove',
    imageIndex: index
  })
}

const uploadImages = async () => {
  if (newImages.value.length === 0) return
  
  isUploading.value = true
  uploadProgress.value = 0
  errorMessage.value = ''
  
  try {
    // Simulate upload progress
    const totalImages = newImages.value.length
    const uploadedImages = []
    
    for (let i = 0; i < totalImages; i++) {
      const image = newImages.value[i]
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // In a real app, you'd upload to your API
      // const result = await inventoryService.uploadProductImage(props.product.id, image.file)
      
      // For demo, create object URL
      const imageUrl = URL.createObjectURL(image.file)
      uploadedImages.push(imageUrl)
      
      uploadProgress.value = Math.round(((i + 1) / totalImages) * 100)
    }
    
    // Emit success event
    emit('images-updated', {
      action: 'add',
      images: uploadedImages
    })
    
    // Reset form
    newImages.value = []
    uploadProgress.value = 0
    
    // Close modal after short delay
    setTimeout(() => {
      closeModal()
    }, 500)
    
  } catch (error) {
    errorMessage.value = error.message || 'Failed to upload images'
    uploadProgress.value = 0
  } finally {
    isUploading.value = false
  }
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const closeModal = () => {
  emit('close')
  resetForm()
}

const resetForm = () => {
  newImages.value = []
  uploadProgress.value = 0
  errorMessage.value = ''
  isDragging.value = false
}

// Reset form when modal opens
watch(() => props.isOpen, (newValue) => {
  if (!newValue) {
    resetForm()
  }
})
</script>
