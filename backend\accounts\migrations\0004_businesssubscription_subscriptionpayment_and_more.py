# Generated by Django 4.2.7 on 2025-07-10 16:24

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0003_add_business_types'),
    ]

    operations = [
        migrations.CreateModel(
            name='BusinessSubscription',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('subscription_number', models.CharField(max_length=20, unique=True)),
                ('status', models.CharField(choices=[('pending', 'Pending Payment'), ('active', 'Active'), ('expired', 'Expired'), ('suspended', 'Suspended'), ('cancelled', 'Cancelled')], default='pending', max_length=10)),
                ('billing_cycle', models.CharField(choices=[('monthly', 'Monthly'), ('yearly', 'Yearly')], default='monthly', max_length=10)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField()),
                ('next_billing_date', models.DateTimeField(blank=True, null=True)),
                ('auto_renew', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('activated_at', models.DateTimeField(blank=True, null=True)),
                ('cancelled_at', models.DateTimeField(blank=True, null=True)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subscriptions', to='accounts.business')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SubscriptionPayment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('payment_reference', models.CharField(max_length=50, unique=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('submitted', 'Proof Submitted'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('refunded', 'Refunded')], default='pending', max_length=10)),
                ('payment_method', models.CharField(blank=True, max_length=100)),
                ('transaction_reference', models.CharField(blank=True, max_length=200)),
                ('customer_notes', models.TextField(blank=True, help_text='Additional notes from business owner about payment')),
                ('approval_notes', models.TextField(blank=True, help_text='Notes from Forge admin during approval')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('submitted_at', models.DateTimeField(blank=True, null=True)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('approved_by', models.ForeignKey(blank=True, limit_choices_to={'role': 'forge'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_subscription_payments', to=settings.AUTH_USER_MODEL)),
                ('subscription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='accounts.businesssubscription')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SubscriptionPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('plan_type', models.CharField(choices=[('basic', 'Basic Plan'), ('premium', 'Premium Plan'), ('enterprise', 'Enterprise Plan')], max_length=20)),
                ('description', models.TextField()),
                ('monthly_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('yearly_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('max_products', models.PositiveIntegerField(blank=True, help_text='Maximum number of products allowed (null = unlimited)', null=True)),
                ('max_orders_per_month', models.PositiveIntegerField(blank=True, help_text='Maximum orders per month (null = unlimited)', null=True)),
                ('whatsapp_integration', models.BooleanField(default=True)),
                ('analytics_dashboard', models.BooleanField(default=True)),
                ('priority_support', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['monthly_price'],
            },
        ),
        migrations.CreateModel(
            name='SubscriptionPaymentProof',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(help_text='Payment proof file (receipt, screenshot, etc.)', upload_to='subscription_payment_proofs/')),
                ('description', models.CharField(blank=True, help_text='Description of the payment proof', max_length=200)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('payment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='proofs', to='accounts.subscriptionpayment')),
            ],
            options={
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.AddField(
            model_name='businesssubscription',
            name='plan',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subscriptions', to='accounts.subscriptionplan'),
        ),
    ]
