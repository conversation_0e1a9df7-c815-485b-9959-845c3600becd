"""
Management command to test WhatsApp integration.
"""

from django.core.management.base import BaseCommand
from whatsapp.services import whatsapp_service


class Command(BaseCommand):
    help = 'Test WhatsApp integration'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--phone',
            type=str,
            help='Phone number to send test message to (with country code, e.g., +27123456789)'
        )
        parser.add_argument(
            '--message',
            type=str,
            default='Hello from Mthunzi! This is a test message.',
            help='Test message to send'
        )
    
    def handle(self, *args, **options):
        phone = options.get('phone')
        message = options.get('message')
        
        if not phone:
            self.stdout.write(
                self.style.ERROR('Please provide a phone number with --phone')
            )
            return
        
        # Check if WhatsApp service is configured
        if not whatsapp_service.is_configured():
            self.stdout.write(
                self.style.ERROR(
                    'WhatsApp service is not configured. Please set TWILIO_ACCOUNT_SID, '
                    'TWILIO_AUTH_TOKEN, and TWILIO_WHATSAPP_NUMBER in your environment.'
                )
            )
            return
        
        self.stdout.write(f'Sending test message to {phone}...')
        
        # Send test message
        result = whatsapp_service.send_text_message(phone, message)
        
        if result:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Message sent successfully! Message ID: {result.whatsapp_message_id}'
                )
            )
        else:
            self.stdout.write(
                self.style.ERROR('Failed to send message. Check your configuration and logs.')
            )
