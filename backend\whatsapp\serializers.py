"""
Serializers for the WhatsApp app.
"""

from rest_framework import serializers
from .models import WhatsAppMessage, WhatsAppTemplate, ChatSession


class WhatsAppMessageSerializer(serializers.ModelSerializer):
    """
    Serializer for WhatsApp messages.
    """
    direction_display = serializers.CharField(source='get_direction_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    message_type_display = serializers.CharField(source='get_message_type_display', read_only=True)
    
    class Meta:
        model = WhatsAppMessage
        fields = (
            'id', 'whatsapp_message_id', 'sender', 'recipient',
            'sender_phone', 'recipient_phone', 'message_type',
            'message_type_display', 'direction', 'direction_display',
            'status', 'status_display', 'content', 'media_url',
            'business', 'order', 'created_at', 'sent_at',
            'delivered_at', 'read_at'
        )
        read_only_fields = (
            'id', 'whatsapp_message_id', 'created_at', 'sent_at',
            'delivered_at', 'read_at'
        )


class WhatsAppTemplateSerializer(serializers.ModelSerializer):
    """
    Serializer for WhatsApp templates.
    """
    category_display = serializers.CharField(source='get_category_display', read_only=True)
    
    class Meta:
        model = WhatsAppTemplate
        fields = (
            'id', 'name', 'category', 'category_display', 'content',
            'whatsapp_template_name', 'language_code', 'is_active',
            'created_at', 'updated_at'
        )
        read_only_fields = ('id', 'created_at', 'updated_at')


class SendMessageSerializer(serializers.Serializer):
    """
    Serializer for sending WhatsApp messages.
    """
    to_number = serializers.CharField(
        max_length=17,
        help_text="Recipient's phone number with country code"
    )
    message = serializers.CharField(
        max_length=1600,
        help_text="Message content"
    )
    media_url = serializers.URLField(
        required=False,
        help_text="URL of media file to send (optional)"
    )
    
    def validate_to_number(self, value):
        """Validate phone number format."""
        # Remove any whitespace
        value = value.strip()
        
        # Check if it starts with + (international format)
        if not value.startswith('+'):
            raise serializers.ValidationError(
                "Phone number must include country code (e.g., +27123456789)"
            )
        
        # Remove + and check if remaining characters are digits
        digits_only = value[1:]
        if not digits_only.isdigit():
            raise serializers.ValidationError(
                "Phone number must contain only digits after country code"
            )
        
        # Check length (minimum 10, maximum 15 digits)
        if len(digits_only) < 10 or len(digits_only) > 15:
            raise serializers.ValidationError(
                "Phone number must be between 10 and 15 digits"
            )
        
        return value


class ChatSessionSerializer(serializers.ModelSerializer):
    """
    Serializer for chat sessions.
    """
    customer_name = serializers.CharField(source='customer.get_full_name', read_only=True)
    business_name = serializers.CharField(source='business.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = ChatSession
        fields = (
            'id', 'customer', 'customer_name', 'business', 'business_name',
            'status', 'status_display', 'order', 'created_at',
            'updated_at', 'closed_at'
        )
        read_only_fields = (
            'id', 'created_at', 'updated_at', 'closed_at'
        )


class TemplateRenderSerializer(serializers.Serializer):
    """
    Serializer for rendering templates with context.
    """
    context = serializers.DictField(
        help_text="Context data for template rendering"
    )
    
    def validate_context(self, value):
        """Validate context data."""
        if not isinstance(value, dict):
            raise serializers.ValidationError("Context must be a dictionary")
        
        # Ensure all values are strings or can be converted to strings
        for key, val in value.items():
            if not isinstance(key, str):
                raise serializers.ValidationError("All context keys must be strings")
            
            # Try to convert value to string
            try:
                str(val)
            except Exception:
                raise serializers.ValidationError(
                    f"Context value for '{key}' cannot be converted to string"
                )
        
        return value


class BulkMessageSerializer(serializers.Serializer):
    """
    Serializer for sending bulk messages.
    """
    phone_numbers = serializers.ListField(
        child=serializers.CharField(max_length=17),
        help_text="List of phone numbers to send message to"
    )
    message = serializers.CharField(
        max_length=1600,
        help_text="Message content"
    )
    template_id = serializers.IntegerField(
        required=False,
        help_text="Template ID to use (optional)"
    )
    context = serializers.DictField(
        required=False,
        help_text="Context data for template rendering (if using template)"
    )
    
    def validate_phone_numbers(self, value):
        """Validate list of phone numbers."""
        if not value:
            raise serializers.ValidationError("At least one phone number is required")
        
        if len(value) > 100:  # Limit bulk messages
            raise serializers.ValidationError("Maximum 100 phone numbers allowed")
        
        # Validate each phone number
        for phone in value:
            if not phone.startswith('+'):
                raise serializers.ValidationError(
                    f"Phone number {phone} must include country code"
                )
            
            digits_only = phone[1:]
            if not digits_only.isdigit() or len(digits_only) < 10 or len(digits_only) > 15:
                raise serializers.ValidationError(
                    f"Invalid phone number format: {phone}"
                )
        
        return value
    
    def validate(self, attrs):
        """Cross-field validation."""
        template_id = attrs.get('template_id')
        context = attrs.get('context')
        
        if template_id and not context:
            raise serializers.ValidationError(
                "Context is required when using a template"
            )
        
        return attrs
