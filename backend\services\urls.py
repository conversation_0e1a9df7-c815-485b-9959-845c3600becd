from django.urls import path
from . import views

app_name = 'services'

urlpatterns = [
    # Service management
    path('services/', views.ServiceListCreateView.as_view(), name='service-list-create'),
    path('services/<int:pk>/', views.ServiceDetailView.as_view(), name='service-detail'),
    
    # Service availability
    path('services/<int:service_id>/availability/', 
         views.ServiceAvailabilityListCreateView.as_view(), 
         name='service-availability'),
    
    # Available time slots
    path('services/<int:service_id>/available-slots/', 
         views.available_time_slots, 
         name='available-time-slots'),
    
    # Appointment management
    path('appointments/', views.AppointmentListCreateView.as_view(), name='appointment-list-create'),
    path('appointments/<uuid:pk>/', views.AppointmentDetailView.as_view(), name='appointment-detail'),
    
    # Appointment actions
    path('appointments/<uuid:appointment_id>/confirm/',
         views.confirm_appointment,
         name='confirm-appointment'),
    path('appointments/<uuid:appointment_id>/cancel/',
         views.cancel_appointment,
         name='cancel-appointment'),
    path('appointments/<uuid:appointment_id>/reschedule/',
         views.reschedule_appointment,
         name='reschedule-appointment'),
]
