# Generated by Django 4.2.7 on 2025-07-10 14:51

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0003_add_business_types'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Service',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('price', models.DecimalField(decimal_places=2, help_text='Service price', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('duration_minutes', models.PositiveIntegerField(help_text='Service duration in minutes')),
                ('category', models.CharField(blank=True, max_length=100)),
                ('image', models.ImageField(blank=True, null=True, upload_to='services/')),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('coming_soon', 'Coming Soon')], default='active', max_length=15)),
                ('requires_deposit', models.BooleanField(default=False, help_text='Whether this service requires a deposit')),
                ('deposit_amount', models.DecimalField(decimal_places=2, default=0, help_text='Deposit amount required', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('buffer_time_minutes', models.PositiveIntegerField(default=0, help_text='Additional buffer time after service completion')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business', models.ForeignKey(limit_choices_to={'business_type': 'service'}, on_delete=django.db.models.deletion.CASCADE, related_name='services', to='accounts.business')),
            ],
            options={
                'ordering': ['name'],
                'unique_together': {('business', 'name')},
            },
        ),
        migrations.CreateModel(
            name='Appointment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('appointment_number', models.CharField(max_length=20, unique=True)),
                ('appointment_datetime', models.DateTimeField(help_text='Scheduled appointment date and time')),
                ('duration_minutes', models.PositiveIntegerField(help_text='Appointment duration in minutes')),
                ('status', models.CharField(choices=[('pending', 'Pending Confirmation'), ('confirmed', 'Confirmed'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('no_show', 'No Show'), ('rescheduled', 'Rescheduled')], default='pending', max_length=20)),
                ('customer_name', models.CharField(blank=True, max_length=200)),
                ('customer_phone', models.CharField(blank=True, max_length=17)),
                ('customer_email', models.EmailField(blank=True, max_length=254)),
                ('customer_notes', models.TextField(blank=True, help_text='Special requests or notes from customer')),
                ('business_notes', models.TextField(blank=True, help_text='Internal notes for the business')),
                ('service_price', models.DecimalField(decimal_places=2, help_text='Price at time of booking', max_digits=10)),
                ('deposit_paid', models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('confirmed_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('cancelled_at', models.DateTimeField(blank=True, null=True)),
                ('whatsapp_message_id', models.CharField(blank=True, max_length=100)),
                ('business', models.ForeignKey(limit_choices_to={'business_type': 'service'}, on_delete=django.db.models.deletion.CASCADE, related_name='appointments', to='accounts.business')),
                ('customer', models.ForeignKey(limit_choices_to={'role': 'pulse'}, on_delete=django.db.models.deletion.CASCADE, related_name='appointments', to=settings.AUTH_USER_MODEL)),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='appointments', to='services.service')),
            ],
            options={
                'ordering': ['-appointment_datetime'],
            },
        ),
        migrations.CreateModel(
            name='ServiceAvailability',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('day_of_week', models.IntegerField(choices=[(1, 'Monday'), (2, 'Tuesday'), (3, 'Wednesday'), (4, 'Thursday'), (5, 'Friday'), (6, 'Saturday'), (7, 'Sunday')], help_text='Day of the week (1=Monday, 7=Sunday)')),
                ('start_time', models.TimeField(help_text='Service start time')),
                ('end_time', models.TimeField(help_text='Service end time')),
                ('is_available', models.BooleanField(default=True, help_text='Whether the service is available on this day')),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='availability_schedule', to='services.service')),
            ],
            options={
                'ordering': ['day_of_week', 'start_time'],
                'unique_together': {('service', 'day_of_week')},
            },
        ),
    ]
