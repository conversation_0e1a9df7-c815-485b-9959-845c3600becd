"""
Views for the payments app.
"""

from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.utils import timezone

from .models import PaymentMethod, Payment, PaymentProof
from .serializers import (
    PaymentMethodSerializer, PaymentMethodCreateSerializer,
    PaymentSerializer, PaymentCreateSerializer, PaymentVerificationSerializer,
    PaymentProofSerializer, PaymentProofUploadSerializer,
    PaymentSummarySerializer, PaymentStatusUpdateSerializer
)
from orders.models import Order
from accounts.permissions import (
    IsPod, IsPulse, IsCustomerOrBusinessOwner,
    IsBusinessOwnerOrReadOnly
)
from whatsapp.services import whatsapp_service


class PaymentMethodListCreateView(generics.ListCreateAPIView):
    """
    API view for listing and creating payment methods.
    """
    permission_classes = [permissions.IsAuthenticated, IsPod]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return PaymentMethodCreateSerializer
        return PaymentMethodSerializer

    def get_queryset(self):
        user = self.request.user
        business = getattr(user, 'business', None)
        if business:
            return PaymentMethod.objects.filter(business=business)
        return PaymentMethod.objects.none()


class PaymentMethodDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API view for payment method detail, update, and delete.
    """
    serializer_class = PaymentMethodSerializer
    permission_classes = [permissions.IsAuthenticated, IsBusinessOwnerOrReadOnly]

    def get_queryset(self):
        user = self.request.user
        business = getattr(user, 'business', None)
        if business:
            return PaymentMethod.objects.filter(business=business)
        return PaymentMethod.objects.none()


class PaymentListView(generics.ListAPIView):
    """
    API view for listing payments.
    """
    serializer_class = PaymentSummarySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user

        if user.role == 'forge':
            return Payment.objects.all()
        elif user.role == 'pod':
            # Business owners can see payments for their business
            business = getattr(user, 'business', None)
            if business:
                return Payment.objects.filter(order__business=business)
        elif user.role == 'pulse':
            # Customers can see their own payments
            return Payment.objects.filter(order__customer=user)

        return Payment.objects.none()


class PaymentDetailView(generics.RetrieveAPIView):
    """
    API view for payment detail.
    """
    serializer_class = PaymentSerializer
    permission_classes = [permissions.IsAuthenticated, IsCustomerOrBusinessOwner]

    def get_queryset(self):
        user = self.request.user

        if user.role == 'forge':
            return Payment.objects.all()
        elif user.role == 'pod':
            business = getattr(user, 'business', None)
            if business:
                return Payment.objects.filter(order__business=business)
        elif user.role == 'pulse':
            return Payment.objects.filter(order__customer=user)

        return Payment.objects.none()


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated, IsPulse])
def create_payment(request, order_id):
    """
    API endpoint for creating a payment for an order.
    """
    try:
        order = get_object_or_404(Order, id=order_id, customer=request.user)

        # Check if payment already exists
        if hasattr(order, 'payment'):
            return Response({
                'error': 'Payment already exists for this order'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check order status
        if order.status not in ['confirmed', 'processing']:
            return Response({
                'error': 'Payment can only be created for confirmed or processing orders'
            }, status=status.HTTP_400_BAD_REQUEST)

        serializer = PaymentCreateSerializer(
            data=request.data,
            context={'request': request, 'order': order}
        )

        if serializer.is_valid():
            payment = serializer.save()

            # Send payment instructions to customer
            _send_payment_instructions(payment)

            return Response({
                'message': 'Payment created successfully',
                'payment': PaymentSerializer(payment, context={'request': request}).data
            }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    except Order.DoesNotExist:
        return Response({
            'error': 'Order not found'
        }, status=status.HTTP_404_NOT_FOUND)


def _send_payment_instructions(payment):
    """Send payment instructions to customer via WhatsApp."""
    try:
        method = payment.payment_method

        message = f"""
💳 *Payment Instructions*

Order: {payment.order.order_number}
Amount: R{payment.amount}
Payment Reference: {payment.payment_reference}

*{method.name}*
{method.instructions}

"""

        if method.method_type == 'bank_transfer':
            if method.account_name:
                message += f"Account Name: {method.account_name}\n"
            if method.account_number:
                message += f"Account Number: {method.account_number}\n"
            if method.bank_name:
                message += f"Bank: {method.bank_name}\n"
        elif method.method_type == 'mobile_money':
            if method.mobile_number:
                message += f"Mobile Number: {method.mobile_number}\n"

        message += f"""

Please use reference: {payment.payment_reference}

After payment, upload your proof of payment by replying with the receipt/screenshot.
        """.strip()

        whatsapp_service.send_text_message(
            payment.order.customer.phone_number,
            message,
            payment.order.business
        )

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to send payment instructions: {str(e)}")


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def upload_payment_proof(request, payment_id):
    """
    API endpoint for uploading payment proof.
    """
    try:
        payment = get_object_or_404(Payment, id=payment_id)

        # Check permissions
        user = request.user
        if user.role == 'pulse' and payment.order.customer != user:
            return Response({
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)
        elif user.role == 'pod' and payment.order.business.owner != user:
            return Response({
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        # Check payment status
        if payment.status not in ['pending', 'rejected']:
            return Response({
                'error': 'Proof can only be uploaded for pending or rejected payments'
            }, status=status.HTTP_400_BAD_REQUEST)

        serializer = PaymentProofUploadSerializer(
            data=request.data,
            context={'request': request, 'payment': payment}
        )

        if serializer.is_valid():
            proof = serializer.save()

            # Update payment status to submitted
            payment.status = 'submitted'
            payment.submitted_at = timezone.now()
            payment.save()

            # Notify business owner
            _notify_business_owner(payment)

            return Response({
                'message': 'Payment proof uploaded successfully',
                'proof': PaymentProofSerializer(proof, context={'request': request}).data
            }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    except Payment.DoesNotExist:
        return Response({
            'error': 'Payment not found'
        }, status=status.HTTP_404_NOT_FOUND)


def _notify_business_owner(payment):
    """Notify business owner about payment proof submission."""
    try:
        message = f"""
📄 *New Payment Proof Submitted*

Order: {payment.order.order_number}
Customer: {payment.order.customer.get_full_name()}
Amount: R{payment.amount}
Reference: {payment.payment_reference}

Please review and verify the payment in your dashboard.
        """.strip()

        whatsapp_service.send_text_message(
            payment.order.business.owner.phone_number,
            message,
            payment.order.business
        )

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to notify business owner: {str(e)}")


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated, IsPod])
def verify_payment(request, payment_id):
    """
    API endpoint for verifying payments (business owners only).
    """
    try:
        business = getattr(request.user, 'business', None)
        payment = get_object_or_404(
            Payment,
            id=payment_id,
            order__business=business
        )

        serializer = PaymentVerificationSerializer(
            payment,
            data=request.data,
            context={'request': request}
        )

        if serializer.is_valid():
            payment = serializer.save()

            # Send verification result to customer
            _send_verification_result(payment)

            return Response({
                'message': 'Payment verification completed',
                'payment': PaymentSerializer(payment, context={'request': request}).data
            }, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    except Payment.DoesNotExist:
        return Response({
            'error': 'Payment not found'
        }, status=status.HTTP_404_NOT_FOUND)


def _send_verification_result(payment):
    """Send payment verification result to customer."""
    try:
        if payment.status == 'verified':
            message = f"""
✅ *Payment Verified*

Order: {payment.order.order_number}
Amount: R{payment.amount}

Your payment has been verified! Your order will now be processed.

Thank you for your business!
            """.strip()
        else:  # rejected
            message = f"""
❌ *Payment Rejected*

Order: {payment.order.order_number}
Amount: R{payment.amount}

Your payment proof was rejected.
Reason: {payment.verification_notes or 'Please contact us for details.'}

Please submit a new proof of payment or contact us for assistance.
            """.strip()

        whatsapp_service.send_text_message(
            payment.order.customer.phone_number,
            message,
            payment.order.business
        )

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to send verification result: {str(e)}")
