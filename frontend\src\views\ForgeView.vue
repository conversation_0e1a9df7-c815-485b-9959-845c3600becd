<template>
  <div class="min-h-screen bg-gradient-to-br from-[#EDF6FF] via-[#F5F5F5] to-[#FFF1DB]">
    <!-- Header -->
    <header class="bg-[#FFFFFF] shadow-sm border-b border-[#CCCCCC]/30">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo & Title -->
          <div class="flex items-center space-x-4">
            <div
              class="w-10 h-10 bg-gradient-to-br from-[#1E4E79] to-[#132F4C] rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
              </svg>
            </div>
            <div>
              <h1 class="text-xl font-bold text-[#111111]">Forge Dashboard</h1>
              <p class="text-sm text-[#4B4B4B]">System Management</p>
            </div>
          </div>

          <!-- User Menu -->
          <div class="flex items-center space-x-4">
            <!-- Notifications -->
            <button class="p-2 text-[#4B4B4B] hover:text-[#1E4E79] transition-colors duration-300">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M15 17h5l-5 5-5-5h5v-5a7.5 7.5 0 01-7.5-7.5H2.5" />
              </svg>
            </button>

            <!-- User Dropdown -->
            <div class="relative" ref="userMenuRef">
              <button @click="toggleUserMenu"
                class="flex items-center space-x-2 p-2 rounded-lg hover:bg-[#F5F5F5] transition-colors duration-300">
                <div
                  class="w-8 h-8 bg-gradient-to-br from-[#C1843E] to-[#704A1F] rounded-full flex items-center justify-center">
                  <span class="text-white text-sm font-semibold">{{ userInitials }}</span>
                </div>
                <svg class="w-4 h-4 text-[#4B4B4B]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              <!-- Dropdown Menu -->
              <div v-show="showUserMenu"
                class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-[#CCCCCC]/30 py-2 z-50">
                <div class="px-4 py-2 border-b border-[#CCCCCC]/30">
                  <p class="text-sm font-medium text-[#111111]">{{ userName }}</p>
                  <p class="text-xs text-[#4B4B4B]">Forge Admin</p>
                </div>

                <button @click="handleLogout"
                  class="w-full text-left px-4 py-2 text-sm text-[#C62828] hover:bg-[#C62828]/5 transition-colors duration-200 flex items-center space-x-2">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                  <span>Logout</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Navigation Tabs -->
      <div class="mb-8">
        <nav class="flex space-x-8" aria-label="Tabs">
          <button v-for="tab in tabs" :key="tab.id" @click="activeTab = tab.id" :class="[
            activeTab === tab.id
              ? 'border-[#1E4E79] text-[#1E4E79]'
              : 'border-transparent text-[#4B4B4B] hover:text-[#111111] hover:border-[#CCCCCC]',
            'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200'
          ]">
            {{ tab.name }}
          </button>
        </nav>
      </div>

      <!-- Dashboard Tab -->
      <div v-if="activeTab === 'dashboard'">
        <!-- Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div
            class="bg-[#FFFFFF] rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30 hover:shadow-md transition-all duration-300">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-[#4B4B4B]">Total Pods</p>
                <p class="text-3xl font-bold text-[#111111]">{{ stats.totalPods }}</p>
              </div>
              <div class="w-12 h-12 bg-[#1E4E79]/10 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-[#1E4E79]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
            </div>
          </div>

          <div
            class="bg-[#FFFFFF] rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30 hover:shadow-md transition-all duration-300">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-[#4B4B4B]">Active Orders</p>
                <p class="text-3xl font-bold text-[#111111]">{{ stats.activeOrders }}</p>
              </div>
              <div class="w-12 h-12 bg-[#2E7D32]/10 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-[#2E7D32]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
            </div>
          </div>

          <div
            class="bg-[#FFFFFF] rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30 hover:shadow-md transition-all duration-300">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-[#4B4B4B]">Total Revenue</p>
                <p class="text-3xl font-bold text-[#111111]">R{{ stats.totalRevenue.toLocaleString() }}</p>
              </div>
              <div class="w-12 h-12 bg-[#C1843E]/10 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-[#C1843E]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
            </div>
          </div>

          <div
            class="bg-[#FFFFFF] rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30 hover:shadow-md transition-all duration-300">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-[#4B4B4B]">System Health</p>
                <p class="text-3xl font-bold text-[#2E7D32]">{{ stats.systemHealth }}%</p>
              </div>
              <div class="w-12 h-12 bg-[#2E7D32]/10 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-[#2E7D32]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="mb-8">
          <h2 class="text-lg font-semibold text-[#111111] mb-4">Quick Actions</h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              class="bg-[#FFFFFF] rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30 hover:shadow-md transition-all duration-300 text-left group">
              <div class="flex items-center space-x-4">
                <div
                  class="w-12 h-12 bg-[#1E4E79]/10 rounded-xl flex items-center justify-center group-hover:bg-[#1E4E79] transition-colors duration-300">
                  <svg class="w-6 h-6 text-[#1E4E79] group-hover:text-white transition-colors duration-300" fill="none"
                    stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </div>
                <div>
                  <h3 class="font-semibold text-[#111111]">Add New Pod</h3>
                  <p class="text-sm text-[#4B4B4B]">Register a new business</p>
                </div>
              </div>
            </button>

            <button
              class="bg-[#FFFFFF] rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30 hover:shadow-md transition-all duration-300 text-left group">
              <div class="flex items-center space-x-4">
                <div
                  class="w-12 h-12 bg-[#C1843E]/10 rounded-xl flex items-center justify-center group-hover:bg-[#C1843E] transition-colors duration-300">
                  <svg class="w-6 h-6 text-[#C1843E] group-hover:text-white transition-colors duration-300" fill="none"
                    stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div>
                  <h3 class="font-semibold text-[#111111]">View Analytics</h3>
                  <p class="text-sm text-[#4B4B4B]">System-wide reports</p>
                </div>
              </div>
            </button>

            <button
              class="bg-[#FFFFFF] rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30 hover:shadow-md transition-all duration-300 text-left group">
              <div class="flex items-center space-x-4">
                <div
                  class="w-12 h-12 bg-[#132F4C]/10 rounded-xl flex items-center justify-center group-hover:bg-[#132F4C] transition-colors duration-300">
                  <svg class="w-6 h-6 text-[#132F4C] group-hover:text-white transition-colors duration-300" fill="none"
                    stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <div>
                  <h3 class="font-semibold text-[#111111]">System Settings</h3>
                  <p class="text-sm text-[#4B4B4B]">Configure platform</p>
                </div>
              </div>
            </button>
          </div>
        </div>

        <!-- Pods Management -->
        <div class="bg-[#FFFFFF] rounded-2xl shadow-sm border border-[#CCCCCC]/30">
          <div class="p-6 border-b border-[#CCCCCC]/30">
            <div class="flex justify-between items-center">
              <h2 class="text-lg font-semibold text-[#111111]">Registered Pods</h2>
              <button
                class="bg-gradient-to-r from-[#1E4E79] to-[#132F4C] text-white px-4 py-2 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                Add Pod
              </button>
            </div>
          </div>

          <div class="p-6">
            <div class="overflow-x-auto">
              <table class="w-full">
                <thead>
                  <tr class="border-b border-[#CCCCCC]/30">
                    <th class="text-left py-3 px-4 font-semibold text-[#111111]">Business Name</th>
                    <th class="text-left py-3 px-4 font-semibold text-[#111111]">Owner</th>
                    <th class="text-left py-3 px-4 font-semibold text-[#111111]">Status</th>
                    <th class="text-left py-3 px-4 font-semibold text-[#111111]">Orders</th>
                    <th class="text-left py-3 px-4 font-semibold text-[#111111]">Revenue</th>
                    <th class="text-left py-3 px-4 font-semibold text-[#111111]">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="business in businesses" :key="business.id"
                    class="border-b border-[#CCCCCC]/20 hover:bg-[#F5F5F5] transition-colors duration-200">
                    <td class="py-4 px-4">
                      <div class="flex items-center space-x-3">
                        <div
                          class="w-10 h-10 bg-gradient-to-br from-[#C1843E] to-[#704A1F] rounded-xl flex items-center justify-center">
                          <span class="text-white font-semibold text-sm">{{ business.name?.charAt(0) || 'B' }}</span>
                        </div>
                        <div>
                          <p class="font-medium text-[#111111]">{{ business.name }}</p>
                          <p class="text-sm text-[#4B4B4B]">{{ business.business_type_display }}</p>
                        </div>
                      </div>
                    </td>
                    <td class="py-4 px-4 text-[#111111]">{{ business.owner_name || 'N/A' }}</td>
                    <td class="py-4 px-4">
                      <span :class="getStatusClass(business.status)" class="px-3 py-1 rounded-full text-xs font-medium">
                        {{ business.status_display }}
                      </span>
                    </td>
                    <td class="py-4 px-4 text-[#111111]">{{ business.total_orders || 0 }}</td>
                    <td class="py-4 px-4 text-[#111111]">R{{ (business.total_revenue || 0).toLocaleString() }}</td>
                    <td class="py-4 px-4">
                      <div class="flex space-x-2">
                        <button @click="openBusinessModal(business, 'view')"
                          class="p-2 text-[#1E4E79] hover:bg-[#1E4E79]/10 rounded-lg transition-colors duration-200"
                          title="View Details">
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        </button>
                        <button @click="openBusinessModal(business, 'edit')"
                          class="p-2 text-[#C1843E] hover:bg-[#C1843E]/10 rounded-lg transition-colors duration-200"
                          title="Edit Business">
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                        <button @click="openBusinessModal(business, 'action')"
                          class="p-2 text-[#4B4B4B] hover:bg-[#4B4B4B]/10 rounded-lg transition-colors duration-200"
                          title="Manage Status">
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="isLoading" class="flex justify-center items-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1E4E79]"></div>
          <span class="ml-2 text-[#4B4B4B]">Loading businesses...</span>
        </div>

        <!-- Empty State -->
        <div v-else-if="businesses.length === 0" class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-[#CCCCCC]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-[#111111]">No businesses found</h3>
          <p class="mt-1 text-sm text-[#4B4B4B]">Get started by registering your first business.</p>
        </div>
      </div>

      <!-- Subscription Reports Tab -->
      <div v-if="activeTab === 'reports'">
        <SubscriptionReports />
      </div>

      <!-- Subscription Approvals Tab -->
      <div v-if="activeTab === 'approvals'">
        <SubscriptionApproval />
      </div>
    </main>

    <!-- Business Modal -->
    <BusinessModal :is-open="businessModal.isOpen" :business="businessModal.business" :mode="businessModal.mode"
      @close="closeBusinessModal" @save="handleBusinessSave" @status-change="handleBusinessStatusChange" />

    <!-- Notification Toast -->
    <NotificationToast ref="notificationToast" />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import BusinessModal from '@/components/BusinessModal.vue'
import SubscriptionReports from '@/components/SubscriptionReports.vue'
import SubscriptionApproval from '@/components/SubscriptionApproval.vue'
import NotificationToast from '@/components/NotificationToast.vue'
import { businessService, reportsService } from '@/services/forgeApi'

// Composables
const router = useRouter()
const authStore = useAuthStore()

// User menu state
const showUserMenu = ref(false)
const userMenuRef = ref(null)

// User info computed properties
const userName = computed(() => {
  return authStore.user?.first_name && authStore.user?.last_name
    ? `${authStore.user.first_name} ${authStore.user.last_name}`
    : authStore.user?.username || 'Forge Admin'
})

const userInitials = computed(() => {
  if (authStore.user?.first_name && authStore.user?.last_name) {
    return `${authStore.user.first_name[0]}${authStore.user.last_name[0]}`.toUpperCase()
  }
  return authStore.user?.username?.[0]?.toUpperCase() || 'F'
})

// Reactive data
const stats = ref({
  totalPods: 0,
  activeOrders: 0,
  totalRevenue: 0,
  systemHealth: 98
})

const businesses = ref([])
const isLoading = ref(false)
const error = ref(null)

// Business modal state
const businessModal = ref({
  isOpen: false,
  business: null,
  mode: 'view' // 'view', 'edit', 'action'
})

// Navigation state
const activeTab = ref('dashboard')
const tabs = ref([
  { id: 'dashboard', name: 'Dashboard' },
  { id: 'reports', name: 'Subscription Reports' },
  { id: 'approvals', name: 'Payment Approvals' }
])

// Notification toast ref
const notificationToast = ref(null)

// Methods
const getStatusClass = (status) => {
  switch (status) {
    case 'active':
      return 'bg-[#2E7D32]/10 text-[#2E7D32]'
    case 'suspended':
      return 'bg-[#FF8F00]/10 text-[#FF8F00]'
    case 'inactive':
      return 'bg-[#C62828]/10 text-[#C62828]'
    default:
      return 'bg-[#4B4B4B]/10 text-[#4B4B4B]'
  }
}

// Data loading functions
const loadBusinesses = async () => {
  isLoading.value = true
  error.value = null
  try {
    const data = await businessService.getAllBusinesses()
    businesses.value = data

    // Update stats
    stats.value.totalPods = data.length
    stats.value.activeOrders = data.reduce((sum, business) => sum + (business.total_orders || 0), 0)
    stats.value.totalRevenue = data.reduce((sum, business) => sum + (business.total_revenue || 0), 0)
  } catch (err) {
    error.value = err.message
    console.error('Failed to load businesses:', err)
  } finally {
    isLoading.value = false
  }
}

const loadSystemStats = async () => {
  try {
    const data = await reportsService.getSystemDashboard()
    stats.value = {
      totalPods: data.business_stats?.total_businesses || 0,
      activeOrders: data.order_stats?.total_orders || 0,
      totalRevenue: data.revenue_stats?.total_revenue || 0,
      systemHealth: 98 // This could come from a health check endpoint
    }
  } catch (err) {
    console.error('Failed to load system stats:', err)
  }
}

// Business modal methods
const openBusinessModal = (business, mode) => {
  businessModal.value = {
    isOpen: true,
    business,
    mode
  }
}

const closeBusinessModal = () => {
  businessModal.value = {
    isOpen: false,
    business: null,
    mode: 'view'
  }
}

const handleBusinessSave = async (businessData) => {
  try {
    const businessId = businessModal.value.business.id
    await businessService.updateBusiness(businessId, businessData)

    // Refresh businesses list
    await loadBusinesses()
    closeBusinessModal()

    // Show success notification
    notificationToast.value?.showSuccess(
      'Business Updated',
      'Business information has been updated successfully.'
    )
  } catch (err) {
    console.error('Failed to update business:', err)
    // Show error notification
    notificationToast.value?.showError(
      'Update Failed',
      err.message || 'Failed to update business information.'
    )
  }
}

const handleBusinessStatusChange = async (newStatus) => {
  try {
    const businessId = businessModal.value.business.id
    await businessService.updateBusinessStatus(businessId, newStatus)

    // Refresh businesses list
    await loadBusinesses()
    closeBusinessModal()

    // Show success notification
    notificationToast.value?.showSuccess(
      'Status Updated',
      `Business status has been updated to ${newStatus}.`
    )
  } catch (err) {
    console.error('Failed to update business status:', err)
    // Show error notification
    notificationToast.value?.showError(
      'Status Update Failed',
      err.message || 'Failed to update business status.'
    )
  }
}

// User menu methods
const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
}

const handleLogout = async () => {
  try {
    await authStore.logout()
    router.push('/')
  } catch (error) {
    console.error('Logout failed:', error)
    // Force logout even if API call fails
    authStore.logout()
    router.push('/')
  }
}

// Close user menu when clicking outside
const handleClickOutside = (event) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target)) {
    showUserMenu.value = false
  }
}

onMounted(async () => {
  // Load data on component mount
  console.log('Forge Dashboard loaded')

  // Add click outside listener for user menu
  document.addEventListener('click', handleClickOutside)

  // Load initial data
  await Promise.all([
    loadBusinesses(),
    loadSystemStats()
  ])
})

onUnmounted(() => {
  // Remove click outside listener
  document.removeEventListener('click', handleClickOutside)
})
</script>
