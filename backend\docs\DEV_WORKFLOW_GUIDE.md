# 🔄 Development Workflow Guide

## 🎯 Daily Development Workflow

### 🌅 Starting Your Development Session

#### 1. Environment Setup (2 minutes)
```bash
# Navigate to project directory
cd /path/to/mthunzi

# Activate virtual environment
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows

# Pull latest changes
git pull origin main

# Install any new dependencies
pip install -r requirements.txt

# Apply any new migrations
python manage.py migrate
```

#### 2. Start Development Servers (1 minute)
```bash
# Terminal 1: Backend Server
python manage.py runserver

# Terminal 2: Frontend Server (when ready)
cd frontend
npm start
```

#### 3. Verify System Health (30 seconds)
```bash
# Quick health check
curl http://localhost:8000/health/

# Test authentication
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

---

## 🛠️ Feature Development Workflow

### 1. Planning Phase
- [ ] Define feature requirements
- [ ] Design API endpoints (if needed)
- [ ] Plan database changes (if needed)
- [ ] Create feature branch

```bash
git checkout -b feature/your-feature-name
```

### 2. Backend Development

#### A. Model Changes (if needed)
```bash
# 1. Update models in relevant app
# 2. Create migrations
python manage.py makemigrations

# 3. Apply migrations
python manage.py migrate

# 4. Test in Django shell
python manage.py shell
```

#### B. API Development
```bash
# 1. Create/update serializers
# 2. Create/update views
# 3. Update URLs
# 4. Test endpoints with curl or Postman
```

#### C. Business Logic
```bash
# 1. Add business logic to models/services
# 2. Update WhatsApp bot (if needed)
# 3. Add notification logic (if needed)
```

### 3. Testing
```bash
# Run specific tests
python manage.py test app_name.tests.TestClassName

# Run all tests
python manage.py test

# Test with coverage
coverage run --source='.' manage.py test
coverage report
```

### 4. Frontend Development (when ready)
```bash
# 1. Create/update React components
# 2. Add API integration
# 3. Test in browser
# 4. Ensure responsive design
```

---

## 🧪 Testing Workflow

### Unit Testing
```bash
# Test specific model
python manage.py test accounts.tests.UserModelTest

# Test specific view
python manage.py test accounts.tests.UserRegistrationAPITest

# Test with verbose output
python manage.py test --verbosity=2
```

### Integration Testing
```bash
# Test API endpoints
curl -X POST http://localhost:8000/api/auth/register/ \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "testpass123",
    "password_confirm": "testpass123",
    "phone_number": "+***********",
    "role": "pulse"
  }'
```

### WhatsApp Bot Testing
```python
# In Django shell
python manage.py shell

from whatsapp.bot import process_message
from accounts.models import Business

business = Business.objects.first()

# Test commands
test_messages = [
    "hello",
    "help", 
    "menu",
    "order",
    "status",
    "track",
    "payment",
    "contact"
]

for msg in test_messages:
    response = process_message(msg, "+***********", business)
    print(f"\nInput: {msg}")
    print(f"Output: {response}")
```

---

## 🔄 Git Workflow

### Feature Development
```bash
# 1. Create feature branch
git checkout -b feature/new-feature

# 2. Make changes and commit regularly
git add .
git commit -m "Add initial feature structure"

# 3. Push to remote
git push origin feature/new-feature

# 4. Create pull request when ready
```

### Commit Message Convention
```bash
# Format: type(scope): description

# Examples:
git commit -m "feat(auth): add phone verification"
git commit -m "fix(orders): resolve order calculation bug"
git commit -m "docs(api): update endpoint documentation"
git commit -m "test(payments): add payment verification tests"
git commit -m "refactor(bot): improve message handling"
```

### Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation
- `test`: Tests
- `refactor`: Code refactoring
- `style`: Code style changes
- `chore`: Maintenance tasks

---

## 📊 Database Development Workflow

### Schema Changes
```bash
# 1. Modify models
# 2. Create migration
python manage.py makemigrations app_name

# 3. Review migration file
cat app_name/migrations/0002_new_migration.py

# 4. Apply migration
python manage.py migrate

# 5. Test in shell
python manage.py shell
```

### Data Migrations
```python
# Create data migration
python manage.py makemigrations --empty app_name

# Edit migration file to add data operations
def forwards_func(apps, schema_editor):
    Model = apps.get_model('app_name', 'ModelName')
    # Add data operations here

def reverse_func(apps, schema_editor):
    # Add reverse operations here

class Migration(migrations.Migration):
    dependencies = [
        ('app_name', '0001_initial'),
    ]
    
    operations = [
        migrations.RunPython(forwards_func, reverse_func),
    ]
```

### Database Debugging
```python
# In Django shell
from django.db import connection

# See all queries
print(connection.queries)

# Reset query log
connection.queries_log.clear()

# Enable query logging
from django.conf import settings
settings.DEBUG = True
```

---

## 🎨 Frontend Development Workflow (Future)

### Component Development
```bash
# 1. Create component
mkdir src/components/NewComponent
touch src/components/NewComponent/index.js
touch src/components/NewComponent/NewComponent.js

# 2. Implement component
# 3. Add to parent component
# 4. Test in browser
```

### API Integration
```javascript
// 1. Create API service
// src/services/api.js
import axios from 'axios';

export const apiService = {
  getOrders: () => axios.get('/api/orders/'),
  createOrder: (data) => axios.post('/api/orders/', data),
};

// 2. Use in components
import { apiService } from '../services/api';

const [orders, setOrders] = useState([]);

useEffect(() => {
  apiService.getOrders()
    .then(response => setOrders(response.data))
    .catch(error => console.error(error));
}, []);
```

---

## 🚀 Deployment Workflow

### Development to Staging
```bash
# 1. Ensure all tests pass
python manage.py test

# 2. Update requirements if needed
pip freeze > requirements.txt

# 3. Commit and push
git add .
git commit -m "Prepare for staging deployment"
git push origin main

# 4. Deploy to staging
./deploy.sh staging
```

### Staging to Production
```bash
# 1. Test thoroughly on staging
# 2. Create release tag
git tag -a v1.0.0 -m "Release version 1.0.0"
git push origin v1.0.0

# 3. Deploy to production
./deploy.sh production

# 4. Monitor deployment
curl https://yourdomain.com/health/
```

---

## 🔍 Debugging Workflow

### Backend Debugging
```python
# Add debug prints
import logging
logger = logging.getLogger(__name__)
logger.debug("Debug message")

# Use Django shell for testing
python manage.py shell
>>> from myapp.models import MyModel
>>> obj = MyModel.objects.first()
>>> obj.some_method()

# Use pdb for step debugging
import pdb; pdb.set_trace()
```

### API Debugging
```bash
# Test with curl
curl -v -X POST http://localhost:8000/api/endpoint/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Token your-token" \
  -d '{"key": "value"}'

# Check Django logs
tail -f mthunzi.log

# Check server output
# Look at terminal running manage.py runserver
```

### Database Debugging
```bash
# Check database directly
python manage.py dbshell
.tables
.schema table_name
SELECT * FROM table_name LIMIT 5;

# Check migrations
python manage.py showmigrations
python manage.py migrate --plan
```

---

## 📈 Performance Optimization Workflow

### Database Optimization
```python
# Use select_related for foreign keys
queryset = Order.objects.select_related('customer', 'business')

# Use prefetch_related for many-to-many
queryset = Order.objects.prefetch_related('items__product')

# Add database indexes
class Meta:
    indexes = [
        models.Index(fields=['status', 'created_at']),
    ]
```

### API Optimization
```python
# Use pagination
from rest_framework.pagination import PageNumberPagination

class CustomPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100

# Cache expensive operations
from django.core.cache import cache

def expensive_operation():
    result = cache.get('expensive_key')
    if result is None:
        result = do_expensive_calculation()
        cache.set('expensive_key', result, 300)  # 5 minutes
    return result
```

---

## 🔒 Security Workflow

### Code Security Review
```bash
# Check for security issues
python manage.py check --deploy

# Update dependencies
pip list --outdated
pip install --upgrade package-name

# Security scan (if available)
safety check
bandit -r .
```

### API Security Testing
```bash
# Test authentication
curl -X GET http://localhost:8000/api/protected-endpoint/
# Should return 401 Unauthorized

# Test authorization
curl -X GET http://localhost:8000/api/admin-only/ \
  -H "Authorization: Token user-token"
# Should return 403 Forbidden for non-admin users
```

---

## 📝 Documentation Workflow

### Code Documentation
```python
def complex_function(param1, param2):
    """
    Brief description of what the function does.
    
    Args:
        param1 (str): Description of param1
        param2 (int): Description of param2
    
    Returns:
        dict: Description of return value
    
    Raises:
        ValueError: When param1 is invalid
    """
    pass
```

### API Documentation
```python
# Use DRF documentation features
from rest_framework.decorators import api_view
from rest_framework.response import Response

@api_view(['POST'])
def create_order(request):
    """
    Create a new order.
    
    Request body:
    {
        "customer_id": 1,
        "items": [{"product_id": 1, "quantity": 2}]
    }
    
    Returns:
    {
        "id": 1,
        "order_number": "ORD001",
        "status": "pending"
    }
    """
    pass
```

---

## 🎯 Quality Assurance Workflow

### Code Review Checklist
- [ ] Code follows project conventions
- [ ] Tests are included and pass
- [ ] Documentation is updated
- [ ] No security vulnerabilities
- [ ] Performance considerations addressed
- [ ] Error handling implemented
- [ ] Logging added where appropriate

### Pre-commit Checklist
- [ ] All tests pass
- [ ] Code is formatted properly
- [ ] No debug statements left
- [ ] Commit message is descriptive
- [ ] Changes are focused and atomic

---

## 🎉 Success Metrics

### Development Velocity
- Features completed per sprint
- Bug fix turnaround time
- Code review completion time
- Test coverage percentage

### Code Quality
- Test coverage > 80%
- No critical security issues
- Performance benchmarks met
- Documentation completeness

### User Experience
- API response times < 200ms
- WhatsApp bot response time < 2s
- Mobile responsiveness verified
- Accessibility standards met

---

**Happy developing! 🚀**

*This workflow guide will help you maintain high productivity and code quality while developing Mthunzi features.*
