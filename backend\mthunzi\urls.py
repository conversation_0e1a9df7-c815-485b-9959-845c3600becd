"""
URL configuration for mthunzi project.

Mthunzi - WhatsApp-based sales and delivery automation system for SMEs.
Owned by NorthForm.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from . import health

urlpatterns = [
    path('admin/', admin.site.urls),

    # Health check endpoints
    path('health/', health.health_check, name='health-check'),
    path('ready/', health.readiness_check, name='readiness-check'),
    path('alive/', health.liveness_check, name='liveness-check'),

    # API endpoints
    path('api/auth/', include('accounts.urls')),
    path('api/orders/', include('orders.urls')),
    path('api/payments/', include('payments.urls')),
    path('api/deliveries/', include('deliveries.urls')),
    path('api/whatsapp/', include('whatsapp.urls')),
    path('api/', include('services.urls')),
    path('api/', include('services.urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
