from django.db import models
from django.conf import settings
import uuid


class WhatsAppMessage(models.Model):
    """
    WhatsApp messages sent and received through the system.
    """

    class MessageType(models.TextChoices):
        TEXT = 'text', 'Text'
        IMAGE = 'image', 'Image'
        DOCUMENT = 'document', 'Document'
        AUDIO = 'audio', 'Audio'
        VIDEO = 'video', 'Video'
        LOCATION = 'location', 'Location'
        CONTACT = 'contact', 'Contact'
        INTERACTIVE = 'interactive', 'Interactive'

    class MessageDirection(models.TextChoices):
        INBOUND = 'inbound', 'Inbound (Received)'
        OUTBOUND = 'outbound', 'Outbound (Sent)'

    class MessageStatus(models.TextChoices):
        PENDING = 'pending', 'Pending'
        SENT = 'sent', 'Sent'
        DELIVERED = 'delivered', 'Delivered'
        READ = 'read', 'Read'
        FAILED = 'failed', 'Failed'

    # Message identification
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    whatsapp_message_id = models.CharField(max_length=100, unique=True)

    # Participants
    sender = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='sent_messages',
        null=True,
        blank=True
    )
    recipient = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='received_messages',
        null=True,
        blank=True
    )

    # For messages from/to unknown numbers
    sender_phone = models.CharField(max_length=17, blank=True)
    recipient_phone = models.CharField(max_length=17, blank=True)

    # Message details
    message_type = models.CharField(max_length=15, choices=MessageType.choices)
    direction = models.CharField(max_length=10, choices=MessageDirection.choices)
    status = models.CharField(
        max_length=10,
        choices=MessageStatus.choices,
        default=MessageStatus.PENDING
    )

    # Content
    content = models.TextField(blank=True, help_text="Text content of the message")
    media_url = models.URLField(blank=True, help_text="URL to media file")
    media_id = models.CharField(max_length=100, blank=True)

    # Context (for replies and interactions)
    reply_to = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='replies'
    )

    # Business context
    business = models.ForeignKey(
        'accounts.Business',
        on_delete=models.CASCADE,
        related_name='whatsapp_messages',
        null=True,
        blank=True
    )

    # Related order (if applicable)
    order = models.ForeignKey(
        'orders.Order',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='whatsapp_messages'
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    sent_at = models.DateTimeField(null=True, blank=True)
    delivered_at = models.DateTimeField(null=True, blank=True)
    read_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.direction} {self.message_type} - {self.sender_phone or self.sender}"

    class Meta:
        ordering = ['-created_at']


class WhatsAppTemplate(models.Model):
    """
    WhatsApp message templates for automated responses.
    """

    class TemplateCategory(models.TextChoices):
        WELCOME = 'welcome', 'Welcome Message'
        ORDER_CONFIRMATION = 'order_confirmation', 'Order Confirmation'
        PAYMENT_REQUEST = 'payment_request', 'Payment Request'
        PAYMENT_CONFIRMATION = 'payment_confirmation', 'Payment Confirmation'
        DELIVERY_UPDATE = 'delivery_update', 'Delivery Update'
        ORDER_COMPLETE = 'order_complete', 'Order Complete'

        # Service business templates
        APPOINTMENT_CONFIRMATION = 'appointment_confirmation', 'Appointment Confirmation'
        APPOINTMENT_REMINDER = 'appointment_reminder', 'Appointment Reminder'
        APPOINTMENT_CANCELLED = 'appointment_cancelled', 'Appointment Cancelled'
        APPOINTMENT_RESCHEDULED = 'appointment_rescheduled', 'Appointment Rescheduled'
        SERVICE_COMPLETE = 'service_complete', 'Service Complete'

        # Food business templates
        MENU_UPDATE = 'menu_update', 'Daily Menu Update'
        FOOD_ORDER_READY = 'food_order_ready', 'Food Order Ready'
        FOOD_ORDER_DELAYED = 'food_order_delayed', 'Food Order Delayed'
        DAILY_SPECIALS = 'daily_specials', 'Daily Specials'

        GENERAL = 'general', 'General'

    business = models.ForeignKey(
        'accounts.Business',
        on_delete=models.CASCADE,
        related_name='whatsapp_templates'
    )

    name = models.CharField(max_length=100)
    category = models.CharField(max_length=30, choices=TemplateCategory.choices)

    # Template content with placeholders
    content = models.TextField(
        help_text="Template content with placeholders like {customer_name}, {order_number}, etc."
    )

    # WhatsApp template details
    whatsapp_template_name = models.CharField(max_length=100, blank=True)
    language_code = models.CharField(max_length=10, default='en')

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} - {self.business.name}"

    def render(self, context):
        """Render template with provided context."""
        content = self.content
        for key, value in context.items():
            placeholder = '{' + key + '}'
            content = content.replace(placeholder, str(value))
        return content

    class Meta:
        unique_together = ['business', 'name']


class WhatsAppWebhook(models.Model):
    """
    Log of WhatsApp webhook events for debugging and monitoring.
    """

    class EventType(models.TextChoices):
        MESSAGE = 'message', 'Message'
        STATUS = 'status', 'Status Update'
        ERROR = 'error', 'Error'
        OTHER = 'other', 'Other'

    event_type = models.CharField(max_length=10, choices=EventType.choices)

    # Raw webhook data
    raw_data = models.JSONField(help_text="Raw webhook payload")

    # Processing status
    processed = models.BooleanField(default=False)
    processing_error = models.TextField(blank=True)

    # Related message (if applicable)
    message = models.ForeignKey(
        WhatsAppMessage,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='webhook_events'
    )

    created_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.event_type} webhook - {self.created_at}"

    class Meta:
        ordering = ['-created_at']


class ChatSession(models.Model):
    """
    Chat sessions between customers and businesses.
    """

    class SessionStatus(models.TextChoices):
        ACTIVE = 'active', 'Active'
        CLOSED = 'closed', 'Closed'
        ARCHIVED = 'archived', 'Archived'

    # Session identification
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Participants
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        limit_choices_to={'role': 'pulse'},
        related_name='chat_sessions'
    )
    business = models.ForeignKey(
        'accounts.Business',
        on_delete=models.CASCADE,
        related_name='chat_sessions'
    )

    # Session details
    status = models.CharField(
        max_length=10,
        choices=SessionStatus.choices,
        default=SessionStatus.ACTIVE
    )

    # Related order (if applicable)
    order = models.ForeignKey(
        'orders.Order',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='chat_sessions'
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    closed_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"Chat: {self.customer.username} - {self.business.name}"

    class Meta:
        ordering = ['-updated_at']
