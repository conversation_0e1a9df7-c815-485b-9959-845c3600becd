# Business Types API Documentation

## Overview

The Mthunzi platform now supports three distinct business types, each with specialized features and API endpoints:

1. **Product/Goods Businesses** - Traditional retail with inventory management
2. **Service Businesses** - Appointment-based services (salons, consultants, etc.)
3. **Food/Restaurant Businesses** - Food services with daily menu management

## Business Type Detection

### Get Business Capabilities

**Endpoint:** `GET /api/auth/capabilities/`
**Authentication:** Required (Pod/Business Owner)

Returns the current user's business capabilities and supported features.

```json
{
  "business_id": 1,
  "business_name": "Awesome Salon",
  "business_type": "service",
  "business_type_display": "Service Business",
  "capabilities": {
    "supports_products": false,
    "supports_inventory": false,
    "supports_services": true,
    "supports_appointments": true,
    "supports_daily_menu": false,
    "supports_delivery": false,
    "supports_whatsapp_ordering": true
  },
  "features": [
    "service_catalog",
    "appointment_booking",
    "schedule_management",
    "customer_management",
    "service_history"
  ]
}
```

### Get Specific Business Capabilities

**Endpoint:** `GET /api/auth/businesses/{business_id}/capabilities/`
**Authentication:** Required

Returns capabilities for a specific business (useful for customers).

## Product/Goods Business APIs

### Products

- `GET/POST /api/orders/products/` - List/create products
- `GET/PUT/DELETE /api/orders/products/{id}/` - Product operations
- Supports inventory management and stock tracking

### Orders

- `GET/POST /api/orders/` - List/create orders
- `GET/PUT /api/orders/{id}/` - Order operations
- `POST /api/orders/{id}/confirm/` - Confirm order

## Service Business APIs

### Services Management

#### List/Create Services

**Endpoint:** `GET/POST /api/services/`
**Authentication:** Required
**Business Type:** Service only

**GET Response:**

```json
[
  {
    "id": 1,
    "business": 1,
    "business_name": "Awesome Salon",
    "name": "Haircut",
    "description": "Professional haircut service",
    "price": "50.00",
    "duration_minutes": 60,
    "category": "Hair Services",
    "status": "active",
    "requires_deposit": false,
    "deposit_amount": "0.00",
    "buffer_time_minutes": 15,
    "total_duration_minutes": 75,
    "is_available": true
  }
]
```

**POST Request:**

```json
{
  "name": "Hair Styling",
  "description": "Professional hair styling",
  "price": "75.00",
  "duration_minutes": 90,
  "category": "Hair Services",
  "requires_deposit": true,
  "deposit_amount": "25.00"
}
```

#### Service Detail

**Endpoint:** `GET/PUT/DELETE /api/services/{id}/`
**Authentication:** Required
**Business Type:** Service only

### Appointment Management

#### List/Create Appointments

**Endpoint:** `GET/POST /api/appointments/`
**Authentication:** Required

**GET Response (Business Owner):**

```json
[
  {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "appointment_number": "APT123456",
    "customer": {
      "id": 2,
      "username": "customer1",
      "email": "<EMAIL>"
    },
    "service_name": "Haircut",
    "appointment_datetime": "2025-07-15T14:00:00Z",
    "end_datetime": "2025-07-15T15:15:00Z",
    "duration_minutes": 60,
    "status": "confirmed",
    "service_price": "50.00",
    "total_amount": "50.00",
    "can_be_cancelled": true,
    "can_be_rescheduled": true
  }
]
```

**POST Request (Customer):**

```json
{
  "service": 1,
  "appointment_datetime": "2025-07-15T14:00:00Z",
  "customer_notes": "Please use organic products"
}
```

#### Appointment Actions

**Confirm Appointment:**
`POST /api/appointments/{id}/confirm/`
**Authentication:** Required (Business Owner)

**Cancel Appointment:**
`POST /api/appointments/{id}/cancel/`
**Authentication:** Required (Business Owner or Customer)

**Reschedule Appointment:**
`POST /api/appointments/{id}/reschedule/`
**Authentication:** Required (Business Owner or Customer)

```json
{
  "new_datetime": "2025-07-15T16:00:00Z"
}
```

### Available Time Slots

**Endpoint:** `GET /api/services/{service_id}/available-slots/?date=YYYY-MM-DD`
**Authentication:** Required

Returns available booking slots for a specific service on a given date.

```json
{
  "service_name": "Haircut",
  "date": "2025-07-15",
  "available_slots": [
    {
      "start_time": "09:00",
      "end_time": "10:15",
      "datetime": "2025-07-15T09:00:00Z",
      "is_available": true
    },
    {
      "start_time": "10:30",
      "end_time": "11:45",
      "datetime": "2025-07-15T10:30:00Z",
      "is_available": false
    }
  ]
}
```

## Food/Restaurant Business APIs

### Daily Menu Management

#### List/Create Daily Menus

**Endpoint:** `GET/POST /api/orders/daily-menus/`
**Authentication:** Required (Business Owner)
**Business Type:** Food/Restaurant only

**GET Response:**

```json
[
  {
    "id": 1,
    "business": 1,
    "business_name": "Tasty Bites",
    "date": "2025-07-10",
    "is_active": true,
    "preparation_time_minutes": 30,
    "effective_preparation_time": 30,
    "notes": "Fresh ingredients today!",
    "menu_items": [
      {
        "id": 1,
        "product": 1,
        "product_name": "Burger",
        "available_quantity": 50,
        "remaining_quantity": 35,
        "is_available": true,
        "special_price": "15.00",
        "effective_price": "15.00",
        "is_sold_out": false,
        "is_orderable": true
      }
    ]
  }
]
```

**POST Request:**

```json
{
  "date": "2025-07-11",
  "is_active": true,
  "preparation_time_minutes": 25,
  "notes": "Special weekend menu",
  "menu_items": [
    {
      "product": 1,
      "available_quantity": 30,
      "is_available": true,
      "special_price": "12.00",
      "notes": "Weekend special price"
    }
  ]
}
```

#### Today's Menu

**Endpoint:** `GET /api/orders/todays-menu/`
**Authentication:** Required

Returns the active menu for today for the current business.

**Endpoint:** `GET /api/orders/businesses/{business_id}/todays-menu/`
**Authentication:** Required

Returns today's menu for a specific business (for customers).

```json
{
  "business_name": "Tasty Bites",
  "date": "2025-07-10",
  "preparation_time_minutes": 30,
  "notes": "Fresh ingredients today!",
  "menu_items": [
    {
      "product_name": "Burger",
      "description": "Juicy beef burger",
      "effective_price": "15.00",
      "remaining_quantity": 35,
      "is_orderable": true,
      "notes": "Limited quantity"
    }
  ]
}
```

## Enhanced Dashboard API

### Business Dashboard

**Endpoint:** `GET /api/auth/dashboard/`
**Authentication:** Required (Business Owner)

Returns business type-specific dashboard data with relevant metrics and quick actions.

**Product Business Response:**

```json
{
  "business_type": "product_goods",
  "dashboard_type": "product_business",
  "metrics": {
    "products": {
      "total": 25,
      "active": 20,
      "low_stock": 3
    },
    "orders": {
      "total": 150,
      "today": 5,
      "this_week": 23,
      "this_month": 89
    },
    "revenue": {
      "today": 250.0,
      "this_week": 1500.0,
      "this_month": 5600.0
    }
  },
  "quick_actions": [
    { "name": "Add Product", "url": "/api/orders/products/", "method": "POST" },
    { "name": "View Orders", "url": "/api/orders/", "method": "GET" }
  ]
}
```

**Service Business Response:**

```json
{
  "business_type": "service",
  "dashboard_type": "service_business",
  "metrics": {
    "services": {
      "total": 8,
      "active": 7
    },
    "appointments": {
      "total": 120,
      "today": 6,
      "this_week": 25,
      "this_month": 95,
      "pending": 3,
      "confirmed": 8
    },
    "revenue": {
      "today": 300.0,
      "this_week": 1800.0,
      "this_month": 6200.0
    }
  },
  "quick_actions": [
    { "name": "Add Service", "url": "/api/services/", "method": "POST" },
    {
      "name": "View Appointments",
      "url": "/api/appointments/",
      "method": "GET"
    }
  ]
}
```

## Error Handling

### Business Type Validation Errors

When attempting operations not supported by the business type:

```json
{
  "error": "Only service businesses can create services."
}
```

```json
{
  "error": "Service businesses cannot manage products. Use services instead."
}
```

### Common HTTP Status Codes

- `400 Bad Request` - Invalid data or business type mismatch
- `403 Forbidden` - Operation not allowed for business type
- `404 Not Found` - Resource not found
- `422 Unprocessable Entity` - Validation errors

## Rate Limiting

All endpoints follow the same rate limiting rules:

- 100 requests per minute for authenticated users
- 1000 requests per hour for business operations

## Webhooks

Business type-specific webhook events:

### Service Business Events

- `appointment.created`
- `appointment.confirmed`
- `appointment.cancelled`
- `appointment.completed`
- `appointment.rescheduled`

### Food Business Events

- `daily_menu.created`
- `daily_menu.updated`
- `menu_item.sold_out`

### All Business Types

- `order.created`
- `order.confirmed`
- `order.completed`

## SDK Examples

### JavaScript/Node.js

```javascript
// Get business capabilities
const capabilities = await fetch("/api/auth/capabilities/", {
  headers: { Authorization: `Token ${token}` },
}).then((r) => r.json());

if (capabilities.supports_appointments) {
  // Book an appointment
  const appointment = await fetch("/api/appointments/", {
    method: "POST",
    headers: {
      Authorization: `Token ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      service: 1,
      appointment_datetime: "2025-07-15T14:00:00Z",
    }),
  }).then((r) => r.json());
}
```

### Python

```python
import requests

# Get business capabilities
response = requests.get(
    'http://localhost:8000/api/auth/capabilities/',
    headers={'Authorization': f'Token {token}'}
)
capabilities = response.json()

if capabilities['supports_daily_menu']:
    # Create daily menu
    menu_data = {
        'date': '2025-07-11',
        'is_active': True,
        'menu_items': [
            {
                'product': 1,
                'available_quantity': 30,
                'special_price': '12.00'
            }
        ]
    }
    response = requests.post(
        'http://localhost:8000/api/orders/daily-menus/',
        headers={
            'Authorization': f'Token {token}',
            'Content-Type': 'application/json'
        },
        json=menu_data
    )
```
