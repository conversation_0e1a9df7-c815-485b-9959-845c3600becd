#!/bin/bash

# Mthunzi Backup Script
# This script creates backups of the database and media files

set -e

echo "📦 Starting Mthunzi backup..."

# Configuration
BACKUP_DIR="backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_BACKUP_FILE="$BACKUP_DIR/db_backup_$DATE.sql"
MEDIA_BACKUP_FILE="$BACKUP_DIR/media_backup_$DATE.tar.gz"

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Database backup
echo "🗄️ Backing up database..."
docker-compose exec -T db pg_dump -U mthunzi mthunzi > $DB_BACKUP_FILE
echo "✅ Database backup created: $DB_BACKUP_FILE"

# Media files backup
echo "📁 Backing up media files..."
docker run --rm -v mthunzi_media_volume:/data -v $(pwd)/$BACKUP_DIR:/backup alpine tar czf /backup/media_backup_$DATE.tar.gz -C /data .
echo "✅ Media backup created: $MEDIA_BACKUP_FILE"

# Compress database backup
echo "🗜️ Compressing database backup..."
gzip $DB_BACKUP_FILE
echo "✅ Database backup compressed: $DB_BACKUP_FILE.gz"

# Clean up old backups (keep last 7 days)
echo "🧹 Cleaning up old backups..."
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
echo "✅ Old backups cleaned up"

# Upload to cloud storage (if configured)
if [ ! -z "$AWS_S3_BACKUP_BUCKET" ]; then
    echo "☁️ Uploading to S3..."
    aws s3 cp $DB_BACKUP_FILE.gz s3://$AWS_S3_BACKUP_BUCKET/database/
    aws s3 cp $MEDIA_BACKUP_FILE s3://$AWS_S3_BACKUP_BUCKET/media/
    echo "✅ Backups uploaded to S3"
fi

echo "🎉 Backup completed successfully!"
echo "📊 Backup summary:"
echo "  Database: $DB_BACKUP_FILE.gz"
echo "  Media: $MEDIA_BACKUP_FILE"
