"""
WhatsApp integration services using Twilio API.
"""

import logging
from typing import Dict, Any, Optional
from django.conf import settings
from twilio.rest import Client
from twilio.base.exceptions import TwilioException
from .models import WhatsAppMessage, WhatsAppTemplate

logger = logging.getLogger(__name__)


class WhatsAppService:
    """
    Service class for WhatsApp integration using Twilio.
    """
    
    def __init__(self):
        self.client = None
        self.from_number = settings.TWILIO_WHATSAPP_NUMBER
        
        if settings.TWILIO_ACCOUNT_SID and settings.TWILIO_AUTH_TOKEN:
            self.client = Client(
                settings.TWILIO_ACCOUNT_SID,
                settings.TWILIO_AUTH_TOKEN
            )
    
    def is_configured(self) -> bool:
        """Check if WhatsApp service is properly configured."""
        return self.client is not None and bool(self.from_number)
    
    def send_text_message(self, to_number: str, message: str, business=None) -> Optional[WhatsAppMessage]:
        """
        Send a text message via WhatsApp.
        
        Args:
            to_number: Recipient's phone number (with country code)
            message: Text message to send
            business: Business instance (optional)
        
        Returns:
            WhatsAppMessage instance if successful, None otherwise
        """
        if not self.is_configured():
            logger.error("WhatsApp service is not properly configured")
            return None
        
        try:
            # Format phone number for WhatsApp
            if not to_number.startswith('whatsapp:'):
                to_number = f'whatsapp:{to_number}'
            
            # Send message via Twilio
            twilio_message = self.client.messages.create(
                body=message,
                from_=self.from_number,
                to=to_number
            )
            
            # Create WhatsApp message record
            whatsapp_message = WhatsAppMessage.objects.create(
                whatsapp_message_id=twilio_message.sid,
                recipient_phone=to_number.replace('whatsapp:', ''),
                message_type='text',
                direction='outbound',
                content=message,
                business=business,
                status='sent'
            )
            
            logger.info(f"WhatsApp message sent successfully: {twilio_message.sid}")
            return whatsapp_message
            
        except TwilioException as e:
            logger.error(f"Failed to send WhatsApp message: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error sending WhatsApp message: {str(e)}")
            return None
    
    def send_media_message(self, to_number: str, message: str, media_url: str, business=None) -> Optional[WhatsAppMessage]:
        """
        Send a media message via WhatsApp.
        
        Args:
            to_number: Recipient's phone number
            message: Text message to accompany media
            media_url: URL of the media file
            business: Business instance (optional)
        
        Returns:
            WhatsAppMessage instance if successful, None otherwise
        """
        if not self.is_configured():
            logger.error("WhatsApp service is not properly configured")
            return None
        
        try:
            # Format phone number for WhatsApp
            if not to_number.startswith('whatsapp:'):
                to_number = f'whatsapp:{to_number}'
            
            # Send message via Twilio
            twilio_message = self.client.messages.create(
                body=message,
                media_url=[media_url],
                from_=self.from_number,
                to=to_number
            )
            
            # Create WhatsApp message record
            whatsapp_message = WhatsAppMessage.objects.create(
                whatsapp_message_id=twilio_message.sid,
                recipient_phone=to_number.replace('whatsapp:', ''),
                message_type='image',  # Could be determined from media_url
                direction='outbound',
                content=message,
                media_url=media_url,
                business=business,
                status='sent'
            )
            
            logger.info(f"WhatsApp media message sent successfully: {twilio_message.sid}")
            return whatsapp_message
            
        except TwilioException as e:
            logger.error(f"Failed to send WhatsApp media message: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error sending WhatsApp media message: {str(e)}")
            return None
    
    def send_template_message(self, to_number: str, template: WhatsAppTemplate, context: Dict[str, Any], business=None) -> Optional[WhatsAppMessage]:
        """
        Send a template message via WhatsApp.
        
        Args:
            to_number: Recipient's phone number
            template: WhatsAppTemplate instance
            context: Context data for template rendering
            business: Business instance (optional)
        
        Returns:
            WhatsAppMessage instance if successful, None otherwise
        """
        try:
            # Render template with context
            message = template.render(context)
            
            # Send as text message
            return self.send_text_message(to_number, message, business)
            
        except Exception as e:
            logger.error(f"Failed to send template message: {str(e)}")
            return None
    
    def get_message_status(self, message_sid: str) -> Optional[str]:
        """
        Get the status of a sent message.
        
        Args:
            message_sid: Twilio message SID
        
        Returns:
            Message status string or None
        """
        if not self.is_configured():
            return None
        
        try:
            message = self.client.messages(message_sid).fetch()
            return message.status
        except TwilioException as e:
            logger.error(f"Failed to get message status: {str(e)}")
            return None


class WhatsAppWebhookProcessor:
    """
    Processor for WhatsApp webhook events.
    """
    
    def __init__(self):
        self.whatsapp_service = WhatsAppService()
    
    def process_incoming_message(self, webhook_data: Dict[str, Any]) -> Optional[WhatsAppMessage]:
        """
        Process incoming WhatsApp message from webhook.
        
        Args:
            webhook_data: Webhook payload data
        
        Returns:
            WhatsAppMessage instance if processed successfully
        """
        try:
            # Extract message data from webhook
            message_sid = webhook_data.get('MessageSid')
            from_number = webhook_data.get('From', '').replace('whatsapp:', '')
            to_number = webhook_data.get('To', '').replace('whatsapp:', '')
            body = webhook_data.get('Body', '')
            media_url = webhook_data.get('MediaUrl0', '')
            
            # Determine message type
            message_type = 'text'
            if media_url:
                message_type = self._determine_media_type(webhook_data.get('MediaContentType0', ''))
            
            # Create WhatsApp message record
            whatsapp_message = WhatsAppMessage.objects.create(
                whatsapp_message_id=message_sid,
                sender_phone=from_number,
                recipient_phone=to_number,
                message_type=message_type,
                direction='inbound',
                content=body,
                media_url=media_url,
                status='delivered'
            )
            
            logger.info(f"Processed incoming WhatsApp message: {message_sid}")
            return whatsapp_message
            
        except Exception as e:
            logger.error(f"Failed to process incoming message: {str(e)}")
            return None
    
    def process_status_update(self, webhook_data: Dict[str, Any]) -> bool:
        """
        Process message status update from webhook.
        
        Args:
            webhook_data: Webhook payload data
        
        Returns:
            True if processed successfully, False otherwise
        """
        try:
            message_sid = webhook_data.get('MessageSid')
            status = webhook_data.get('MessageStatus')
            
            if not message_sid or not status:
                return False
            
            # Update message status
            WhatsAppMessage.objects.filter(
                whatsapp_message_id=message_sid
            ).update(status=status)
            
            logger.info(f"Updated message status: {message_sid} -> {status}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to process status update: {str(e)}")
            return False
    
    def _determine_media_type(self, content_type: str) -> str:
        """Determine message type from media content type."""
        if content_type.startswith('image/'):
            return 'image'
        elif content_type.startswith('video/'):
            return 'video'
        elif content_type.startswith('audio/'):
            return 'audio'
        elif content_type.startswith('application/'):
            return 'document'
        else:
            return 'text'


# Global service instance
whatsapp_service = WhatsAppService()
webhook_processor = WhatsAppWebhookProcessor()
