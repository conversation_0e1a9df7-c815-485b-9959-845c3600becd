# Mthunzi Deployment Guide

This guide covers deploying the Mthunzi WhatsApp-based sales and delivery automation system to production.

## Prerequisites

### Server Requirements
- **OS**: Ubuntu 20.04 LTS or newer
- **RAM**: Minimum 2GB, recommended 4GB+
- **Storage**: Minimum 20GB SSD
- **CPU**: 2+ cores recommended
- **Network**: Public IP with ports 80, 443 open

### Software Requirements
- Docker 20.10+
- Docker Compose 2.0+
- Git
- Nginx (if not using Docker nginx)
- SSL certificate (Let's Encrypt recommended)

## Quick Start

### 1. Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Logout and login to apply Docker group changes
```

### 2. Clone Repository

```bash
git clone https://github.com/your-org/mthunzi.git
cd mthunzi
```

### 3. Environment Configuration

Create production environment file:

```bash
cp .env.example .env.production
```

Edit `.env.production` with your configuration:

```bash
# Django Configuration
SECRET_KEY=your-super-secret-key-here
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
DJANGO_ENVIRONMENT=production

# Database Configuration
DATABASE_URL=*********************************************/mthunzi
POSTGRES_PASSWORD=your-db-password

# Redis Configuration
REDIS_URL=redis://redis:6379/0

# WhatsApp/Twilio Configuration
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_WHATSAPP_NUMBER=whatsapp:+**********
WHATSAPP_WEBHOOK_URL=https://yourdomain.com/api/whatsapp/webhook/

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>

# AWS S3 Configuration (optional)
USE_S3=True
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=your-s3-bucket
AWS_S3_REGION_NAME=us-east-1

# Security
ADMIN_PASSWORD=your-admin-password
ADMIN_EMAIL=<EMAIL>

# CORS Configuration
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Monitoring (optional)
SENTRY_DSN=your-sentry-dsn
```

### 4. SSL Certificate Setup

For Let's Encrypt SSL:

```bash
# Install certbot
sudo apt install certbot

# Get certificate
sudo certbot certonly --standalone -d yourdomain.com -d www.yourdomain.com

# Copy certificates to project
sudo mkdir -p ssl
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem ssl/cert.pem
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem ssl/key.pem
sudo chown -R $USER:$USER ssl/
```

### 5. Deploy

```bash
# Make scripts executable
chmod +x deploy.sh scripts/*.sh

# Create backup directory
mkdir -p backups

# Deploy
./deploy.sh production
```

## Manual Deployment Steps

If you prefer manual deployment:

### 1. Build and Start Services

```bash
# Build images
docker-compose build

# Start services
docker-compose up -d
```

### 2. Database Setup

```bash
# Run migrations
docker-compose exec web python manage.py migrate

# Create superuser
docker-compose exec web python manage.py createsuperuser
```

### 3. Static Files

```bash
# Collect static files
docker-compose exec web python manage.py collectstatic --noinput
```

## Configuration Details

### WhatsApp Integration

1. **Twilio Setup**:
   - Create Twilio account
   - Get WhatsApp Business API access
   - Configure webhook URL: `https://yourdomain.com/api/whatsapp/webhook/`

2. **Webhook Configuration**:
   - Set webhook URL in Twilio console
   - Ensure HTTPS is enabled
   - Test webhook connectivity

### Database Configuration

The system uses PostgreSQL in production:

```yaml
# docker-compose.yml database service
db:
  image: postgres:15
  environment:
    POSTGRES_DB: mthunzi
    POSTGRES_USER: mthunzi
    POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
```

### Nginx Configuration

The included `nginx.conf` provides:
- SSL termination
- Static file serving
- Rate limiting
- Security headers
- Gzip compression

### Monitoring and Logging

Logs are stored in:
- Application logs: `./logs/mthunzi.log`
- Nginx logs: Docker container logs
- Database logs: Docker container logs

## Backup and Restore

### Automated Backups

Set up daily backups with cron:

```bash
# Add to crontab
crontab -e

# Add this line for daily backups at 2 AM
0 2 * * * /path/to/mthunzi/scripts/backup.sh
```

### Manual Backup

```bash
./scripts/backup.sh
```

### Restore from Backup

```bash
./scripts/restore.sh 20231201_143000 true
```

## Scaling and Performance

### Horizontal Scaling

To scale the web service:

```bash
docker-compose up -d --scale web=3
```

### Database Optimization

For high traffic:
1. Use connection pooling
2. Configure PostgreSQL for performance
3. Add read replicas if needed

### Caching

Redis is configured for:
- Session storage
- Cache backend
- Celery message broker

## Security Checklist

- [ ] SSL certificate installed and configured
- [ ] Strong SECRET_KEY generated
- [ ] Database password is secure
- [ ] Admin password is strong
- [ ] Firewall configured (only ports 80, 443 open)
- [ ] Regular security updates applied
- [ ] Backup encryption enabled
- [ ] Monitoring and alerting configured

## Troubleshooting

### Common Issues

1. **Services won't start**:
   ```bash
   docker-compose logs
   ```

2. **Database connection errors**:
   ```bash
   docker-compose exec db psql -U mthunzi -d mthunzi
   ```

3. **WhatsApp webhook not working**:
   - Check webhook URL is accessible
   - Verify SSL certificate
   - Check Twilio configuration

4. **Static files not loading**:
   ```bash
   docker-compose exec web python manage.py collectstatic --noinput
   ```

### Health Checks

Monitor system health:

```bash
# Application health
curl https://yourdomain.com/health/

# Service status
docker-compose ps

# Resource usage
docker stats
```

### Log Analysis

```bash
# Application logs
docker-compose logs web

# Database logs
docker-compose logs db

# Nginx logs
docker-compose logs nginx
```

## Maintenance

### Regular Tasks

1. **Weekly**:
   - Check disk space
   - Review error logs
   - Verify backups

2. **Monthly**:
   - Update dependencies
   - Security patches
   - Performance review

3. **Quarterly**:
   - SSL certificate renewal
   - Capacity planning
   - Security audit

### Updates

To update the application:

```bash
git pull origin main
./deploy.sh production
```

## Support

For deployment issues:
1. Check logs first
2. Review this documentation
3. Check GitHub issues
4. Contact support team

## Production Checklist

Before going live:

- [ ] All environment variables configured
- [ ] SSL certificate installed
- [ ] Database backups working
- [ ] WhatsApp integration tested
- [ ] Admin user created
- [ ] Monitoring configured
- [ ] Security review completed
- [ ] Performance testing done
- [ ] Documentation updated
- [ ] Team trained on operations
