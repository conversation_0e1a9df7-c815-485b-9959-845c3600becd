# Mthunzi

Mthunzi is a WhatsApp-based sales and delivery automation system designed for SMEs.  
It helps small businesses manage orders, payments, and deliveries seamlessly via WhatsApp.

---

## Owned By

**NorthForm**

---

## Features

### Core Features (MVP)
- Customer ordering through WhatsApp  
- Proof of payment submission  
- Business owner (Pod) payment verification  
- Order status notifications  
- Delivery confirmation flow  
- Basic admin dashboard for business owners (Pods)  
- Role-based access control for system owner (Forge), business owners (Pods), delivery agents (Runners), and customers (Pulses)

### Planned Features (Post-MVP)
- Auto-detection of payment proofs  
- Inventory management  
- Promotions and discounts  
- Customer feedback collection  
- Multi-business management under system owner (Forge)  
- Sales and activity reports  
- Multi-language support  
- Chatbot FAQs and support  

---

## User Roles

| Role  | Description                                  |
|-------|----------------------------------------------|
| **Forge** | System/brand owner, powers the platform   |
| **Pod**   | Business owner using Mthunzi to sell       |
| **Pulse** | Customer interacting via WhatsApp          |
| **Runner**| Delivery agent handling shipments          |
| **Luma**  | WhatsApp chatbot handling communication    |

---

## Technology Stack

- Backend: Django + Django REST Framework  
- WhatsApp Integration: WhatsApp Business API or Twilio API  
- Frontend/Admin: Flutter (for dashboard)
- Database: PostgreSQL or preferred RDBMS  

---

## Getting Started

1. Clone the repository  
2. Setup your virtual environment and install dependencies  
3. Configure environment variables for WhatsApp API and database  
4. Run migrations and start the server  
5. Connect WhatsApp Business API to start receiving messages  

---

## Contribution

Contributions are welcome! Please open issues or submit pull requests for improvements and new features.

---

## License

MIT License

---

## Contact

For inquiries and support, contact **NorthForm** at [<EMAIL>]

