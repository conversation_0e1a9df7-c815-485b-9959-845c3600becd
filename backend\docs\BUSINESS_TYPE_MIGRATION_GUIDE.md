# Business Type Migration Guide

This guide explains how to migrate existing Mthunzi businesses to the new business type system that supports Product/Goods, Service, and Food/Restaurant businesses.

## Overview

The new business type system introduces three distinct business categories:

1. **Product/Goods** (`product_goods`) - Traditional retail businesses with inventory management
2. **Service** (`service`) - Appointment-based businesses like salons, consultants, etc.
3. **Food/Restaurant** (`food_restaurant`) - Food businesses with daily menu management

## Database Changes

### New Fields Added to Business Model

- `business_type` - The type of business (required)
- `booking_advance_days` - How many days in advance customers can book (service businesses)
- `booking_buffer_minutes` - Buffer time between appointments (service businesses)
- `service_hours_start` - Business opening time for services
- `service_hours_end` - Business closing time for services
- `daily_menu_enabled` - Enable daily menu management (food businesses)
- `preparation_time_minutes` - Average preparation time for orders (food businesses)
- `accepts_preorders` - Whether business accepts advance orders (food businesses)

### New Models Added

#### Services App
- `Service` - Services offered by service businesses
- `ServiceAvailability` - Weekly schedule for services
- `Appointment` - Customer appointments for services

#### Orders App (Extended)
- `DailyMenu` - Daily menu for food businesses
- `DailyMenuItem` - Items available in daily menu with quantities

## Migration Steps

### 1. Apply Database Migrations

```bash
cd backend
python manage.py migrate
```

This will:
- Add new fields to the Business model with default values
- Create new tables for services and daily menus
- Set all existing businesses to `product_goods` type by default

### 2. Classify Existing Businesses

Use the management command to automatically classify businesses:

```bash
# Dry run to see what would change
python manage.py migrate_business_types --dry-run

# Apply the migration
python manage.py migrate_business_types
```

#### Manual Classification

For specific businesses, you can set the type manually:

```bash
# Set specific business type
python manage.py migrate_business_types --business-id 1 --business-type service

# Force all businesses to a specific type (use with caution)
python manage.py migrate_business_types --business-type food_restaurant
```

### 3. Configure Business-Specific Settings

After classification, configure business-specific settings:

#### Service Businesses
- Set `booking_advance_days` (default: 30)
- Set `booking_buffer_minutes` (default: 15)
- Configure `service_hours_start` and `service_hours_end`
- Create services using the Services API
- Set up service availability schedules

#### Food Businesses
- Enable `daily_menu_enabled` (default: true after migration)
- Set `preparation_time_minutes` (default: 30)
- Configure `accepts_preorders` (default: true)
- Create daily menus using the Daily Menu API

#### Product Businesses
- No additional configuration needed
- Existing product and inventory management continues to work

## API Changes

### New Endpoints

#### Business Capabilities
- `GET /api/auth/capabilities/` - Get current user's business capabilities
- `GET /api/auth/businesses/{id}/capabilities/` - Get specific business capabilities

#### Services (Service Businesses Only)
- `GET/POST /api/services/` - List/create services
- `GET/PUT/DELETE /api/services/{id}/` - Service detail operations
- `GET /api/services/{id}/available-slots/?date=YYYY-MM-DD` - Get available time slots
- `GET/POST /api/appointments/` - List/create appointments
- `GET/PUT /api/appointments/{id}/` - Appointment detail operations
- `POST /api/appointments/{id}/confirm/` - Confirm appointment
- `POST /api/appointments/{id}/cancel/` - Cancel appointment
- `POST /api/appointments/{id}/reschedule/` - Reschedule appointment

#### Daily Menus (Food Businesses Only)
- `GET/POST /api/orders/daily-menus/` - List/create daily menus
- `GET/PUT/DELETE /api/orders/daily-menus/{id}/` - Daily menu operations
- `GET /api/orders/todays-menu/` - Get today's menu for current business
- `GET /api/orders/businesses/{id}/todays-menu/` - Get today's menu for specific business

#### Enhanced Dashboard
- `GET /api/auth/dashboard/` - Business type-specific dashboard data

### Updated Endpoints

#### Business Serializer
Now includes business type information and capabilities:
- `business_type` and `business_type_display`
- Boolean flags: `is_product_business`, `is_service_business`, `is_food_business`
- Capability flags: `requires_inventory_management`, `supports_appointments`, `supports_daily_menu`

## WhatsApp Integration Updates

### New Commands by Business Type

#### Service Businesses
- `services` - View available services
- `book` - Book an appointment
- `appointments` - View customer appointments
- `reschedule` - Reschedule an appointment

#### Food Businesses
- `specials` - View today's specials
- `today` - View today's menu
- `menu` - View regular menu (falls back to today's menu for food businesses)

#### All Business Types
- Enhanced `help` command shows business type-specific options
- Customized welcome messages based on business type

### New Template Categories
- `appointment_confirmation` - Appointment booking confirmations
- `appointment_reminder` - Appointment reminders
- `appointment_cancelled` - Appointment cancellation notifications
- `appointment_rescheduled` - Appointment rescheduling notifications
- `service_complete` - Service completion notifications
- `menu_update` - Daily menu updates
- `food_order_ready` - Food order ready notifications
- `food_order_delayed` - Food order delay notifications
- `daily_specials` - Daily specials announcements

## Validation and Business Rules

### Business Type Validation
- Product operations are blocked for service businesses
- Service operations are blocked for non-service businesses
- Daily menu operations are blocked for non-food businesses
- Inventory management is only available for product businesses

### Model Validation
- `Product.clean()` prevents creation for service businesses
- `Service.clean()` prevents creation for non-service businesses
- `DailyMenu.clean()` prevents creation for non-food businesses

### API Validation
- Serializers include business type validation
- Views use `@require_business_type` decorator for access control
- Appropriate error messages for invalid operations

## Testing the Migration

### 1. Test Business Classification
```bash
# Check business types after migration
python manage.py shell
>>> from accounts.models import Business
>>> for b in Business.objects.all():
...     print(f"{b.name}: {b.business_type} ({b.get_business_type_display()})")
```

### 2. Test API Endpoints
```bash
# Test capabilities endpoint
curl -H "Authorization: Token YOUR_TOKEN" http://localhost:8000/api/auth/capabilities/

# Test business-specific endpoints based on type
# For service businesses:
curl -H "Authorization: Token YOUR_TOKEN" http://localhost:8000/api/services/

# For food businesses:
curl -H "Authorization: Token YOUR_TOKEN" http://localhost:8000/api/orders/todays-menu/
```

### 3. Test WhatsApp Integration
Send WhatsApp messages to test new commands:
- `help` - Should show business type-specific options
- `services` (service businesses)
- `specials` (food businesses)

## Rollback Plan

If issues arise, you can rollback by:

1. **Revert business types to product_goods:**
```bash
python manage.py migrate_business_types --business-type product_goods
```

2. **Remove new models (if necessary):**
```bash
# This will lose data - use with extreme caution
python manage.py migrate services zero
python manage.py migrate orders 0001
python manage.py migrate accounts 0002
```

## Support and Troubleshooting

### Common Issues

1. **Business type validation errors**
   - Ensure businesses are properly classified
   - Check that operations match business type

2. **Missing services/menus**
   - Service businesses need to create services first
   - Food businesses need to create daily menus

3. **WhatsApp command not working**
   - Verify business type classification
   - Check that new templates are created

### Getting Help

For issues during migration:
1. Check the migration logs for errors
2. Use `--dry-run` flag to preview changes
3. Test with a single business first using `--business-id`
4. Verify API endpoints with appropriate business types

## Post-Migration Tasks

1. **Update documentation** for business owners
2. **Train customer support** on new business types
3. **Create business type-specific templates** in WhatsApp
4. **Set up monitoring** for new endpoints
5. **Update frontend applications** to handle business types
