from django.db import models
from django.conf import settings
from django.core.validators import MinValueValidator
from decimal import Decimal
import uuid


class Product(models.Model):
    """
    Product model for items that businesses can sell.
    """

    class ProductStatus(models.TextChoices):
        ACTIVE = 'active', 'Active'
        INACTIVE = 'inactive', 'Inactive'
        OUT_OF_STOCK = 'out_of_stock', 'Out of Stock'

    business = models.ForeignKey(
        'accounts.Business',
        on_delete=models.CASCADE,
        related_name='products'
    )

    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))]
    )

    # Product details
    sku = models.CharField(max_length=100, blank=True, help_text="Stock Keeping Unit")
    category = models.CharField(max_length=100, blank=True)
    image = models.ImageField(upload_to='products/', null=True, blank=True)

    # Inventory
    stock_quantity = models.PositiveIntegerField(default=0)
    low_stock_threshold = models.PositiveIntegerField(default=5)

    status = models.CharField(
        max_length=15,
        choices=ProductStatus.choices,
        default=ProductStatus.ACTIVE
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} - {self.business.name}"

    @property
    def is_in_stock(self):
        return self.stock_quantity > 0

    @property
    def is_low_stock(self):
        return self.stock_quantity <= self.low_stock_threshold

    @property
    def requires_inventory_tracking(self):
        """Check if this product requires inventory tracking based on business type."""
        return self.business.requires_inventory_management

    @property
    def is_available_for_order(self):
        """Check if product is available for ordering based on business type."""
        if self.status != self.ProductStatus.ACTIVE:
            return False

        # For product businesses, check stock
        if self.business.is_product_business:
            return self.is_in_stock

        # For food businesses, always available (daily menu controls availability)
        # For service businesses, products are not applicable
        return self.business.is_food_business

    def clean(self):
        """Validate that product operations are appropriate for business type."""
        super().clean()
        if self.business and self.business.is_service_business:
            from django.core.exceptions import ValidationError
            raise ValidationError(
                "Service businesses cannot manage products. Use services instead."
            )

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    class Meta:
        unique_together = ['business', 'sku']


class Order(models.Model):
    """
    Order model for customer orders placed through WhatsApp.
    """

    class OrderStatus(models.TextChoices):
        PENDING = 'pending', 'Pending'
        CONFIRMED = 'confirmed', 'Confirmed'
        PROCESSING = 'processing', 'Processing'
        READY_FOR_DELIVERY = 'ready_for_delivery', 'Ready for Delivery'
        OUT_FOR_DELIVERY = 'out_for_delivery', 'Out for Delivery'
        DELIVERED = 'delivered', 'Delivered'
        CANCELLED = 'cancelled', 'Cancelled'
        REFUNDED = 'refunded', 'Refunded'

    # Order identification
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    order_number = models.CharField(max_length=20, unique=True)

    # Relationships
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        limit_choices_to={'role': 'pulse'},
        related_name='orders'
    )
    business = models.ForeignKey(
        'accounts.Business',
        on_delete=models.CASCADE,
        related_name='orders'
    )

    # Order details
    status = models.CharField(
        max_length=20,
        choices=OrderStatus.choices,
        default=OrderStatus.PENDING
    )

    # Pricing
    subtotal = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    delivery_fee = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Delivery information
    delivery_address = models.TextField()
    delivery_phone = models.CharField(max_length=17, blank=True)
    delivery_notes = models.TextField(blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    confirmed_at = models.DateTimeField(null=True, blank=True)
    delivered_at = models.DateTimeField(null=True, blank=True)

    # WhatsApp integration
    whatsapp_message_id = models.CharField(max_length=100, blank=True)

    def __str__(self):
        return f"Order {self.order_number} - {self.customer.username}"

    def save(self, *args, **kwargs):
        if not self.order_number:
            # Generate order number
            import random
            import string
            self.order_number = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))

        # Calculate totals
        self.calculate_totals()
        super().save(*args, **kwargs)

    def calculate_totals(self):
        """Calculate order totals based on order items."""
        self.subtotal = sum(item.total_price for item in self.items.all())
        self.total_amount = self.subtotal + self.delivery_fee

    class Meta:
        ordering = ['-created_at']


class DailyMenu(models.Model):
    """
    Daily menu for food/restaurant businesses.
    """
    business = models.ForeignKey(
        'accounts.Business',
        on_delete=models.CASCADE,
        related_name='daily_menus',
        limit_choices_to={'business_type': 'food_restaurant'}
    )

    date = models.DateField(help_text="Menu date")
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this menu is active for the day"
    )

    # Menu settings
    preparation_time_minutes = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Override business preparation time for this menu"
    )

    notes = models.TextField(
        blank=True,
        help_text="Special notes or announcements for the day"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.business.name} - {self.date}"

    @property
    def effective_preparation_time(self):
        """Get effective preparation time (menu-specific or business default)."""
        return self.preparation_time_minutes or self.business.preparation_time_minutes

    def clean(self):
        """Validate that daily menu operations are appropriate for business type."""
        super().clean()
        if self.business and not self.business.is_food_business:
            from django.core.exceptions import ValidationError
            raise ValidationError(
                "Only food/restaurant businesses can create daily menus."
            )

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    class Meta:
        unique_together = ['business', 'date']
        ordering = ['-date']


class DailyMenuItem(models.Model):
    """
    Items available in a daily menu with per-day availability.
    """
    daily_menu = models.ForeignKey(
        DailyMenu,
        on_delete=models.CASCADE,
        related_name='menu_items'
    )

    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='daily_menu_items'
    )

    # Daily availability
    available_quantity = models.PositiveIntegerField(
        help_text="Quantity available for this day (0 = unlimited)"
    )

    remaining_quantity = models.PositiveIntegerField(
        help_text="Remaining quantity for the day"
    )

    is_available = models.BooleanField(
        default=True,
        help_text="Whether this item is available today"
    )

    # Pricing override
    special_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Special price for today (overrides product price)"
    )

    # Item notes
    notes = models.TextField(
        blank=True,
        help_text="Special notes about this item for today"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.daily_menu.date} - {self.product.name}"

    @property
    def effective_price(self):
        """Get effective price (special price or product price)."""
        return self.special_price or self.product.price

    @property
    def is_sold_out(self):
        """Check if item is sold out for the day."""
        if self.available_quantity == 0:  # 0 means unlimited
            return False
        return self.remaining_quantity <= 0

    @property
    def is_orderable(self):
        """Check if item can be ordered."""
        return self.is_available and not self.is_sold_out

    def reduce_quantity(self, quantity):
        """Reduce remaining quantity when ordered."""
        if self.available_quantity > 0:  # Only reduce if not unlimited
            self.remaining_quantity = max(0, self.remaining_quantity - quantity)
            self.save()

    def save(self, *args, **kwargs):
        # Set remaining quantity to available quantity on creation
        if not self.pk and self.remaining_quantity is None:
            self.remaining_quantity = self.available_quantity
        super().save(*args, **kwargs)

    class Meta:
        unique_together = ['daily_menu', 'product']
        ordering = ['product__name']


class OrderItem(models.Model):
    """
    Individual items within an order.
    """
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)

    quantity = models.PositiveIntegerField(validators=[MinValueValidator(1)])
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    total_price = models.DecimalField(max_digits=10, decimal_places=2)

    # Special instructions for this item
    notes = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.quantity}x {self.product.name} - Order {self.order.order_number}"

    def save(self, *args, **kwargs):
        self.unit_price = self.product.price
        self.total_price = self.unit_price * self.quantity
        super().save(*args, **kwargs)

        # Update order totals
        self.order.calculate_totals()
        self.order.save()

    class Meta:
        unique_together = ['order', 'product']
