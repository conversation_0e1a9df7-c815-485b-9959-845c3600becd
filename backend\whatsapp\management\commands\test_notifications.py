"""
Management command to test the notification system.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from accounts.models import Business
from orders.models import Order
from whatsapp.notifications import notification_manager

User = get_user_model()


class Command(BaseCommand):
    help = 'Test the notification system'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--type',
            type=str,
            choices=[
                'welcome', 'order_created', 'order_confirmed',
                'payment_request', 'payment_verified', 'delivery_assigned'
            ],
            help='Type of notification to test'
        )
        parser.add_argument(
            '--phone',
            type=str,
            help='Phone number to send test notification to'
        )
        parser.add_argument(
            '--business-id',
            type=int,
            help='Business ID for the notification'
        )
    
    def handle(self, *args, **options):
        notification_type = options.get('type')
        phone = options.get('phone')
        business_id = options.get('business_id')
        
        if not phone:
            self.stdout.write(
                self.style.ERROR('Please provide a phone number with --phone')
            )
            return
        
        if not notification_type:
            self.stdout.write(
                self.style.ERROR('Please provide a notification type with --type')
            )
            return
        
        try:
            # Get or create test user
            user, created = User.objects.get_or_create(
                phone_number=phone,
                defaults={
                    'username': f'test_user_{phone.replace("+", "").replace(" ", "")}',
                    'role': 'pulse'
                }
            )
            
            if created:
                self.stdout.write(f'Created test user: {user.username}')
            
            # Get business
            if business_id:
                business = Business.objects.get(id=business_id)
            else:
                business = Business.objects.first()
                if not business:
                    self.stdout.write(
                        self.style.ERROR('No business found. Please create a business first.')
                    )
                    return
            
            self.stdout.write(f'Using business: {business.name}')
            
            # Send test notification
            if notification_type == 'welcome':
                success = notification_manager.send_welcome_message(user, business)
            
            elif notification_type == 'order_created':
                # Create a test order
                order = Order.objects.create(
                    customer=user,
                    business=business,
                    delivery_address='123 Test Street, Test City',
                    delivery_phone=phone,
                    total_amount=100.00
                )
                success = notification_manager.send_order_created(order)
                self.stdout.write(f'Created test order: {order.order_number}')
            
            elif notification_type == 'order_confirmed':
                # Get latest order for user
                order = Order.objects.filter(customer=user, business=business).first()
                if not order:
                    self.stdout.write(
                        self.style.ERROR('No order found for user. Create an order first.')
                    )
                    return
                success = notification_manager.send_order_confirmed(order)
            
            else:
                self.stdout.write(
                    self.style.ERROR(f'Notification type {notification_type} not implemented in test')
                )
                return
            
            if success:
                self.stdout.write(
                    self.style.SUCCESS(f'Successfully sent {notification_type} notification to {phone}')
                )
            else:
                self.stdout.write(
                    self.style.ERROR(f'Failed to send {notification_type} notification')
                )
                
        except Business.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Business with ID {business_id} not found')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error: {str(e)}')
            )
