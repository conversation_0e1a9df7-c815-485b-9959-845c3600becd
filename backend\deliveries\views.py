"""
Views for the deliveries app.
"""

from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.utils import timezone

from .models import DeliveryZone, Delivery, DeliveryUpdate
from .serializers import (
    DeliveryZoneSerializer, DeliveryZoneCreateSerializer,
    DeliverySerializer, DeliveryCreateSerializer, DeliveryAssignmentSerializer,
    DeliveryStatusUpdateSerializer, DeliveryProofUploadSerializer,
    DeliverySummarySerializer, AvailableRunnerSerializer
)
from orders.models import Order
from accounts.models import User
from accounts.permissions import (
    <PERSON><PERSON>od, Is<PERSON><PERSON>ner, IsRunnerOrBusinessOwner,
    IsBusinessOwnerOrReadOnly
)
from whatsapp.services import whatsapp_service


class DeliveryZoneListCreateView(generics.ListCreateAPIView):
    """
    API view for listing and creating delivery zones.
    """
    permission_classes = [permissions.IsAuthenticated, <PERSON>P<PERSON>]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return DeliveryZoneCreateSerializer
        return DeliveryZoneSerializer

    def get_queryset(self):
        user = self.request.user
        business = getattr(user, 'business', None)
        if business:
            return DeliveryZone.objects.filter(business=business)
        return DeliveryZone.objects.none()


class DeliveryZoneDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API view for delivery zone detail, update, and delete.
    """
    serializer_class = DeliveryZoneSerializer
    permission_classes = [permissions.IsAuthenticated, IsBusinessOwnerOrReadOnly]

    def get_queryset(self):
        user = self.request.user
        business = getattr(user, 'business', None)
        if business:
            return DeliveryZone.objects.filter(business=business)
        return DeliveryZone.objects.none()


class DeliveryListView(generics.ListAPIView):
    """
    API view for listing deliveries.
    """
    serializer_class = DeliverySummarySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user

        if user.role == 'forge':
            return Delivery.objects.all()
        elif user.role == 'pod':
            # Business owners can see deliveries for their business
            business = getattr(user, 'business', None)
            if business:
                return Delivery.objects.filter(order__business=business)
        elif user.role == 'pulse':
            # Customers can see their own deliveries
            return Delivery.objects.filter(order__customer=user)
        elif user.role == 'runner':
            # Runners can see deliveries assigned to them
            return Delivery.objects.filter(runner=user)

        return Delivery.objects.none()


class DeliveryDetailView(generics.RetrieveAPIView):
    """
    API view for delivery detail.
    """
    serializer_class = DeliverySerializer
    permission_classes = [permissions.IsAuthenticated, IsRunnerOrBusinessOwner]

    def get_queryset(self):
        user = self.request.user

        if user.role == 'forge':
            return Delivery.objects.all()
        elif user.role == 'pod':
            business = getattr(user, 'business', None)
            if business:
                return Delivery.objects.filter(order__business=business)
        elif user.role == 'pulse':
            return Delivery.objects.filter(order__customer=user)
        elif user.role == 'runner':
            return Delivery.objects.filter(runner=user)

        return Delivery.objects.none()


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated, IsPod])
def create_delivery(request, order_id):
    """
    API endpoint for creating a delivery for an order.
    """
    try:
        business = getattr(request.user, 'business', None)
        order = get_object_or_404(Order, id=order_id, business=business)

        # Check if delivery already exists
        if hasattr(order, 'delivery'):
            return Response({
                'error': 'Delivery already exists for this order'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check order status
        if order.status not in ['confirmed', 'processing', 'ready_for_delivery']:
            return Response({
                'error': 'Delivery can only be created for confirmed, processing, or ready orders'
            }, status=status.HTTP_400_BAD_REQUEST)

        serializer = DeliveryCreateSerializer(
            data=request.data,
            context={'request': request, 'order': order}
        )

        if serializer.is_valid():
            delivery = serializer.save()

            # Update order status
            order.status = 'ready_for_delivery'
            order.save()

            return Response({
                'message': 'Delivery created successfully',
                'delivery': DeliverySerializer(delivery, context={'request': request}).data
            }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    except Order.DoesNotExist:
        return Response({
            'error': 'Order not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated, IsPod])
def assign_delivery(request, delivery_id):
    """
    API endpoint for assigning delivery to a runner.
    """
    try:
        business = getattr(request.user, 'business', None)
        delivery = get_object_or_404(
            Delivery,
            id=delivery_id,
            order__business=business
        )

        if delivery.status != 'pending':
            return Response({
                'error': 'Delivery can only be assigned from pending status'
            }, status=status.HTTP_400_BAD_REQUEST)

        serializer = DeliveryAssignmentSerializer(
            delivery,
            data=request.data,
            context={'request': request}
        )

        if serializer.is_valid():
            delivery = serializer.save()

            # Notify runner
            _notify_runner_assignment(delivery)

            # Notify customer
            _notify_customer_assignment(delivery)

            return Response({
                'message': 'Delivery assigned successfully',
                'delivery': DeliverySerializer(delivery, context={'request': request}).data
            }, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    except Delivery.DoesNotExist:
        return Response({
            'error': 'Delivery not found'
        }, status=status.HTTP_404_NOT_FOUND)


def _notify_runner_assignment(delivery):
    """Notify runner about delivery assignment."""
    try:
        message = f"""
🚚 *New Delivery Assignment*

Delivery: {delivery.delivery_number}
Order: {delivery.order.order_number}
Customer: {delivery.order.customer.get_full_name()}

Pickup: {delivery.pickup_address}
Delivery: {delivery.delivery_address}
Fee: R{delivery.delivery_fee}

Please confirm pickup when ready.
        """.strip()

        whatsapp_service.send_text_message(
            delivery.runner.phone_number,
            message,
            delivery.order.business
        )

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to notify runner: {str(e)}")


def _notify_customer_assignment(delivery):
    """Notify customer about delivery assignment."""
    try:
        message = f"""
🚚 *Delivery Update*

Order: {delivery.order.order_number}
Status: Assigned to delivery agent

Your order is ready for delivery and has been assigned to our delivery agent.
You'll receive updates as your order is on its way.

Estimated delivery: {delivery.estimated_delivery_time or 'TBD'}
        """.strip()

        whatsapp_service.send_text_message(
            delivery.order.customer.phone_number,
            message,
            delivery.order.business
        )

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to notify customer: {str(e)}")


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated, IsRunner])
def update_delivery_status(request, delivery_id):
    """
    API endpoint for updating delivery status (runners only).
    """
    try:
        delivery = get_object_or_404(Delivery, id=delivery_id, runner=request.user)

        serializer = DeliveryStatusUpdateSerializer(
            delivery,
            data=request.data,
            context={'request': request}
        )

        if serializer.is_valid():
            delivery = serializer.save()

            # Send status update notifications
            _send_status_notifications(delivery)

            return Response({
                'message': 'Delivery status updated successfully',
                'delivery': DeliverySerializer(delivery, context={'request': request}).data
            }, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    except Delivery.DoesNotExist:
        return Response({
            'error': 'Delivery not found or not assigned to you'
        }, status=status.HTTP_404_NOT_FOUND)


def _send_status_notifications(delivery):
    """Send delivery status notifications."""
    try:
        status_messages = {
            'picked_up': f"📦 Your order {delivery.order.order_number} has been picked up and is on its way!",
            'in_transit': f"🚚 Your order {delivery.order.order_number} is in transit.",
            'delivered': f"✅ Your order {delivery.order.order_number} has been delivered! Thank you for your business.",
            'failed': f"❌ There was an issue with delivering your order {delivery.order.order_number}. We'll contact you shortly."
        }

        message = status_messages.get(delivery.status)
        if message:
            # Notify customer
            whatsapp_service.send_text_message(
                delivery.order.customer.phone_number,
                message,
                delivery.order.business
            )

            # Notify business owner for important statuses
            if delivery.status in ['delivered', 'failed']:
                business_message = f"""
📋 *Delivery Update*

Order: {delivery.order.order_number}
Customer: {delivery.order.customer.get_full_name()}
Status: {delivery.get_status_display()}
Runner: {delivery.runner.get_full_name()}
                """.strip()

                whatsapp_service.send_text_message(
                    delivery.order.business.owner.phone_number,
                    business_message,
                    delivery.order.business
                )

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to send status notifications: {str(e)}")


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated, IsRunner])
def upload_delivery_proof(request, delivery_id):
    """
    API endpoint for uploading delivery proof (runners only).
    """
    try:
        delivery = get_object_or_404(Delivery, id=delivery_id, runner=request.user)

        serializer = DeliveryProofUploadSerializer(
            data=request.data,
            context={'request': request, 'delivery': delivery}
        )

        if serializer.is_valid():
            proof = serializer.save()

            # Auto-update status to delivered if not already
            if delivery.status != 'delivered':
                delivery.status = 'delivered'
                delivery.delivered_at = timezone.now()
                delivery.save()

                # Create delivery update
                DeliveryUpdate.objects.create(
                    delivery=delivery,
                    status='delivered',
                    message='Delivery completed with proof uploaded',
                    created_by=request.user
                )

                # Send notifications
                _send_status_notifications(delivery)

            return Response({
                'message': 'Delivery proof uploaded successfully',
                'proof': proof.id
            }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    except Delivery.DoesNotExist:
        return Response({
            'error': 'Delivery not found or not assigned to you'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated, IsPod])
def available_runners(request):
    """
    API endpoint for getting available runners.
    """
    runners = User.objects.filter(
        role='runner',
        is_active=True,
        profile__is_available=True
    )

    serializer = AvailableRunnerSerializer(runners, many=True)
    return Response(serializer.data)
