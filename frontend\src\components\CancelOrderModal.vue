<template>
  <div v-if="isOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
    <div class="bg-[#FFFFFF] rounded-2xl shadow-xl max-w-md w-full">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-[#CCCCCC]/30">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-[#C62828]/10 rounded-xl flex items-center justify-center">
            <svg class="w-5 h-5 text-[#C62828]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
            </svg>
          </div>
          <div>
            <h2 class="text-lg font-bold text-[#111111]">Cancel Order</h2>
            <p class="text-sm text-[#4B4B4B]">Order #{{ orderId }}</p>
          </div>
        </div>
        <button @click="closeModal" class="p-2 hover:bg-[#F5F5F5] rounded-xl transition-colors duration-300">
          <svg class="w-5 h-5 text-[#4B4B4B]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>

      <!-- Content -->
      <div class="p-6">
        <div class="mb-6">
          <p class="text-[#4B4B4B] mb-4">
            Are you sure you want to cancel this order? This action cannot be undone.
          </p>
          
          <!-- Cancellation Reason -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-[#111111]">
              Reason for cancellation
            </label>
            <div class="space-y-2">
              <label v-for="reason in cancellationReasons" :key="reason.value" class="flex items-center space-x-3 p-3 bg-[#F5F5F5] rounded-xl hover:bg-[#CCCCCC]/30 transition-colors duration-300 cursor-pointer">
                <input
                  type="radio"
                  :value="reason.value"
                  v-model="selectedReason"
                  class="w-4 h-4 text-[#C62828] bg-[#F5F5F5] border-[#CCCCCC] focus:ring-[#C62828] focus:ring-2"
                />
                <div>
                  <p class="font-medium text-[#111111]">{{ reason.label }}</p>
                  <p class="text-xs text-[#4B4B4B]">{{ reason.description }}</p>
                </div>
              </label>
            </div>
          </div>

          <!-- Custom Reason -->
          <div v-if="selectedReason === 'other'" class="mt-4 space-y-2">
            <label for="customReason" class="block text-sm font-medium text-[#111111]">
              Please specify
            </label>
            <textarea
              id="customReason"
              v-model="customReason"
              rows="3"
              placeholder="Enter the reason for cancellation..."
              class="w-full px-4 py-3 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] placeholder-[#4B4B4B] focus:bg-[#FFFFFF] focus:ring-2 focus:ring-[#C62828]/20 focus:border-[#C62828] transition-all duration-300 outline-none resize-none"
            ></textarea>
          </div>
        </div>

        <!-- Warning Box -->
        <div class="bg-[#C62828]/5 border border-[#C62828]/20 rounded-xl p-4 mb-6">
          <div class="flex items-start space-x-3">
            <svg class="w-5 h-5 text-[#C62828] mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
            </svg>
            <div>
              <p class="text-sm font-medium text-[#C62828]">Important Notice</p>
              <p class="text-xs text-[#C62828]/80 mt-1">
                The customer will be notified immediately about the cancellation. Any payments will be processed for refund according to your refund policy.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex space-x-4 p-6 border-t border-[#CCCCCC]/30 bg-[#F5F5F5]/50">
        <button
          @click="closeModal"
          class="flex-1 px-6 py-3 bg-[#FFFFFF] text-[#4B4B4B] border border-[#CCCCCC]/30 rounded-xl hover:bg-[#F5F5F5] transition-colors duration-300"
        >
          Keep Order
        </button>
        <button
          @click="confirmCancellation"
          :disabled="!selectedReason || isProcessing || (selectedReason === 'other' && !customReason.trim())"
          class="flex-1 px-6 py-3 bg-gradient-to-r from-[#C62828] to-[#B71C1C] text-white rounded-xl hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center"
        >
          <svg v-if="isProcessing" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ isProcessing ? 'Cancelling...' : 'Cancel Order' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  orderId: {
    type: [String, Number],
    default: null
  },
  isProcessing: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'confirm'])

const selectedReason = ref('')
const customReason = ref('')

const cancellationReasons = [
  {
    value: 'out_of_stock',
    label: 'Out of Stock',
    description: 'One or more items are no longer available'
  },
  {
    value: 'customer_request',
    label: 'Customer Request',
    description: 'Customer requested to cancel the order'
  },
  {
    value: 'payment_issue',
    label: 'Payment Issue',
    description: 'Payment could not be processed'
  },
  {
    value: 'delivery_issue',
    label: 'Delivery Issue',
    description: 'Unable to deliver to the specified location'
  },
  {
    value: 'quality_issue',
    label: 'Quality Issue',
    description: 'Product quality does not meet standards'
  },
  {
    value: 'other',
    label: 'Other',
    description: 'Please specify the reason'
  }
]

const closeModal = () => {
  emit('close')
  resetForm()
}

const confirmCancellation = () => {
  const reason = selectedReason.value === 'other' ? customReason.value.trim() : 
                 cancellationReasons.find(r => r.value === selectedReason.value)?.label || selectedReason.value
  
  emit('confirm', {
    orderId: props.orderId,
    reason: reason
  })
}

const resetForm = () => {
  selectedReason.value = ''
  customReason.value = ''
}

// Reset form when modal opens
watch(() => props.isOpen, (newValue) => {
  if (!newValue) {
    resetForm()
  }
})
</script>
