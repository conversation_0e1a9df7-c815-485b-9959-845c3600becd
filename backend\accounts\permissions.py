"""
Custom permissions for the Mthunzi system.
"""

from rest_framework import permissions


class IsForge(permissions.BasePermission):
    """
    Permission class to check if user is a Forge (System Owner).
    """
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role == 'forge'
        )


class IsPod(permissions.BasePermission):
    """
    Permission class to check if user is a Pod (Business Owner).
    """
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role == 'pod'
        )


class IsPulse(permissions.BasePermission):
    """
    Permission class to check if user is a Pulse (Customer).
    """
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role == 'pulse'
        )


class IsRunner(permissions.BasePermission):
    """
    Permission class to check if user is a Runner (Delivery Agent).
    """
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role == 'runner'
        )


class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow owners of an object to edit it.
    """
    
    def has_object_permission(self, request, view, obj):
        # Read permissions are allowed for any request,
        # so we'll always allow GET, HEAD or OPTIONS requests.
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Write permissions are only allowed to the owner of the object.
        return obj.owner == request.user


class IsBusinessOwnerOrReadOnly(permissions.BasePermission):
    """
    Custom permission for business-related objects.
    Only business owners can modify their business data.
    """
    
    def has_object_permission(self, request, view, obj):
        # Read permissions for authenticated users
        if request.method in permissions.SAFE_METHODS:
            return request.user.is_authenticated
        
        # Write permissions only for business owner
        if hasattr(obj, 'business'):
            return obj.business.owner == request.user
        elif hasattr(obj, 'owner'):
            return obj.owner == request.user
        
        return False


class IsCustomerOrBusinessOwner(permissions.BasePermission):
    """
    Permission for order-related objects.
    Customers can access their own orders, business owners can access orders for their business.
    """
    
    def has_object_permission(self, request, view, obj):
        user = request.user
        
        if not user.is_authenticated:
            return False
        
        # If it's an order object
        if hasattr(obj, 'customer') and hasattr(obj, 'business'):
            # Customer can access their own orders
            if user.role == 'pulse' and obj.customer == user:
                return True
            # Business owner can access orders for their business
            if user.role == 'pod' and obj.business.owner == user:
                return True
        
        return False


class IsRunnerOrBusinessOwner(permissions.BasePermission):
    """
    Permission for delivery-related objects.
    Runners can access deliveries assigned to them, business owners can access deliveries for their business.
    """
    
    def has_object_permission(self, request, view, obj):
        user = request.user
        
        if not user.is_authenticated:
            return False
        
        # If it's a delivery object
        if hasattr(obj, 'runner') and hasattr(obj, 'order'):
            # Runner can access deliveries assigned to them
            if user.role == 'runner' and obj.runner == user:
                return True
            # Business owner can access deliveries for their business orders
            if user.role == 'pod' and obj.order.business.owner == user:
                return True
        
        return False


class CanManageUsers(permissions.BasePermission):
    """
    Permission to manage users. Only Forge users can manage all users.
    """
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role == 'forge'
        )


class CanManageBusinesses(permissions.BasePermission):
    """
    Permission to manage businesses. Forge can manage all, Pod can manage their own.
    """
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role in ['forge', 'pod']
        )
    
    def has_object_permission(self, request, view, obj):
        user = request.user
        
        if user.role == 'forge':
            return True
        elif user.role == 'pod':
            return obj.owner == user
        
        return False
