#!/usr/bin/env python
"""
Test runner script for the Mthunzi project.
"""

import os
import sys
import django
from django.conf import settings
from django.test.utils import get_runner


def run_tests():
    """Run all tests for the Mthunzi project."""
    
    # Setup Django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mthunzi.settings')
    django.setup()
    
    # Get test runner
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    
    # Define test modules to run
    test_modules = [
        'accounts.tests',
        'orders.tests',
        'payments.tests',
        'deliveries.tests',
        'whatsapp.tests',
    ]
    
    print("🧪 Running Mthunzi Test Suite")
    print("=" * 50)
    
    # Run tests
    failures = test_runner.run_tests(test_modules)
    
    if failures:
        print(f"\n❌ {failures} test(s) failed!")
        sys.exit(1)
    else:
        print("\n✅ All tests passed!")
        sys.exit(0)


if __name__ == '__main__':
    run_tests()
