"""
Views for the accounts app.
"""

from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.authtoken.models import Token
from django.contrib.auth import login, logout
from django.utils import timezone

from .models import User, Business, UserProfile, SubscriptionPlan, BusinessSubscription, SubscriptionPayment, SubscriptionPaymentProof
from .serializers import (
    UserRegistrationSerializer, UserLoginSerializer, UserSerializer,
    BusinessSerializer, BusinessCreateSerializer, UserProfileSerializer,
    PasswordChangeSerializer, PhoneVerificationSerializer,
    SubscriptionPlanSerializer, BusinessSubscriptionSerializer,
    SubscriptionPaymentSerializer, SubscriptionPaymentApprovalSerializer,
    SubscriptionPaymentProofSerializer
)
from .permissions import (
    IsPod, IsForge, CanManageBusinesses, IsBusinessOwnerOrReadOnly
)


class UserRegistrationView(generics.CreateAPIView):
    """
    API view for user registration.
    """
    queryset = User.objects.all()
    serializer_class = UserRegistrationSerializer
    permission_classes = [permissions.AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()

        # Create authentication token
        token, created = Token.objects.get_or_create(user=user)

        return Response({
            'user': UserSerializer(user).data,
            'token': token.key,
            'message': 'User registered successfully'
        }, status=status.HTTP_201_CREATED)


class UserLoginView(generics.GenericAPIView):
    """
    API view for user login.
    """
    serializer_class = UserLoginSerializer
    permission_classes = [permissions.AllowAny]

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.validated_data['user']

        # Create or get authentication token
        token, created = Token.objects.get_or_create(user=user)

        # Login user
        login(request, user)

        return Response({
            'user': UserSerializer(user).data,
            'token': token.key,
            'message': 'Login successful'
        }, status=status.HTTP_200_OK)


class UserLogoutView(generics.GenericAPIView):
    """
    API view for user logout.
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, *args, **kwargs):
        # Delete the user's token
        try:
            token = Token.objects.get(user=request.user)
            token.delete()
        except Token.DoesNotExist:
            pass

        # Logout user
        logout(request)

        return Response({
            'message': 'Logout successful'
        }, status=status.HTTP_200_OK)


class UserProfileView(generics.RetrieveUpdateAPIView):
    """
    API view for user profile management.
    """
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        return self.request.user


class UserProfileDetailView(generics.RetrieveUpdateAPIView):
    """
    API view for detailed user profile management.
    """
    serializer_class = UserProfileSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        profile, created = UserProfile.objects.get_or_create(user=self.request.user)
        return profile


class PasswordChangeView(generics.GenericAPIView):
    """
    API view for changing password.
    """
    serializer_class = PasswordChangeSerializer
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response({
            'message': 'Password changed successfully'
        }, status=status.HTTP_200_OK)


class PhoneVerificationView(generics.GenericAPIView):
    """
    API view for phone number verification.
    """
    serializer_class = PhoneVerificationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Here you would implement actual verification logic
        # For now, we'll just mark the user as verified
        user = request.user
        user.is_verified = True
        user.save()

        return Response({
            'message': 'Phone number verified successfully'
        }, status=status.HTTP_200_OK)


class BusinessListCreateView(generics.ListCreateAPIView):
    """
    API view for listing and creating businesses.
    """
    queryset = Business.objects.all()
    permission_classes = [permissions.IsAuthenticated, CanManageBusinesses]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return BusinessCreateSerializer
        return BusinessSerializer

    def get_queryset(self):
        user = self.request.user
        if user.role == 'forge':
            return Business.objects.all()
        elif user.role == 'pod':
            return Business.objects.filter(owner=user)
        return Business.objects.none()


class BusinessDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API view for business detail, update, and delete.
    """
    queryset = Business.objects.all()
    serializer_class = BusinessSerializer
    permission_classes = [permissions.IsAuthenticated, IsBusinessOwnerOrReadOnly]


class UserListView(generics.ListAPIView):
    """
    API view for listing users (admin only).
    """
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        if user.role == 'forge':
            return User.objects.all()
        elif user.role == 'pod':
            # Business owners can see their customers and runners
            business = getattr(user, 'business', None)
            if business:
                customer_ids = business.orders.values_list('customer_id', flat=True)
                runner_ids = business.orders.filter(
                    delivery__runner__isnull=False
                ).values_list('delivery__runner_id', flat=True)
                return User.objects.filter(
                    id__in=list(customer_ids) + list(runner_ids)
                ).distinct()
        return User.objects.filter(id=user.id)


class RunnerListView(generics.ListAPIView):
    """
    API view for listing available runners.
    """
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated, IsPod]

    def get_queryset(self):
        return User.objects.filter(
            role='runner',
            is_active=True,
            profile__is_available=True
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated, IsPod])
def business_dashboard(request):
    """
    API endpoint for business dashboard data based on business type.
    """
    try:
        business = getattr(request.user, 'business', None)
        if not business:
            return Response({
                'error': 'No business found for this user'
            }, status=status.HTTP_404_NOT_FOUND)

        # Base dashboard data
        dashboard_data = {
            'business': BusinessSerializer(business).data,
            'business_type': business.business_type,
            'business_type_display': business.get_business_type_display(),
        }

        # Add business type-specific data
        if business.is_product_business:
            dashboard_data.update(_get_product_business_dashboard(business))
        elif business.is_service_business:
            dashboard_data.update(_get_service_business_dashboard(business))
        elif business.is_food_business:
            dashboard_data.update(_get_food_business_dashboard(business))

        return Response(dashboard_data)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def _get_product_business_dashboard(business):
    """Get dashboard data for product businesses."""
    from orders.models import Product, Order
    from django.utils import timezone
    from datetime import timedelta
    from django.db.models import Sum, F

    today = timezone.now().date()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)

    # Product metrics
    total_products = Product.objects.filter(business=business).count()
    active_products = Product.objects.filter(business=business, status='active').count()
    low_stock_products = Product.objects.filter(
        business=business,
        status='active'
    ).filter(stock_quantity__lte=F('low_stock_threshold')).count()

    # Order metrics
    total_orders = Order.objects.filter(business=business).count()
    orders_today = Order.objects.filter(business=business, created_at__date=today).count()
    orders_this_week = Order.objects.filter(business=business, created_at__date__gte=week_ago).count()
    orders_this_month = Order.objects.filter(business=business, created_at__date__gte=month_ago).count()

    # Revenue metrics
    revenue_today = Order.objects.filter(
        business=business,
        created_at__date=today,
        status__in=['confirmed', 'processing', 'delivered']
    ).aggregate(total=Sum('total_amount'))['total'] or 0

    revenue_this_week = Order.objects.filter(
        business=business,
        created_at__date__gte=week_ago,
        status__in=['confirmed', 'processing', 'delivered']
    ).aggregate(total=Sum('total_amount'))['total'] or 0

    revenue_this_month = Order.objects.filter(
        business=business,
        created_at__date__gte=month_ago,
        status__in=['confirmed', 'processing', 'delivered']
    ).aggregate(total=Sum('total_amount'))['total'] or 0

    return {
        'dashboard_type': 'product_business',
        'metrics': {
            'products': {
                'total': total_products,
                'active': active_products,
                'low_stock': low_stock_products,
            },
            'orders': {
                'total': total_orders,
                'today': orders_today,
                'this_week': orders_this_week,
                'this_month': orders_this_month,
            },
            'revenue': {
                'today': float(revenue_today),
                'this_week': float(revenue_this_week),
                'this_month': float(revenue_this_month),
            }
        },
        'quick_actions': [
            {'name': 'Add Product', 'url': '/api/orders/products/', 'method': 'POST'},
            {'name': 'View Orders', 'url': '/api/orders/', 'method': 'GET'},
            {'name': 'Manage Inventory', 'url': '/api/orders/products/', 'method': 'GET'},
        ]
    }


def _get_service_business_dashboard(business):
    """Get dashboard data for service businesses."""
    try:
        from services.models import Service, Appointment
    except ImportError:
        # Services app not available
        return {
            'dashboard_type': 'service_business',
            'metrics': {'error': 'Services module not available'},
            'quick_actions': []
        }

    from django.utils import timezone
    from datetime import timedelta
    from django.db.models import Sum

    today = timezone.now().date()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)

    # Service metrics
    total_services = Service.objects.filter(business=business).count()
    active_services = Service.objects.filter(business=business, status='active').count()

    # Appointment metrics
    total_appointments = Appointment.objects.filter(business=business).count()
    appointments_today = Appointment.objects.filter(
        business=business,
        appointment_datetime__date=today
    ).count()
    appointments_this_week = Appointment.objects.filter(
        business=business,
        appointment_datetime__date__gte=week_ago
    ).count()
    appointments_this_month = Appointment.objects.filter(
        business=business,
        appointment_datetime__date__gte=month_ago
    ).count()

    # Status breakdown
    pending_appointments = Appointment.objects.filter(
        business=business,
        status='pending',
        appointment_datetime__gte=timezone.now()
    ).count()

    confirmed_appointments = Appointment.objects.filter(
        business=business,
        status='confirmed',
        appointment_datetime__gte=timezone.now()
    ).count()

    # Revenue metrics
    revenue_today = Appointment.objects.filter(
        business=business,
        appointment_datetime__date=today,
        status__in=['confirmed', 'completed']
    ).aggregate(total=Sum('service_price'))['total'] or 0

    revenue_this_week = Appointment.objects.filter(
        business=business,
        appointment_datetime__date__gte=week_ago,
        status__in=['confirmed', 'completed']
    ).aggregate(total=Sum('service_price'))['total'] or 0

    revenue_this_month = Appointment.objects.filter(
        business=business,
        appointment_datetime__date__gte=month_ago,
        status__in=['confirmed', 'completed']
    ).aggregate(total=Sum('service_price'))['total'] or 0

    return {
        'dashboard_type': 'service_business',
        'metrics': {
            'services': {
                'total': total_services,
                'active': active_services,
            },
            'appointments': {
                'total': total_appointments,
                'today': appointments_today,
                'this_week': appointments_this_week,
                'this_month': appointments_this_month,
                'pending': pending_appointments,
                'confirmed': confirmed_appointments,
            },
            'revenue': {
                'today': float(revenue_today),
                'this_week': float(revenue_this_week),
                'this_month': float(revenue_this_month),
            }
        },
        'quick_actions': [
            {'name': 'Add Service', 'url': '/api/services/', 'method': 'POST'},
            {'name': 'View Appointments', 'url': '/api/appointments/', 'method': 'GET'},
            {'name': 'Manage Schedule', 'url': '/api/services/', 'method': 'GET'},
        ]
    }


def _get_food_business_dashboard(business):
    """Get dashboard data for food businesses."""
    from orders.models import Product, Order, DailyMenu, DailyMenuItem
    from django.utils import timezone
    from datetime import timedelta
    from django.db.models import Sum

    today = timezone.now().date()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)

    # Menu metrics
    total_products = Product.objects.filter(business=business).count()
    active_products = Product.objects.filter(business=business, status='active').count()

    # Today's menu
    todays_menu = DailyMenu.objects.filter(
        business=business,
        date=today,
        is_active=True
    ).first()

    todays_menu_items = 0
    available_items = 0
    if todays_menu:
        todays_menu_items = todays_menu.menu_items.count()
        available_items = todays_menu.menu_items.filter(is_available=True).count()

    # Order metrics
    total_orders = Order.objects.filter(business=business).count()
    orders_today = Order.objects.filter(business=business, created_at__date=today).count()
    orders_this_week = Order.objects.filter(business=business, created_at__date__gte=week_ago).count()
    orders_this_month = Order.objects.filter(business=business, created_at__date__gte=month_ago).count()

    # Revenue metrics
    revenue_today = Order.objects.filter(
        business=business,
        created_at__date=today,
        status__in=['confirmed', 'processing', 'delivered']
    ).aggregate(total=Sum('total_amount'))['total'] or 0

    revenue_this_week = Order.objects.filter(
        business=business,
        created_at__date__gte=week_ago,
        status__in=['confirmed', 'processing', 'delivered']
    ).aggregate(total=Sum('total_amount'))['total'] or 0

    revenue_this_month = Order.objects.filter(
        business=business,
        created_at__date__gte=month_ago,
        status__in=['confirmed', 'processing', 'delivered']
    ).aggregate(total=Sum('total_amount'))['total'] or 0

    return {
        'dashboard_type': 'food_business',
        'metrics': {
            'menu': {
                'total_products': total_products,
                'active_products': active_products,
                'todays_menu_items': todays_menu_items,
                'available_items': available_items,
                'has_todays_menu': todays_menu is not None,
            },
            'orders': {
                'total': total_orders,
                'today': orders_today,
                'this_week': orders_this_week,
                'this_month': orders_this_month,
            },
            'revenue': {
                'today': float(revenue_today),
                'this_week': float(revenue_this_week),
                'this_month': float(revenue_this_month),
            }
        },
        'quick_actions': [
            {'name': 'Create Daily Menu', 'url': '/api/orders/daily-menus/', 'method': 'POST'},
            {'name': 'View Orders', 'url': '/api/orders/', 'method': 'GET'},
            {'name': 'Manage Products', 'url': '/api/orders/products/', 'method': 'GET'},
            {'name': "Today's Menu", 'url': '/api/orders/todays-menu/', 'method': 'GET'},
        ]
    }


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def business_capabilities(request, business_id=None):
    """
    API endpoint to get business capabilities based on business type.
    """
    try:
        if business_id:
            # Get specific business (for customers)
            business = Business.objects.get(id=business_id)
        else:
            # Get current user's business
            business = getattr(request.user, 'business', None)
            if not business:
                return Response({
                    'error': 'No business found for this user'
                }, status=status.HTTP_404_NOT_FOUND)

        capabilities = {
            'business_id': business.id,
            'business_name': business.name,
            'business_type': business.business_type,
            'business_type_display': business.get_business_type_display(),
            'capabilities': {
                'supports_products': business.is_product_business or business.is_food_business,
                'supports_inventory': business.requires_inventory_management,
                'supports_services': business.supports_appointments,
                'supports_appointments': business.supports_appointments,
                'supports_daily_menu': business.supports_daily_menu,
                'supports_delivery': business.is_product_business or business.is_food_business,
                'supports_whatsapp_ordering': True,
            },
            'features': []
        }

        # Add business type-specific features
        if business.is_product_business:
            capabilities['features'].extend([
                'product_catalog',
                'inventory_management',
                'stock_alerts',
                'order_management',
                'delivery_tracking'
            ])
        elif business.is_service_business:
            capabilities['features'].extend([
                'service_catalog',
                'appointment_booking',
                'schedule_management',
                'customer_management',
                'service_history'
            ])
        elif business.is_food_business:
            capabilities['features'].extend([
                'menu_management',
                'daily_menu',
                'order_management',
                'preparation_tracking',
                'special_offers'
            ])

        return Response(capabilities)

    except Business.DoesNotExist:
        return Response({
            'error': 'Business not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Subscription Management Views

class SubscriptionPlanListView(generics.ListAPIView):
    """
    API view for listing subscription plans.
    """
    queryset = SubscriptionPlan.objects.filter(is_active=True)
    serializer_class = SubscriptionPlanSerializer
    permission_classes = [permissions.IsAuthenticated]


class BusinessSubscriptionListView(generics.ListAPIView):
    """
    API view for listing business subscriptions (Forge only).
    """
    serializer_class = BusinessSubscriptionSerializer
    permission_classes = [permissions.IsAuthenticated, IsForge]

    def get_queryset(self):
        queryset = BusinessSubscription.objects.select_related('business', 'plan').all()

        # Filter by status if provided
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by business if provided
        business_id = self.request.query_params.get('business_id')
        if business_id:
            queryset = queryset.filter(business_id=business_id)

        return queryset


class BusinessSubscriptionDetailView(generics.RetrieveUpdateAPIView):
    """
    API view for retrieving and updating business subscriptions (Forge only).
    """
    queryset = BusinessSubscription.objects.select_related('business', 'plan').all()
    serializer_class = BusinessSubscriptionSerializer
    permission_classes = [permissions.IsAuthenticated, IsForge]


class SubscriptionPaymentListView(generics.ListAPIView):
    """
    API view for listing subscription payments (Forge only).
    """
    serializer_class = SubscriptionPaymentSerializer
    permission_classes = [permissions.IsAuthenticated, IsForge]

    def get_queryset(self):
        queryset = SubscriptionPayment.objects.select_related(
            'subscription__business', 'subscription__plan', 'approved_by'
        ).prefetch_related('proofs').all()

        # Filter by status if provided
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by business if provided
        business_id = self.request.query_params.get('business_id')
        if business_id:
            queryset = queryset.filter(subscription__business_id=business_id)

        return queryset


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated, IsForge])
def approve_subscription_payment(request, payment_id):
    """
    Approve or reject a subscription payment.
    """
    try:
        payment = SubscriptionPayment.objects.select_related(
            'subscription__business', 'subscription__plan'
        ).get(id=payment_id)

        serializer = SubscriptionPaymentApprovalSerializer(
            payment, data=request.data, partial=True
        )

        if serializer.is_valid():
            # Update payment status
            payment.status = serializer.validated_data['status']
            payment.approval_notes = serializer.validated_data.get('approval_notes', '')
            payment.approved_by = request.user

            if payment.status == SubscriptionPayment.PaymentStatus.APPROVED:
                payment.approved_at = timezone.now()

                # Activate or extend subscription
                subscription = payment.subscription
                if subscription.status == BusinessSubscription.SubscriptionStatus.PENDING:
                    subscription.status = BusinessSubscription.SubscriptionStatus.ACTIVE
                    subscription.activated_at = timezone.now()

                    # Set subscription dates
                    if subscription.billing_cycle == BusinessSubscription.BillingCycle.MONTHLY:
                        subscription.end_date = timezone.now() + timezone.timedelta(days=30)
                        subscription.next_billing_date = subscription.end_date
                    else:  # Yearly
                        subscription.end_date = timezone.now() + timezone.timedelta(days=365)
                        subscription.next_billing_date = subscription.end_date

                    subscription.save()

                # Update business status to active
                business = subscription.business
                if business.status != Business.BusinessStatus.ACTIVE:
                    business.status = Business.BusinessStatus.ACTIVE
                    business.save()

            payment.save()

            return Response({
                'message': f'Payment {payment.status}',
                'payment': SubscriptionPaymentSerializer(payment).data
            })

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    except SubscriptionPayment.DoesNotExist:
        return Response({
            'error': 'Payment not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated, IsForge])
def subscription_reports(request):
    """
    Get subscription reports and statistics for Forge dashboard.
    """
    try:
        from django.db.models import Count, Sum, Q
        from datetime import datetime, timedelta

        # Date range for analytics (last 30 days)
        end_date = timezone.now()
        start_date = end_date - timedelta(days=30)

        # Subscription statistics
        total_subscriptions = BusinessSubscription.objects.count()
        active_subscriptions = BusinessSubscription.objects.filter(
            status=BusinessSubscription.SubscriptionStatus.ACTIVE
        ).count()
        pending_subscriptions = BusinessSubscription.objects.filter(
            status=BusinessSubscription.SubscriptionStatus.PENDING
        ).count()
        expired_subscriptions = BusinessSubscription.objects.filter(
            status=BusinessSubscription.SubscriptionStatus.EXPIRED
        ).count()

        # Payment statistics
        total_payments = SubscriptionPayment.objects.count()
        pending_payments = SubscriptionPayment.objects.filter(
            status=SubscriptionPayment.PaymentStatus.SUBMITTED
        ).count()
        approved_payments = SubscriptionPayment.objects.filter(
            status=SubscriptionPayment.PaymentStatus.APPROVED
        ).count()

        # Revenue statistics
        monthly_revenue = SubscriptionPayment.objects.filter(
            status=SubscriptionPayment.PaymentStatus.APPROVED,
            approved_at__gte=start_date
        ).aggregate(total=Sum('amount'))['total'] or 0

        # Recent activity
        recent_payments = SubscriptionPayment.objects.filter(
            created_at__gte=start_date
        ).select_related(
            'subscription__business', 'subscription__plan'
        ).order_by('-created_at')[:10]

        # Subscription by plan type
        plan_distribution = SubscriptionPlan.objects.annotate(
            subscription_count=Count('subscriptions')
        ).values('name', 'plan_type', 'subscription_count')

        return Response({
            'subscription_stats': {
                'total_subscriptions': total_subscriptions,
                'active_subscriptions': active_subscriptions,
                'pending_subscriptions': pending_subscriptions,
                'expired_subscriptions': expired_subscriptions,
            },
            'payment_stats': {
                'total_payments': total_payments,
                'pending_payments': pending_payments,
                'approved_payments': approved_payments,
            },
            'revenue_stats': {
                'monthly_revenue': float(monthly_revenue),
            },
            'recent_payments': SubscriptionPaymentSerializer(recent_payments, many=True).data,
            'plan_distribution': list(plan_distribution),
        })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
