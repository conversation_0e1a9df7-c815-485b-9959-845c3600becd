"""
Tests for the orders app.
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from rest_framework.authtoken.models import Token
from decimal import Decimal

from .models import Product, Order, OrderItem
from accounts.models import Business

User = get_user_model()


class ProductModelTest(TestCase):
    """Test the Product model."""

    def setUp(self):
        self.pod_user = User.objects.create_user(
            username='business_owner',
            phone_number='+***********',
            role='pod'
        )
        self.business = Business.objects.create(
            owner=self.pod_user,
            name='Test Business'
        )

        self.product_data = {
            'business': self.business,
            'name': 'Test Product',
            'description': 'A test product',
            'price': Decimal('99.99'),
            'sku': 'TEST001',
            'stock_quantity': 10,
            'low_stock_threshold': 5
        }

    def test_create_product(self):
        """Test creating a product."""
        product = Product.objects.create(**self.product_data)

        self.assertEqual(product.name, 'Test Product')
        self.assertEqual(product.price, Decimal('99.99'))
        self.assertEqual(product.business, self.business)
        self.assertEqual(product.status, 'active')  # Default status
        self.assertTrue(product.is_in_stock)
        self.assertFalse(product.is_low_stock)

    def test_product_low_stock(self):
        """Test product low stock detection."""
        product = Product.objects.create(**self.product_data)
        product.stock_quantity = 3  # Below threshold of 5
        product.save()

        self.assertTrue(product.is_low_stock)
        self.assertTrue(product.is_in_stock)  # Still in stock, just low

    def test_product_out_of_stock(self):
        """Test product out of stock detection."""
        product = Product.objects.create(**self.product_data)
        product.stock_quantity = 0
        product.save()

        self.assertFalse(product.is_in_stock)
        self.assertTrue(product.is_low_stock)  # 0 is <= threshold

    def test_product_str_representation(self):
        """Test product string representation."""
        product = Product.objects.create(**self.product_data)
        expected = f"{product.name} - {product.business.name}"
        self.assertEqual(str(product), expected)


class OrderModelTest(TestCase):
    """Test the Order model."""

    def setUp(self):
        # Create business owner and business
        self.pod_user = User.objects.create_user(
            username='business_owner',
            phone_number='+***********',
            role='pod'
        )
        self.business = Business.objects.create(
            owner=self.pod_user,
            name='Test Business'
        )

        # Create customer
        self.customer = User.objects.create_user(
            username='customer',
            phone_number='+***********',
            role='pulse'
        )

        # Create product
        self.product = Product.objects.create(
            business=self.business,
            name='Test Product',
            price=Decimal('50.00'),
            stock_quantity=10
        )

        self.order_data = {
            'customer': self.customer,
            'business': self.business,
            'delivery_address': '123 Test St, Test City',
            'delivery_phone': '+***********'
        }

    def test_create_order(self):
        """Test creating an order."""
        order = Order.objects.create(**self.order_data)

        self.assertEqual(order.customer, self.customer)
        self.assertEqual(order.business, self.business)
        self.assertEqual(order.status, 'pending')  # Default status
        self.assertIsNotNone(order.order_number)  # Auto-generated
        self.assertEqual(order.total_amount, 0)  # No items yet

    def test_order_with_items(self):
        """Test order with items and total calculation."""
        order = Order.objects.create(**self.order_data)

        # Add order item
        OrderItem.objects.create(
            order=order,
            product=self.product,
            quantity=2
        )

        order.refresh_from_db()
        self.assertEqual(order.subtotal, Decimal('100.00'))  # 2 * 50.00
        self.assertEqual(order.total_amount, Decimal('100.00'))  # No delivery fee

    def test_order_str_representation(self):
        """Test order string representation."""
        order = Order.objects.create(**self.order_data)
        expected = f"Order {order.order_number} - {order.customer.username}"
        self.assertEqual(str(order), expected)


class OrderItemModelTest(TestCase):
    """Test the OrderItem model."""

    def setUp(self):
        # Create necessary objects
        self.pod_user = User.objects.create_user(
            username='business_owner',
            phone_number='+***********',
            role='pod'
        )
        self.business = Business.objects.create(
            owner=self.pod_user,
            name='Test Business'
        )
        self.customer = User.objects.create_user(
            username='customer',
            phone_number='+***********',
            role='pulse'
        )
        self.product = Product.objects.create(
            business=self.business,
            name='Test Product',
            price=Decimal('25.00'),
            stock_quantity=10
        )
        self.order = Order.objects.create(
            customer=self.customer,
            business=self.business,
            delivery_address='123 Test St'
        )

    def test_create_order_item(self):
        """Test creating an order item."""
        item = OrderItem.objects.create(
            order=self.order,
            product=self.product,
            quantity=3
        )

        self.assertEqual(item.quantity, 3)
        self.assertEqual(item.unit_price, Decimal('25.00'))  # From product
        self.assertEqual(item.total_price, Decimal('75.00'))  # 3 * 25.00

    def test_order_item_str_representation(self):
        """Test order item string representation."""
        item = OrderItem.objects.create(
            order=self.order,
            product=self.product,
            quantity=2
        )
        expected = f"2x {self.product.name} - Order {self.order.order_number}"
        self.assertEqual(str(item), expected)
