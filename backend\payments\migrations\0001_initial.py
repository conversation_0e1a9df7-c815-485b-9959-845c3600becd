# Generated by Django 5.2.4 on 2025-07-08 14:13

import django.core.validators
import django.db.models.deletion
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
        ('orders', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentMethod',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('method_type', models.CharField(choices=[('bank_transfer', 'Bank Transfer'), ('mobile_money', 'Mobile Money'), ('cash_on_delivery', 'Cash on Delivery'), ('card', 'Card Payment')], max_length=20)),
                ('name', models.Char<PERSON>ield(help_text='Display name for this payment method', max_length=100)),
                ('account_name', models.CharField(blank=True, max_length=200)),
                ('account_number', models.CharField(blank=True, max_length=100)),
                ('bank_name', models.CharField(blank=True, max_length=100)),
                ('mobile_number', models.CharField(blank=True, max_length=17)),
                ('instructions', models.TextField(blank=True, help_text='Instructions to show customers for this payment method')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_methods', to='accounts.business')),
            ],
            options={
                'unique_together': {('business', 'method_type', 'name')},
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('payment_reference', models.CharField(max_length=50, unique=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('submitted', 'Proof Submitted'), ('verified', 'Verified'), ('rejected', 'Rejected'), ('refunded', 'Refunded')], default='pending', max_length=10)),
                ('customer_reference', models.CharField(blank=True, help_text='Reference number provided by customer', max_length=100)),
                ('customer_notes', models.TextField(blank=True, help_text='Additional notes from customer about payment')),
                ('verification_notes', models.TextField(blank=True, help_text='Notes from business owner during verification')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('submitted_at', models.DateTimeField(blank=True, null=True)),
                ('verified_at', models.DateTimeField(blank=True, null=True)),
                ('order', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='payment', to='orders.order')),
                ('verified_by', models.ForeignKey(blank=True, limit_choices_to={'role': 'pod'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_payments', to=settings.AUTH_USER_MODEL)),
                ('payment_method', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='payments.paymentmethod')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PaymentProof',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('proof_type', models.CharField(choices=[('screenshot', 'Screenshot'), ('receipt', 'Receipt'), ('bank_statement', 'Bank Statement'), ('other', 'Other')], default='screenshot', max_length=15)),
                ('file', models.FileField(help_text='Upload proof of payment (image or PDF)', upload_to='payment_proofs/')),
                ('description', models.CharField(blank=True, help_text='Brief description of the proof', max_length=200)),
                ('whatsapp_media_id', models.CharField(blank=True, max_length=100)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('payment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='proofs', to='payments.payment')),
            ],
            options={
                'ordering': ['-uploaded_at'],
            },
        ),
    ]
