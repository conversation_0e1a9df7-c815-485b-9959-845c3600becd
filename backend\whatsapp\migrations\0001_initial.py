# Generated by Django 5.2.4 on 2025-07-08 14:13

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
        ('orders', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ChatSession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.CharField(choices=[('active', 'Active'), ('closed', 'Closed'), ('archived', 'Archived')], default='active', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('closed_at', models.DateTimeField(blank=True, null=True)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='chat_sessions', to='accounts.business')),
                ('customer', models.ForeignKey(limit_choices_to={'role': 'pulse'}, on_delete=django.db.models.deletion.CASCADE, related_name='chat_sessions', to=settings.AUTH_USER_MODEL)),
                ('order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='chat_sessions', to='orders.order')),
            ],
            options={
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='WhatsAppMessage',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('whatsapp_message_id', models.CharField(max_length=100, unique=True)),
                ('sender_phone', models.CharField(blank=True, max_length=17)),
                ('recipient_phone', models.CharField(blank=True, max_length=17)),
                ('message_type', models.CharField(choices=[('text', 'Text'), ('image', 'Image'), ('document', 'Document'), ('audio', 'Audio'), ('video', 'Video'), ('location', 'Location'), ('contact', 'Contact'), ('interactive', 'Interactive')], max_length=15)),
                ('direction', models.CharField(choices=[('inbound', 'Inbound (Received)'), ('outbound', 'Outbound (Sent)')], max_length=10)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('sent', 'Sent'), ('delivered', 'Delivered'), ('read', 'Read'), ('failed', 'Failed')], default='pending', max_length=10)),
                ('content', models.TextField(blank=True, help_text='Text content of the message')),
                ('media_url', models.URLField(blank=True, help_text='URL to media file')),
                ('media_id', models.CharField(blank=True, max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('delivered_at', models.DateTimeField(blank=True, null=True)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('business', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='whatsapp_messages', to='accounts.business')),
                ('order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='whatsapp_messages', to='orders.order')),
                ('recipient', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='received_messages', to=settings.AUTH_USER_MODEL)),
                ('reply_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='replies', to='whatsapp.whatsappmessage')),
                ('sender', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sent_messages', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WhatsAppWebhook',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('message', 'Message'), ('status', 'Status Update'), ('error', 'Error'), ('other', 'Other')], max_length=10)),
                ('raw_data', models.JSONField(help_text='Raw webhook payload')),
                ('processed', models.BooleanField(default=False)),
                ('processing_error', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('message', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='webhook_events', to='whatsapp.whatsappmessage')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WhatsAppTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('category', models.CharField(choices=[('welcome', 'Welcome Message'), ('order_confirmation', 'Order Confirmation'), ('payment_request', 'Payment Request'), ('payment_confirmation', 'Payment Confirmation'), ('delivery_update', 'Delivery Update'), ('order_complete', 'Order Complete'), ('general', 'General')], max_length=20)),
                ('content', models.TextField(help_text='Template content with placeholders like {customer_name}, {order_number}, etc.')),
                ('whatsapp_template_name', models.CharField(blank=True, max_length=100)),
                ('language_code', models.CharField(default='en', max_length=10)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='whatsapp_templates', to='accounts.business')),
            ],
            options={
                'unique_together': {('business', 'name')},
            },
        ),
    ]
