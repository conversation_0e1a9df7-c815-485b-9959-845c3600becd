/**
 * API service functions for Forge dashboard operations
 */

const API_BASE_URL = 'http://localhost:8000/api'

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('authToken')
  return {
    'Content-Type': 'application/json',
    'Authorization': token ? `Token ${token}` : ''
  }
}

// Helper function to handle API responses
const handleResponse = async (response) => {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}))
    throw new Error(errorData.message || errorData.error || `HTTP ${response.status}`)
  }
  return response.json()
}

// Business Management Services
export const businessService = {
  // Get all businesses (Forge only)
  getAllBusinesses: async (filters = {}) => {
    const params = new URLSearchParams()
    if (filters.status) params.append('status', filters.status)
    if (filters.business_type) params.append('business_type', filters.business_type)
    if (filters.search) params.append('search', filters.search)
    
    const response = await fetch(`${API_BASE_URL}/auth/businesses/?${params}`, {
      headers: getAuthHeaders()
    })
    return handleResponse(response)
  },

  // Get business details
  getBusinessDetails: async (businessId) => {
    const response = await fetch(`${API_BASE_URL}/auth/businesses/${businessId}/`, {
      headers: getAuthHeaders()
    })
    return handleResponse(response)
  },

  // Update business status (suspend, activate, etc.)
  updateBusinessStatus: async (businessId, status) => {
    const response = await fetch(`${API_BASE_URL}/auth/businesses/${businessId}/`, {
      method: 'PATCH',
      headers: getAuthHeaders(),
      body: JSON.stringify({ status })
    })
    return handleResponse(response)
  },

  // Update business details
  updateBusiness: async (businessId, businessData) => {
    const response = await fetch(`${API_BASE_URL}/auth/businesses/${businessId}/`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(businessData)
    })
    return handleResponse(response)
  }
}

// Subscription Management Services
export const subscriptionService = {
  // Get subscription plans
  getSubscriptionPlans: async () => {
    const response = await fetch(`${API_BASE_URL}/auth/subscription-plans/`, {
      headers: getAuthHeaders()
    })
    return handleResponse(response)
  },

  // Get all business subscriptions
  getBusinessSubscriptions: async (filters = {}) => {
    const params = new URLSearchParams()
    if (filters.status) params.append('status', filters.status)
    if (filters.business_id) params.append('business_id', filters.business_id)
    
    const response = await fetch(`${API_BASE_URL}/auth/subscriptions/?${params}`, {
      headers: getAuthHeaders()
    })
    return handleResponse(response)
  },

  // Get subscription details
  getSubscriptionDetails: async (subscriptionId) => {
    const response = await fetch(`${API_BASE_URL}/auth/subscriptions/${subscriptionId}/`, {
      headers: getAuthHeaders()
    })
    return handleResponse(response)
  },

  // Update subscription
  updateSubscription: async (subscriptionId, subscriptionData) => {
    const response = await fetch(`${API_BASE_URL}/auth/subscriptions/${subscriptionId}/`, {
      method: 'PATCH',
      headers: getAuthHeaders(),
      body: JSON.stringify(subscriptionData)
    })
    return handleResponse(response)
  }
}

// Subscription Payment Services
export const subscriptionPaymentService = {
  // Get subscription payments
  getSubscriptionPayments: async (filters = {}) => {
    const params = new URLSearchParams()
    if (filters.status) params.append('status', filters.status)
    if (filters.business_id) params.append('business_id', filters.business_id)
    
    const response = await fetch(`${API_BASE_URL}/auth/subscription-payments/?${params}`, {
      headers: getAuthHeaders()
    })
    return handleResponse(response)
  },

  // Approve or reject subscription payment
  approvePayment: async (paymentId, approvalData) => {
    const response = await fetch(`${API_BASE_URL}/auth/subscription-payments/${paymentId}/approve/`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(approvalData)
    })
    return handleResponse(response)
  }
}

// Reports and Analytics Services
export const reportsService = {
  // Get subscription reports
  getSubscriptionReports: async () => {
    const response = await fetch(`${API_BASE_URL}/auth/subscription-reports/`, {
      headers: getAuthHeaders()
    })
    return handleResponse(response)
  },

  // Get system dashboard data
  getSystemDashboard: async () => {
    const response = await fetch(`${API_BASE_URL}/auth/dashboard/system/`, {
      headers: getAuthHeaders()
    })
    return handleResponse(response)
  }
}

// User Management Services
export const userService = {
  // Get all users (Forge only)
  getAllUsers: async (filters = {}) => {
    const params = new URLSearchParams()
    if (filters.role) params.append('role', filters.role)
    if (filters.is_active !== undefined) params.append('is_active', filters.is_active)
    
    const response = await fetch(`${API_BASE_URL}/auth/users/?${params}`, {
      headers: getAuthHeaders()
    })
    return handleResponse(response)
  },

  // Get user details
  getUserDetails: async (userId) => {
    const response = await fetch(`${API_BASE_URL}/auth/users/${userId}/`, {
      headers: getAuthHeaders()
    })
    return handleResponse(response)
  },

  // Update user status
  updateUserStatus: async (userId, isActive) => {
    const response = await fetch(`${API_BASE_URL}/auth/users/${userId}/`, {
      method: 'PATCH',
      headers: getAuthHeaders(),
      body: JSON.stringify({ is_active: isActive })
    })
    return handleResponse(response)
  }
}

// Export all services as default
export default {
  businessService,
  subscriptionService,
  subscriptionPaymentService,
  reportsService,
  userService
}
