"""
Django signals for automatic notification triggering.
"""

import logging
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone

from orders.models import Order
from payments.models import Payment
from deliveries.models import Delivery
from .notifications import notification_manager

logger = logging.getLogger(__name__)


@receiver(post_save, sender=Order)
def handle_order_notifications(sender, instance, created, **kwargs):
    """Handle order-related notifications."""
    try:
        if created:
            # New order created - notify customer and business
            notification_manager.send_notification('order_created', order=instance)
            notification_manager.send_notification('new_order_alert', order=instance)
            logger.info(f"Sent order created notifications for {instance.order_number}")
        
        else:
            # Order status changed
            if hasattr(instance, '_previous_status'):
                old_status = instance._previous_status
                new_status = instance.status
                
                if old_status != new_status:
                    # Send appropriate status notification
                    if new_status == 'confirmed':
                        notification_manager.send_notification('order_confirmed', order=instance)
                        logger.info(f"Sent order confirmed notification for {instance.order_number}")
                    
                    elif new_status == 'processing':
                        notification_manager.send_notification('order_processing', order=instance)
                        logger.info(f"Sent order processing notification for {instance.order_number}")
                    
                    elif new_status == 'ready_for_delivery':
                        notification_manager.send_notification('order_ready', order=instance)
                        logger.info(f"Sent order ready notification for {instance.order_number}")
                    
                    elif new_status == 'cancelled':
                        notification_manager.send_notification('order_cancelled', order=instance)
                        logger.info(f"Sent order cancelled notification for {instance.order_number}")
    
    except Exception as e:
        logger.error(f"Failed to send order notification: {str(e)}")


@receiver(pre_save, sender=Order)
def track_order_status_change(sender, instance, **kwargs):
    """Track order status changes for notifications."""
    if instance.pk:
        try:
            previous = Order.objects.get(pk=instance.pk)
            instance._previous_status = previous.status
        except Order.DoesNotExist:
            instance._previous_status = None


@receiver(post_save, sender=Payment)
def handle_payment_notifications(sender, instance, created, **kwargs):
    """Handle payment-related notifications."""
    try:
        if created:
            # New payment created - send payment request
            notification_manager.send_notification('payment_request', payment=instance)
            logger.info(f"Sent payment request for {instance.payment_reference}")
        
        else:
            # Payment status changed
            if hasattr(instance, '_previous_status'):
                old_status = instance._previous_status
                new_status = instance.status
                
                if old_status != new_status:
                    if new_status == 'submitted':
                        notification_manager.send_notification('payment_received', payment=instance)
                        logger.info(f"Sent payment received notification for {instance.payment_reference}")
                    
                    elif new_status == 'verified':
                        notification_manager.send_notification('payment_verified', payment=instance)
                        logger.info(f"Sent payment verified notification for {instance.payment_reference}")
                    
                    elif new_status == 'rejected':
                        notification_manager.send_notification('payment_rejected', payment=instance)
                        logger.info(f"Sent payment rejected notification for {instance.payment_reference}")
    
    except Exception as e:
        logger.error(f"Failed to send payment notification: {str(e)}")


@receiver(pre_save, sender=Payment)
def track_payment_status_change(sender, instance, **kwargs):
    """Track payment status changes for notifications."""
    if instance.pk:
        try:
            previous = Payment.objects.get(pk=instance.pk)
            instance._previous_status = previous.status
        except Payment.DoesNotExist:
            instance._previous_status = None


@receiver(post_save, sender=Delivery)
def handle_delivery_notifications(sender, instance, created, **kwargs):
    """Handle delivery-related notifications."""
    try:
        if not created and hasattr(instance, '_previous_status'):
            old_status = instance._previous_status
            new_status = instance.status
            
            if old_status != new_status:
                if new_status == 'assigned':
                    notification_manager.send_notification('delivery_assigned', delivery=instance)
                    logger.info(f"Sent delivery assigned notification for {instance.delivery_number}")
                
                elif new_status == 'picked_up':
                    notification_manager.send_notification('delivery_picked_up', delivery=instance)
                    logger.info(f"Sent delivery picked up notification for {instance.delivery_number}")
                
                elif new_status == 'in_transit':
                    notification_manager.send_notification('delivery_in_transit', delivery=instance)
                    logger.info(f"Sent delivery in transit notification for {instance.delivery_number}")
                
                elif new_status == 'delivered':
                    notification_manager.send_notification('delivery_delivered', delivery=instance)
                    logger.info(f"Sent delivery completed notification for {instance.delivery_number}")
                
                elif new_status == 'failed':
                    notification_manager.send_notification('delivery_failed', delivery=instance)
                    logger.info(f"Sent delivery failed notification for {instance.delivery_number}")
    
    except Exception as e:
        logger.error(f"Failed to send delivery notification: {str(e)}")


@receiver(pre_save, sender=Delivery)
def track_delivery_status_change(sender, instance, **kwargs):
    """Track delivery status changes for notifications."""
    if instance.pk:
        try:
            previous = Delivery.objects.get(pk=instance.pk)
            instance._previous_status = previous.status
        except Delivery.DoesNotExist:
            instance._previous_status = None
