"""
Serializers for the deliveries app.
"""

from rest_framework import serializers
from django.utils import timezone
from .models import DeliveryZone, Delivery, DeliveryUpdate, DeliveryProof
from accounts.models import User


class DeliveryZoneSerializer(serializers.ModelSerializer):
    """
    Serializer for delivery zones.
    """
    business_name = serializers.CharField(source='business.name', read_only=True)
    
    class Meta:
        model = DeliveryZone
        fields = (
            'id', 'business', 'business_name', 'name', 'description',
            'delivery_fee', 'estimated_delivery_time', 'is_active',
            'created_at', 'updated_at'
        )
        read_only_fields = ('id', 'business', 'created_at', 'updated_at')


class DeliveryZoneCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating delivery zones.
    """
    
    class Meta:
        model = DeliveryZone
        fields = (
            'name', 'description', 'delivery_fee',
            'estimated_delivery_time', 'is_active'
        )
    
    def create(self, validated_data):
        # Set business from request context
        business = self.context['request'].user.business
        validated_data['business'] = business
        return super().create(validated_data)


class DeliveryUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for delivery updates.
    """
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = DeliveryUpdate
        fields = (
            'id', 'status', 'status_display', 'message',
            'latitude', 'longitude', 'created_by',
            'created_by_name', 'created_at'
        )
        read_only_fields = ('id', 'created_by', 'created_at')


class DeliveryProofSerializer(serializers.ModelSerializer):
    """
    Serializer for delivery proofs.
    """
    proof_type_display = serializers.CharField(source='get_proof_type_display', read_only=True)
    uploaded_by_name = serializers.CharField(source='uploaded_by.get_full_name', read_only=True)
    file_url = serializers.SerializerMethodField()
    
    class Meta:
        model = DeliveryProof
        fields = (
            'id', 'proof_type', 'proof_type_display', 'file',
            'file_url', 'description', 'latitude', 'longitude',
            'uploaded_by', 'uploaded_by_name', 'uploaded_at'
        )
        read_only_fields = ('id', 'uploaded_by', 'uploaded_at')
    
    def get_file_url(self, obj):
        if obj.file:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.file.url)
            return obj.file.url
        return None


class DeliverySerializer(serializers.ModelSerializer):
    """
    Serializer for deliveries.
    """
    updates = DeliveryUpdateSerializer(many=True, read_only=True)
    proofs = DeliveryProofSerializer(many=True, read_only=True)
    order_number = serializers.CharField(source='order.order_number', read_only=True)
    customer_name = serializers.CharField(source='order.customer.get_full_name', read_only=True)
    customer_phone = serializers.CharField(source='order.customer.phone_number', read_only=True)
    business_name = serializers.CharField(source='order.business.name', read_only=True)
    runner_name = serializers.CharField(source='runner.get_full_name', read_only=True)
    runner_phone = serializers.CharField(source='runner.phone_number', read_only=True)
    delivery_zone_name = serializers.CharField(source='delivery_zone.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Delivery
        fields = (
            'id', 'delivery_number', 'order', 'order_number',
            'customer_name', 'customer_phone', 'business_name',
            'runner', 'runner_name', 'runner_phone',
            'delivery_zone', 'delivery_zone_name', 'status',
            'status_display', 'pickup_address', 'delivery_address',
            'pickup_contact', 'delivery_contact', 'pickup_instructions',
            'delivery_instructions', 'delivery_fee', 'updates',
            'proofs', 'created_at', 'updated_at', 'assigned_at',
            'picked_up_at', 'delivered_at', 'estimated_delivery_time'
        )
        read_only_fields = (
            'id', 'delivery_number', 'order', 'created_at',
            'updated_at', 'assigned_at', 'picked_up_at', 'delivered_at'
        )


class DeliveryCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating deliveries.
    """
    
    class Meta:
        model = Delivery
        fields = (
            'delivery_zone', 'pickup_address', 'delivery_address',
            'pickup_contact', 'delivery_contact', 'pickup_instructions',
            'delivery_instructions', 'delivery_fee'
        )
    
    def create(self, validated_data):
        # Get order from context
        order = self.context['order']
        validated_data['order'] = order
        
        # Set default addresses from order if not provided
        if not validated_data.get('delivery_address'):
            validated_data['delivery_address'] = order.delivery_address
        if not validated_data.get('delivery_contact'):
            validated_data['delivery_contact'] = order.delivery_phone or order.customer.phone_number
        
        # Set pickup address from business if not provided
        if not validated_data.get('pickup_address'):
            validated_data['pickup_address'] = order.business.address or "Business location"
        if not validated_data.get('pickup_contact'):
            validated_data['pickup_contact'] = order.business.business_phone or order.business.owner.phone_number
        
        return super().create(validated_data)


class DeliveryAssignmentSerializer(serializers.ModelSerializer):
    """
    Serializer for assigning deliveries to runners.
    """
    
    class Meta:
        model = Delivery
        fields = ('runner',)
    
    def validate_runner(self, value):
        if value.role != 'runner':
            raise serializers.ValidationError("User must be a runner")
        
        if not value.is_active:
            raise serializers.ValidationError("Runner is not active")
        
        # Check if runner is available
        profile = getattr(value, 'profile', None)
        if profile and not profile.is_available:
            raise serializers.ValidationError("Runner is not available")
        
        return value
    
    def update(self, instance, validated_data):
        # Set assignment timestamp
        if 'runner' in validated_data and validated_data['runner']:
            instance.assigned_at = timezone.now()
            instance.status = 'assigned'
        
        return super().update(instance, validated_data)


class DeliveryStatusUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating delivery status.
    """
    message = serializers.CharField(required=False, allow_blank=True)
    latitude = serializers.DecimalField(max_digits=9, decimal_places=6, required=False)
    longitude = serializers.DecimalField(max_digits=9, decimal_places=6, required=False)
    
    class Meta:
        model = Delivery
        fields = ('status', 'message', 'latitude', 'longitude')
    
    def validate_status(self, value):
        current_status = self.instance.status
        
        # Define allowed status transitions
        allowed_transitions = {
            'pending': ['assigned'],
            'assigned': ['picked_up', 'failed'],
            'picked_up': ['in_transit', 'failed'],
            'in_transit': ['delivered', 'failed'],
            'delivered': [],  # Final state
            'failed': ['assigned'],  # Can reassign
            'returned': []    # Final state
        }
        
        if value not in allowed_transitions.get(current_status, []):
            raise serializers.ValidationError(
                f"Cannot change status from {current_status} to {value}"
            )
        
        return value
    
    def update(self, instance, validated_data):
        status = validated_data.get('status')
        message = validated_data.pop('message', '')
        latitude = validated_data.pop('latitude', None)
        longitude = validated_data.pop('longitude', None)
        
        # Update status timestamps
        if status == 'picked_up':
            instance.picked_up_at = timezone.now()
        elif status == 'delivered':
            instance.delivered_at = timezone.now()
        
        # Create delivery update
        if status:
            DeliveryUpdate.objects.create(
                delivery=instance,
                status=status,
                message=message or f"Status updated to {instance.get_status_display()}",
                latitude=latitude,
                longitude=longitude,
                created_by=self.context['request'].user
            )
        
        return super().update(instance, validated_data)


class DeliveryProofUploadSerializer(serializers.ModelSerializer):
    """
    Serializer for uploading delivery proofs.
    """
    
    class Meta:
        model = DeliveryProof
        fields = ('proof_type', 'file', 'description', 'latitude', 'longitude')
    
    def validate_file(self, value):
        # Check file size (max 10MB)
        if value.size > 10 * 1024 * 1024:
            raise serializers.ValidationError("File size cannot exceed 10MB")
        
        # Check file type
        allowed_types = [
            'image/jpeg', 'image/png', 'image/gif',
            'application/pdf', 'image/webp'
        ]
        
        if hasattr(value, 'content_type'):
            if value.content_type not in allowed_types:
                raise serializers.ValidationError(
                    "File type not supported. Please upload JPEG, PNG, GIF, WebP, or PDF files."
                )
        
        return value
    
    def create(self, validated_data):
        # Get delivery from context
        delivery = self.context['delivery']
        validated_data['delivery'] = delivery
        validated_data['uploaded_by'] = self.context['request'].user
        return super().create(validated_data)


class DeliverySummarySerializer(serializers.ModelSerializer):
    """
    Simplified serializer for delivery summaries.
    """
    order_number = serializers.CharField(source='order.order_number', read_only=True)
    customer_name = serializers.CharField(source='order.customer.get_full_name', read_only=True)
    business_name = serializers.CharField(source='order.business.name', read_only=True)
    runner_name = serializers.CharField(source='runner.get_full_name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Delivery
        fields = (
            'id', 'delivery_number', 'order_number', 'customer_name',
            'business_name', 'runner_name', 'status', 'status_display',
            'delivery_fee', 'created_at', 'assigned_at', 'delivered_at'
        )


class AvailableRunnerSerializer(serializers.ModelSerializer):
    """
    Serializer for available runners.
    """
    full_name = serializers.CharField(source='get_full_name', read_only=True)
    vehicle_type = serializers.CharField(source='profile.vehicle_type', read_only=True)
    
    class Meta:
        model = User
        fields = (
            'id', 'username', 'full_name', 'phone_number',
            'vehicle_type', 'is_active'
        )
