# Mthunzi API Documentation

This document provides an overview of the Mthunzi API endpoints for the WhatsApp-based sales and delivery automation system.

## Base URL
```
http://localhost:8000/api/
```

## Authentication
Most endpoints require authentication using Token-based authentication. Include the token in the Authorization header:
```
Authorization: Token your-token-here
```

## User Roles
- **Forge**: System/brand owner
- **Pod**: Business owner
- **Pulse**: Customer
- **Runner**: Delivery agent

---

## Authentication Endpoints

### Register User
- **POST** `/auth/register/`
- **Body**: `username`, `email`, `password`, `password_confirm`, `first_name`, `last_name`, `phone_number`, `role`
- **Response**: User data and authentication token

### Login
- **POST** `/auth/login/`
- **Body**: `username`, `password`
- **Response**: User data and authentication token

### Logout
- **POST** `/auth/logout/`
- **Headers**: Authorization required
- **Response**: Success message

### User Profile
- **GET/PUT** `/auth/profile/`
- **Headers**: Authorization required
- **Response**: User profile data

---

## Business Management (Pod only)

### List/Create Businesses
- **GET/POST** `/auth/businesses/`
- **Headers**: Authorization required (Pod role)
- **Response**: List of businesses or created business

### Business Detail
- **GET/PUT/DELETE** `/auth/businesses/{id}/`
- **Headers**: Authorization required (Pod role)
- **Response**: Business details

---

## Product Management

### List/Create Products
- **GET/POST** `/orders/products/`
- **Headers**: Authorization required
- **GET**: All users can view active products
- **POST**: Pod only
- **Response**: List of products or created product

### Product Detail
- **GET/PUT/DELETE** `/orders/products/{id}/`
- **Headers**: Authorization required
- **Response**: Product details

### Business Products
- **GET** `/orders/businesses/{business_id}/products/`
- **Headers**: Authorization required
- **Response**: Products for specific business

---

## Order Management

### List/Create Orders
- **GET/POST** `/orders/`
- **Headers**: Authorization required
- **GET**: Users see orders based on role
- **POST**: Pulse (customers) only
- **Response**: List of orders or created order

### Order Detail
- **GET/PUT** `/orders/{id}/`
- **Headers**: Authorization required
- **Response**: Order details

### Confirm Order
- **POST** `/orders/{id}/confirm/`
- **Headers**: Authorization required (Pod only)
- **Response**: Confirmed order

---

## Payment Management

### Payment Methods (Pod only)
- **GET/POST** `/payments/methods/`
- **Headers**: Authorization required (Pod role)
- **Response**: List of payment methods or created method

### Payment Method Detail
- **GET/PUT/DELETE** `/payments/methods/{id}/`
- **Headers**: Authorization required (Pod role)
- **Response**: Payment method details

### List Payments
- **GET** `/payments/`
- **Headers**: Authorization required
- **Response**: List of payments based on user role

### Payment Detail
- **GET** `/payments/{id}/`
- **Headers**: Authorization required
- **Response**: Payment details

### Create Payment
- **POST** `/payments/orders/{order_id}/create/`
- **Headers**: Authorization required (Pulse only)
- **Body**: `payment_method`, `customer_reference`, `customer_notes`
- **Response**: Created payment

### Upload Payment Proof
- **POST** `/payments/{payment_id}/upload-proof/`
- **Headers**: Authorization required
- **Body**: `proof_type`, `file`, `description`
- **Response**: Uploaded proof details

### Verify Payment
- **POST** `/payments/{payment_id}/verify/`
- **Headers**: Authorization required (Pod only)
- **Body**: `status` (verified/rejected), `verification_notes`
- **Response**: Verification result

---

## WhatsApp Integration

### List Messages
- **GET** `/whatsapp/messages/`
- **Headers**: Authorization required
- **Response**: WhatsApp messages based on user role

### Send Message
- **POST** `/whatsapp/send/`
- **Headers**: Authorization required (Pod only)
- **Body**: `to_number`, `message`, `media_url` (optional)
- **Response**: Sent message details

### Message Templates (Pod only)
- **GET/POST** `/whatsapp/templates/`
- **Headers**: Authorization required (Pod role)
- **Response**: List of templates or created template

### Template Detail
- **GET/PUT/DELETE** `/whatsapp/templates/{id}/`
- **Headers**: Authorization required (Pod role)
- **Response**: Template details

### Send Template Message
- **POST** `/whatsapp/templates/{id}/send/`
- **Headers**: Authorization required (Pod only)
- **Body**: `to_number`, `context`
- **Response**: Sent template message

### Webhook (Public)
- **POST** `/whatsapp/webhook/`
- **No authentication required**
- **Body**: Twilio webhook data
- **Response**: HTTP 200 OK

---

## Error Responses

All endpoints return appropriate HTTP status codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `500`: Internal Server Error

Error responses include a JSON object with error details:
```json
{
    "error": "Error message description"
}
```

---

## Example Usage

### 1. Register a new customer
```bash
curl -X POST http://localhost:8000/api/auth/register/ \
  -H "Content-Type: application/json" \
  -d '{
    "username": "customer1",
    "email": "<EMAIL>",
    "password": "securepassword",
    "password_confirm": "securepassword",
    "phone_number": "+***********",
    "role": "pulse"
  }'
```

### 2. Create an order
```bash
curl -X POST http://localhost:8000/api/orders/ \
  -H "Authorization: Token your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "business": 1,
    "delivery_address": "123 Main St, City",
    "delivery_phone": "+***********",
    "items": [
      {
        "product": 1,
        "quantity": 2
      }
    ]
  }'
```

### 3. Upload payment proof
```bash
curl -X POST http://localhost:8000/api/payments/{payment_id}/upload-proof/ \
  -H "Authorization: Token your-token" \
  -F "proof_type=screenshot" \
  -F "file=@payment_receipt.jpg" \
  -F "description=Bank transfer receipt"
```

---

## WhatsApp Bot Commands

The Luma bot responds to these commands via WhatsApp:
- `help` - Show available commands
- `menu` - View products and prices
- `order [product] [quantity]` - Place an order
- `status` - Check order status
- `cancel` - Cancel latest order
- `contact` - Get business contact info

---

## Setup Instructions

1. Install dependencies: `pip install -r requirements.txt`
2. Configure environment variables in `.env`
3. Run migrations: `python manage.py migrate`
4. Create superuser: `python manage.py createsuperuser`
5. Start server: `python manage.py runserver`

For WhatsApp integration, configure Twilio credentials in your environment variables.

---

## Admin Dashboard Endpoints

### Business Dashboard (Pod only)
- **GET** `/auth/dashboard/business/`
- **Headers**: Authorization required (Pod role)
- **Response**: Comprehensive business analytics including orders, revenue, payments, products, and customers

### Sales Analytics (Pod only)
- **GET** `/auth/dashboard/sales/?days=30`
- **Headers**: Authorization required (Pod role)
- **Query Params**: `days` (optional, default: 30)
- **Response**: Daily sales data, top products, order status distribution

### System Dashboard (Forge only)
- **GET** `/auth/dashboard/system/`
- **Headers**: Authorization required (Forge role)
- **Response**: System-wide statistics for all businesses, users, orders, and WhatsApp messages

### Inventory Alerts (Pod only)
- **GET** `/auth/dashboard/inventory/`
- **Headers**: Authorization required (Pod role)
- **Response**: Low stock alerts, out of stock products, and inventory warnings

### User Activity Dashboard
- **GET** `/auth/dashboard/activity/`
- **Headers**: Authorization required
- **Response**: Role-specific activity data (customer orders, runner deliveries, etc.)

---

## Delivery Management

### Delivery Zones (Pod only)
- **GET/POST** `/deliveries/zones/`
- **Headers**: Authorization required (Pod role)
- **Response**: List of delivery zones or created zone

### Delivery Zone Detail
- **GET/PUT/DELETE** `/deliveries/zones/{id}/`
- **Headers**: Authorization required (Pod role)
- **Response**: Delivery zone details

### List Deliveries
- **GET** `/deliveries/`
- **Headers**: Authorization required
- **Response**: List of deliveries based on user role

### Delivery Detail
- **GET** `/deliveries/{id}/`
- **Headers**: Authorization required
- **Response**: Detailed delivery information

### Create Delivery
- **POST** `/deliveries/orders/{order_id}/create/`
- **Headers**: Authorization required (Pod only)
- **Body**: `delivery_zone`, `pickup_address`, `delivery_address`, etc.
- **Response**: Created delivery

### Assign Delivery
- **POST** `/deliveries/{delivery_id}/assign/`
- **Headers**: Authorization required (Pod only)
- **Body**: `runner` (runner user ID)
- **Response**: Assigned delivery

### Update Delivery Status
- **POST** `/deliveries/{delivery_id}/status/`
- **Headers**: Authorization required (Runner only)
- **Body**: `status`, `message`, `latitude`, `longitude`
- **Response**: Updated delivery

### Upload Delivery Proof
- **POST** `/deliveries/{delivery_id}/proof/`
- **Headers**: Authorization required (Runner only)
- **Body**: `proof_type`, `file`, `description`, `latitude`, `longitude`
- **Response**: Uploaded proof

### Available Runners
- **GET** `/deliveries/runners/available/`
- **Headers**: Authorization required (Pod only)
- **Response**: List of available runners

---

## Enhanced WhatsApp Bot Features

The Luma bot now supports advanced conversational features:

### Natural Language Understanding
- Greetings in multiple languages (English, Afrikaans, Zulu)
- Context-aware responses based on keywords
- Multi-step order process with conversation state

### Enhanced Commands
- `track` - Track delivery status with real-time updates
- `payment` - View payment methods and instructions
- `feedback [message]` - Submit customer feedback
- `hours` - View business hours
- `location` - Get business address and contact info

### Interactive Order Process
1. Customer types `order` or a number
2. Bot shows product menu with numbered options
3. Customer selects product by number
4. Bot asks for quantity
5. Customer provides quantity
6. Bot asks for delivery address
7. Order is created automatically

### Smart Keyword Detection
- **Order keywords**: buy, purchase, want, need, price, cost
- **Payment keywords**: pay, payment, bank, transfer, money
- **Delivery keywords**: track, delivery, where, location, driver
- **Info keywords**: hours, time, open, closed, address
- **Feedback keywords**: feedback, review, complain, suggest

---

## Notification System

### Automatic Notifications
The system automatically sends WhatsApp notifications for:

#### Order Notifications
- Order created (to customer and business)
- Order confirmed (to customer)
- Order processing (to customer)
- Order ready for delivery (to customer)
- Order cancelled (to customer)

#### Payment Notifications
- Payment request with instructions (to customer)
- Payment proof submitted (to business)
- Payment verified (to customer)
- Payment rejected with reason (to customer)

#### Delivery Notifications
- Delivery assigned (to customer and runner)
- Delivery picked up (to customer and business)
- Delivery in transit (to customer)
- Delivery completed (to customer and business)
- Delivery failed (to customer and business)

#### Business Notifications
- New order alerts (to business owner)
- Low stock alerts (to business owner)
- Payment proof submissions (to business owner)

### Manual Notifications
Business owners can send custom notifications using templates or direct messages.

---

## Testing

### Running Tests
```bash
# Run all tests
python run_tests.py

# Run specific app tests
python manage.py test accounts
python manage.py test orders
python manage.py test payments
python manage.py test deliveries
python manage.py test whatsapp

# Run with coverage
pip install coverage
coverage run --source='.' manage.py test
coverage report
coverage html
```

### Test Categories
- **Model Tests**: Test database models and business logic
- **API Tests**: Test REST API endpoints and authentication
- **Integration Tests**: Test WhatsApp integration and notifications
- **Permission Tests**: Test role-based access control

### Test Data
The test suite includes:
- User creation and authentication tests
- Business management tests
- Order lifecycle tests
- Payment verification tests
- Delivery tracking tests
- WhatsApp bot interaction tests

---

## Performance and Monitoring

### Database Optimization
- Indexed fields for common queries
- Optimized querysets with select_related and prefetch_related
- Database connection pooling ready

### Caching Strategy
- Redis caching for frequently accessed data
- Template fragment caching
- API response caching for static data

### Monitoring Endpoints
- Health check: `/health/`
- System status: `/status/`
- Metrics: `/metrics/` (when monitoring is enabled)

### Logging
- Structured logging with different levels
- WhatsApp message logging
- Error tracking and alerting
- Performance monitoring

---

## Security Features

### Authentication & Authorization
- Token-based authentication
- Role-based permissions (Forge, Pod, Pulse, Runner)
- Phone number verification
- Session management

### Data Protection
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CSRF protection
- Rate limiting on API endpoints

### WhatsApp Security
- Webhook signature verification
- Message encryption in transit
- Secure file uploads
- Media file validation

---

## Deployment Checklist

### Environment Setup
- [ ] Configure production database (PostgreSQL)
- [ ] Set up Redis for caching and Celery
- [ ] Configure WhatsApp Business API or Twilio
- [ ] Set up file storage (AWS S3 or similar)
- [ ] Configure email settings
- [ ] Set up monitoring and logging

### Security Configuration
- [ ] Generate secure SECRET_KEY
- [ ] Configure ALLOWED_HOSTS
- [ ] Set up SSL/TLS certificates
- [ ] Configure firewall rules
- [ ] Set up backup procedures

### Performance Optimization
- [ ] Configure database connection pooling
- [ ] Set up CDN for static files
- [ ] Configure caching layers
- [ ] Optimize database queries
- [ ] Set up load balancing (if needed)

### Monitoring Setup
- [ ] Configure error tracking (Sentry)
- [ ] Set up performance monitoring
- [ ] Configure log aggregation
- [ ] Set up health checks
- [ ] Configure alerting

---

## API Rate Limits

To prevent abuse, the following rate limits are recommended:

- **Authentication endpoints**: 5 requests per minute
- **Order creation**: 10 requests per minute
- **WhatsApp sending**: 30 requests per minute
- **File uploads**: 5 requests per minute
- **General API**: 100 requests per minute

---

## Troubleshooting

### Common Issues

#### WhatsApp Integration
- **Messages not sending**: Check Twilio credentials and webhook URL
- **Webhook not receiving**: Verify webhook URL is accessible and HTTPS
- **Bot not responding**: Check bot logic and error logs

#### Database Issues
- **Migration errors**: Ensure database is accessible and has proper permissions
- **Performance issues**: Check query optimization and indexing
- **Connection errors**: Verify database configuration and connection limits

#### Authentication Problems
- **Token errors**: Check token generation and validation
- **Permission denied**: Verify user roles and permissions
- **Session issues**: Check session configuration and storage

### Debug Mode
Enable debug mode for development:
```python
DEBUG = True
LOGGING['loggers']['mthunzi']['level'] = 'DEBUG'
```

### Health Checks
Monitor system health with:
```bash
curl http://localhost:8000/health/
```

This comprehensive documentation covers all aspects of the Mthunzi system, from basic usage to advanced deployment and troubleshooting.
