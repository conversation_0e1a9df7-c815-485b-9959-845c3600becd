<template>
    <main
        class="min-h-screen bg-gradient-to-br from-[#EDF6FF] via-[#F5F5F5] to-[#FFF1DB] flex items-center justify-center p-4 relative overflow-hidden">
        <!-- Animated Background -->
        <div class="absolute inset-0">
            <!-- Primary Gradient Overlay -->
            <div
                class="absolute inset-0 bg-gradient-to-br from-[#1E4E79]/10 via-transparent to-[#C1843E]/10 animate-pulse">
            </div>
            <!-- Subtle Pattern -->
            <div
                class="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(30,78,121,.03)_25%,rgba(30,78,121,.03)_50%,transparent_50%,transparent_75%,rgba(30,78,121,.03)_75%)] bg-[length:80px_80px] animate-[drift_20s_ease-in-out_infinite]">
            </div>
            <!-- Floating Animated Elements -->
            <div
                class="absolute top-20 left-20 w-32 h-32 bg-[#1E4E79]/15 rounded-full blur-xl animate-[float_6s_ease-in-out_infinite]">
            </div>
            <div
                class="absolute bottom-20 right-20 w-40 h-40 bg-[#C1843E]/15 rounded-full blur-xl animate-[float_8s_ease-in-out_infinite_reverse]">
            </div>
            <div
                class="absolute top-1/2 left-1/4 w-24 h-24 bg-[#132F4C]/20 rounded-full blur-lg animate-[float_7s_ease-in-out_infinite]">
            </div>
            <div
                class="absolute top-1/3 right-1/3 w-20 h-20 bg-[#704A1F]/10 rounded-full blur-md animate-[float_9s_ease-in-out_infinite_reverse]">
            </div>
        </div>

        <!-- Login Card -->
        <div class="relative w-full max-w-md animate-[slideUp_0.8s_ease-out]">
            <!-- Main Card -->
            <div
                class="bg-[#FFFFFF] rounded-2xl shadow-lg border border-[#CCCCCC]/30 overflow-hidden backdrop-blur-sm hover:shadow-xl transition-all duration-500">
                <!-- Header -->
                <div class="px-8 pt-8 pb-6 text-center">
                    <div
                        class="w-16 h-16 bg-gradient-to-br from-[#1E4E79] to-[#132F4C] rounded-2xl mx-auto mb-4 flex items-center justify-center shadow-lg animate-[bounce_1s_ease-out_0.5s] hover:scale-110 transition-transform duration-300">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                    </div>
                    <h1 class="text-2xl font-bold text-[#111111] mb-2 animate-[fadeIn_1s_ease-out_0.3s_both]">Welcome
                        back</h1>
                    <p class="text-[#4B4B4B] text-sm animate-[fadeIn_1s_ease-out_0.5s_both]">Sign in to your <span
                            class="font-semibold bg-gradient-to-r from-[#1E4E79] to-[#C1843E] bg-clip-text text-transparent">Mthunzi</span>
                        account</p>
                </div>

                <!-- Form -->
                <div class="px-8 pb-8">
                    <form @submit.prevent="handleLogin" class="space-y-6">
                        <!-- Error Message -->
                        <div v-if="errorMessage"
                            class="animate-[slideInDown_0.3s_ease-out] bg-red-50 border border-red-200 rounded-xl p-4">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <p class="text-sm text-red-700">{{ errorMessage }}</p>
                            </div>
                        </div>

                        <!-- Username Field -->
                        <div class="space-y-2 animate-[slideInLeft_0.6s_ease-out_0.7s_both]">
                            <label for="username" class="block text-sm font-medium text-[#111111]">
                                Username
                            </label>
                            <div class="relative group">
                                <input id="username" type="text" v-model="username" @input="clearError"
                                    placeholder="Enter your username"
                                    class="w-full px-4 py-3 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] placeholder-[#4B4B4B] focus:bg-[#FFFFFF] focus:ring-2 focus:ring-[#1E4E79]/20 focus:border-[#1E4E79] transition-all duration-300 outline-none hover:bg-[#FFFFFF] group-hover:shadow-md"
                                    required />
                                <div
                                    class="absolute inset-y-0 right-0 flex items-center pr-4 transition-colors duration-300">
                                    <svg class="w-5 h-5 text-[#4B4B4B] group-focus-within:text-[#1E4E79]" fill="none"
                                        stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <!-- Password Field -->
                        <div class="space-y-2 animate-[slideInRight_0.6s_ease-out_0.9s_both]">
                            <label for="password" class="block text-sm font-medium text-[#111111]">
                                Password
                            </label>
                            <div class="relative group">
                                <input id="password" :type="showPassword ? 'text' : 'password'" v-model="password"
                                    @input="clearError" placeholder="Enter your password"
                                    class="w-full px-4 py-3 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] placeholder-[#4B4B4B] focus:bg-[#FFFFFF] focus:ring-2 focus:ring-[#1E4E79]/20 focus:border-[#1E4E79] transition-all duration-300 outline-none pr-12 hover:bg-[#FFFFFF] group-hover:shadow-md"
                                    required />
                                <button type="button" @click="showPassword = !showPassword"
                                    class="absolute inset-y-0 right-0 flex items-center pr-4 text-[#4B4B4B] hover:text-[#1E4E79] transition-all duration-300 hover:scale-110">
                                    <svg v-if="!showPassword" class="w-5 h-5" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                    <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Remember Me & Forgot Password -->
                        <div class="flex items-center justify-between animate-[fadeIn_0.6s_ease-out_1.1s_both]">
                            <label class="flex items-center group cursor-pointer">
                                <input type="checkbox" v-model="rememberMe"
                                    class="w-4 h-4 text-[#1E4E79] bg-[#F5F5F5] border-[#CCCCCC] rounded focus:ring-[#1E4E79] focus:ring-2 transition-all duration-300">
                                <span
                                    class="ml-2 text-sm text-[#4B4B4B] group-hover:text-[#111111] transition-colors duration-300">Remember
                                    me</span>
                            </label>
                            <a href="#"
                                class="text-sm text-[#1E4E79] hover:text-[#C1843E] font-medium transition-all duration-300 hover:underline">
                                Forgot password?
                            </a>
                        </div>

                        <!-- Login Button -->
                        <button type="submit" :disabled="isLoading"
                            class="w-full bg-gradient-to-r from-[#1E4E79] to-[#132F4C] hover:from-[#132F4C] hover:to-[#1E4E79] disabled:from-[#4B4B4B] disabled:to-[#4B4B4B] text-white font-semibold py-3 px-4 rounded-xl transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg active:scale-[0.98] disabled:transform-none disabled:cursor-not-allowed flex items-center justify-center animate-[slideUp_0.6s_ease-out_1.3s_both] group">
                            <svg v-if="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                    stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                </path>
                            </svg>
                            <span class="group-hover:tracking-wide transition-all duration-300">
                                {{ isLoading ? 'Signing in...' : 'Sign in' }}
                            </span>
                        </button>
                    </form>

                    <!-- Divider -->
                    <div class="relative my-6 animate-[fadeIn_0.6s_ease-out_1.5s_both]">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-[#CCCCCC]/50"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-4 bg-[#FFFFFF] text-[#4B4B4B]">or</span>
                        </div>
                    </div>

                    <!-- Register Link -->
                    <div class="text-center animate-[fadeIn_0.6s_ease-out_1.7s_both]">
                        <p class="text-sm text-[#4B4B4B]">
                            Don't have an account?
                            <router-link to="/register"
                                class="font-semibold text-[#1E4E79] hover:text-[#C1843E] transition-all duration-300 hover:underline">
                                Create account
                            </router-link>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Security Badge -->
            <div class="mt-6 flex items-center justify-center space-x-6 animate-[slideUp_0.8s_ease-out_1.9s_both]">
                <div
                    class="flex items-center space-x-2 text-[#4B4B4B] hover:text-[#1E4E79] transition-colors duration-300 group">
                    <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-300" fill="none"
                        stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                    <span class="text-xs font-medium">Secure Login</span>
                </div>
                <div
                    class="flex items-center space-x-2 text-[#4B4B4B] hover:text-[#C1843E] transition-colors duration-300 group">
                    <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-300" fill="none"
                        stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                    <span class="text-xs font-medium">Privacy Protected</span>
                </div>
            </div>

            <!-- Footer -->
            <div class="mt-8 text-center animate-[fadeIn_0.8s_ease-out_2.1s_both]">
                <p class="text-xs text-[#4B4B4B]">
                    A <span
                        class="font-semibold bg-gradient-to-r from-[#1E4E79] to-[#C1843E] bg-clip-text text-transparent">NorthForm</span>
                    Production
                </p>
            </div>
        </div>
    </main>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth.js'

const router = useRouter()
const authStore = useAuthStore()

// Form data
const username = ref('')
const password = ref('')
const rememberMe = ref(false)
const showPassword = ref(false)
const isLoading = ref(false)
const errorMessage = ref('')

// Handle login
const handleLogin = async () => {
    if (isLoading.value) return

    isLoading.value = true
    errorMessage.value = ''
    authStore.clearError()

    try {
        const credentials = {
            username: username.value,
            password: password.value
        }

        const result = await authStore.login(credentials)

        if (result.success) {
            // Redirect based on user role
            if (authStore.isForge) {
                router.push('/forge')
            } else if (authStore.isPod) {
                router.push('/pod')
            } else {
                // Default to pod dashboard
                router.push('/pod')
            }
        }

    } catch (error) {
        console.error('Login failed:', error)
        errorMessage.value = error.message || 'Login failed. Please check your credentials and try again.'
    } finally {
        isLoading.value = false
    }
}

// Clear error when user starts typing
const clearError = () => {
    if (errorMessage.value) {
        errorMessage.value = ''
        authStore.clearError()
    }
}

// Check if user is already authenticated on component mount
onMounted(async () => {
    if (authStore.isAuthenticated) {
        // User is already logged in, redirect to appropriate dashboard
        if (authStore.isForge) {
            router.push('/forge')
        } else if (authStore.isPod) {
            router.push('/pod')
        }
    }
})
</script>

<style scoped>
/* Custom Animations */
@keyframes float {

    0%,
    100% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-20px);
    }
}

@keyframes drift {

    0%,
    100% {
        transform: translateX(0px);
    }

    50% {
        transform: translateX(10px);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes bounce {

    0%,
    20%,
    53%,
    80%,
    100% {
        transform: translate3d(0, 0, 0);
    }

    40%,
    43% {
        transform: translate3d(0, -8px, 0);
    }

    70% {
        transform: translate3d(0, -4px, 0);
    }

    90% {
        transform: translate3d(0, -2px, 0);
    }
}

/* Hover effects for enhanced interactivity */
.group:hover .group-hover\:shadow-md {
    box-shadow: 0 4px 6px -1px rgba(30, 78, 121, 0.1), 0 2px 4px -1px rgba(30, 78, 121, 0.06);
}
</style>