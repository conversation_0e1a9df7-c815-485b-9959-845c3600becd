from django.db import models
from django.conf import settings
from django.core.validators import MinValueValidator
from decimal import Decimal
import uuid


class DeliveryZone(models.Model):
    """
    Delivery zones for businesses with different pricing.
    """
    business = models.ForeignKey(
        'accounts.Business',
        on_delete=models.CASCADE,
        related_name='delivery_zones'
    )

    name = models.CharField(max_length=100, help_text="Zone name (e.g., 'City Center', 'Suburbs')")
    description = models.TextField(blank=True)

    # Pricing
    delivery_fee = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.00'))]
    )

    # Delivery time estimates
    estimated_delivery_time = models.PositiveIntegerField(
        help_text="Estimated delivery time in minutes"
    )

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} - {self.business.name}"

    class Meta:
        unique_together = ['business', 'name']


class Delivery(models.Model):
    """
    Delivery records for orders.
    """

    class DeliveryStatus(models.TextChoices):
        PENDING = 'pending', 'Pending Assignment'
        ASSIGNED = 'assigned', 'Assigned to Runner'
        PICKED_UP = 'picked_up', 'Picked Up'
        IN_TRANSIT = 'in_transit', 'In Transit'
        DELIVERED = 'delivered', 'Delivered'
        FAILED = 'failed', 'Delivery Failed'
        RETURNED = 'returned', 'Returned to Business'

    # Delivery identification
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    delivery_number = models.CharField(max_length=20, unique=True)

    # Relationships
    order = models.OneToOneField(
        'orders.Order',
        on_delete=models.CASCADE,
        related_name='delivery'
    )
    runner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        limit_choices_to={'role': 'runner'},
        related_name='deliveries'
    )
    delivery_zone = models.ForeignKey(
        DeliveryZone,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='deliveries'
    )

    # Delivery details
    status = models.CharField(
        max_length=15,
        choices=DeliveryStatus.choices,
        default=DeliveryStatus.PENDING
    )

    # Addresses
    pickup_address = models.TextField(help_text="Business pickup address")
    delivery_address = models.TextField(help_text="Customer delivery address")

    # Contact information
    pickup_contact = models.CharField(max_length=17, blank=True)
    delivery_contact = models.CharField(max_length=17, blank=True)

    # Delivery instructions
    pickup_instructions = models.TextField(blank=True)
    delivery_instructions = models.TextField(blank=True)

    # Pricing
    delivery_fee = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.00'))]
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    assigned_at = models.DateTimeField(null=True, blank=True)
    picked_up_at = models.DateTimeField(null=True, blank=True)
    delivered_at = models.DateTimeField(null=True, blank=True)

    # Estimated delivery time
    estimated_delivery_time = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"Delivery {self.delivery_number} - Order {self.order.order_number}"

    def save(self, *args, **kwargs):
        if not self.delivery_number:
            # Generate delivery number
            import random
            import string
            self.delivery_number = 'DEL-' + ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
        super().save(*args, **kwargs)

    class Meta:
        ordering = ['-created_at']
        verbose_name_plural = "Deliveries"


class DeliveryUpdate(models.Model):
    """
    Status updates and tracking information for deliveries.
    """
    delivery = models.ForeignKey(
        Delivery,
        on_delete=models.CASCADE,
        related_name='updates'
    )

    status = models.CharField(
        max_length=15,
        choices=Delivery.DeliveryStatus.choices
    )

    message = models.TextField(help_text="Update message or notes")

    # Location information (optional)
    latitude = models.DecimalField(
        max_digits=9,
        decimal_places=6,
        null=True,
        blank=True
    )
    longitude = models.DecimalField(
        max_digits=9,
        decimal_places=6,
        null=True,
        blank=True
    )

    # Who created this update
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='delivery_updates'
    )

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.delivery.delivery_number} - {self.get_status_display()}"

    class Meta:
        ordering = ['-created_at']


class DeliveryProof(models.Model):
    """
    Proof of delivery files (photos, signatures, etc.).
    """

    class ProofType(models.TextChoices):
        PHOTO = 'photo', 'Photo'
        SIGNATURE = 'signature', 'Signature'
        RECEIPT = 'receipt', 'Receipt'
        OTHER = 'other', 'Other'

    delivery = models.ForeignKey(
        Delivery,
        on_delete=models.CASCADE,
        related_name='proofs'
    )

    proof_type = models.CharField(
        max_length=10,
        choices=ProofType.choices,
        default=ProofType.PHOTO
    )

    file = models.FileField(
        upload_to='delivery_proofs/',
        help_text="Upload proof of delivery"
    )

    description = models.CharField(
        max_length=200,
        blank=True,
        help_text="Brief description of the proof"
    )

    # Location where proof was taken
    latitude = models.DecimalField(
        max_digits=9,
        decimal_places=6,
        null=True,
        blank=True
    )
    longitude = models.DecimalField(
        max_digits=9,
        decimal_places=6,
        null=True,
        blank=True
    )

    uploaded_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='uploaded_delivery_proofs'
    )

    uploaded_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Proof for {self.delivery.delivery_number}"

    class Meta:
        ordering = ['-uploaded_at']
