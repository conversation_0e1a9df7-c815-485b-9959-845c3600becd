"""
Notification system for WhatsApp communications.
"""

import logging
from typing import Dict, Any, List, Optional
from django.conf import settings
from django.utils import timezone
from django.template import Template, Context

from .services import whatsapp_service
from .models import WhatsAppTemplate
from accounts.models import User, Business
from orders.models import Order
from payments.models import Payment
from deliveries.models import Delivery

logger = logging.getLogger(__name__)


class NotificationManager:
    """
    Centralized notification manager for WhatsApp communications.
    """
    
    def __init__(self):
        self.notification_types = {
            # Order notifications
            'order_created': self.send_order_created,
            'order_confirmed': self.send_order_confirmed,
            'order_processing': self.send_order_confirmed,  # Use same as confirmed for now
            'order_ready': self.send_order_confirmed,       # Use same as confirmed for now
            'order_cancelled': self.send_order_confirmed,   # Use same as confirmed for now
            
            # Payment notifications
            'payment_request': self.send_payment_request,
            'payment_received': self.send_payment_verified,  # Use same as verified for now
            'payment_verified': self.send_payment_verified,
            'payment_rejected': self.send_payment_verified,  # Use same as verified for now
            
            # Delivery notifications
            'delivery_assigned': self.send_delivery_assigned,
            'delivery_picked_up': self.send_delivery_assigned,  # Use same as assigned for now
            'delivery_in_transit': self.send_delivery_assigned, # Use same as assigned for now
            'delivery_delivered': self.send_delivery_delivered,
            'delivery_failed': self.send_delivery_delivered,    # Use same as delivered for now
            
            # Business notifications
            'new_order_alert': self.send_new_order_alert,
            'payment_proof_submitted': self.send_new_order_alert,  # Use same as new order for now
            'low_stock_alert': self.send_low_stock_alert,
            
            # Customer service
            'welcome_message': self.send_welcome_message,
            'feedback_received': self.send_welcome_message,  # Use same as welcome for now
        }
    
    def send_notification(self, notification_type: str, **kwargs) -> bool:
        """
        Send a notification of the specified type.
        
        Args:
            notification_type: Type of notification to send
            **kwargs: Additional parameters for the notification
        
        Returns:
            True if notification was sent successfully, False otherwise
        """
        try:
            if notification_type in self.notification_types:
                handler = self.notification_types[notification_type]
                return handler(**kwargs)
            else:
                logger.error(f"Unknown notification type: {notification_type}")
                return False
        except Exception as e:
            logger.error(f"Failed to send notification {notification_type}: {str(e)}")
            return False
    
    def send_order_created(self, order: Order) -> bool:
        """Send order creation confirmation to customer."""
        try:
            template = self._get_template(order.business, 'order_confirmation')
            
            if template:
                context = {
                    'customer_name': order.customer.get_full_name() or order.customer.username,
                    'order_number': order.order_number,
                    'business_name': order.business.name,
                    'total_amount': str(order.total_amount),
                    'item_count': order.items.count(),
                    'delivery_address': order.delivery_address,
                }
                message = template.render(context)
            else:
                message = f"""
🛍️ *Order Confirmation*

Hi {order.customer.get_full_name() or order.customer.username}!

Your order has been received:
• Order Number: {order.order_number}
• Business: {order.business.name}
• Total: R{order.total_amount}
• Items: {order.items.count()}

We'll process your order and keep you updated!

Thank you for choosing {order.business.name}! 🙏
                """.strip()
            
            return whatsapp_service.send_text_message(
                order.customer.phone_number,
                message,
                order.business
            ) is not None
            
        except Exception as e:
            logger.error(f"Failed to send order created notification: {str(e)}")
            return False
    
    def send_order_confirmed(self, order: Order) -> bool:
        """Send order confirmation to customer."""
        try:
            message = f"""
✅ *Order Confirmed!*

Great news! Your order {order.order_number} has been confirmed by {order.business.name}.

We're now preparing your order and will notify you when it's ready for delivery.

Total: R{order.total_amount}
Delivery Address: {order.delivery_address}

Thank you for your patience! 😊
            """.strip()
            
            return whatsapp_service.send_text_message(
                order.customer.phone_number,
                message,
                order.business
            ) is not None
            
        except Exception as e:
            logger.error(f"Failed to send order confirmed notification: {str(e)}")
            return False
    
    def send_payment_request(self, payment: Payment) -> bool:
        """Send payment request to customer."""
        try:
            method = payment.payment_method
            
            message = f"""
💳 *Payment Request*

Order: {payment.order.order_number}
Amount: R{payment.amount}
Payment Reference: {payment.payment_reference}

*Payment Method: {method.name}*
{method.instructions}

"""
            
            if method.method_type == 'bank_transfer':
                if method.account_name:
                    message += f"Account Name: {method.account_name}\n"
                if method.account_number:
                    message += f"Account Number: {method.account_number}\n"
                if method.bank_name:
                    message += f"Bank: {method.bank_name}\n"
            elif method.method_type == 'mobile_money':
                if method.mobile_number:
                    message += f"Mobile Number: {method.mobile_number}\n"
            
            message += f"""

⚠️ Important: Use reference "{payment.payment_reference}" for your payment.

After payment, please upload your proof of payment by replying with the receipt/screenshot.
            """.strip()
            
            return whatsapp_service.send_text_message(
                payment.order.customer.phone_number,
                message,
                payment.order.business
            ) is not None
            
        except Exception as e:
            logger.error(f"Failed to send payment request: {str(e)}")
            return False
    
    def send_payment_verified(self, payment: Payment) -> bool:
        """Send payment verification confirmation."""
        try:
            message = f"""
✅ *Payment Verified!*

Your payment for order {payment.order.order_number} has been verified!

Amount: R{payment.amount}
Reference: {payment.payment_reference}

Your order will now be processed. We'll keep you updated on the progress.

Thank you! 🙏
            """.strip()
            
            return whatsapp_service.send_text_message(
                payment.order.customer.phone_number,
                message,
                payment.order.business
            ) is not None
            
        except Exception as e:
            logger.error(f"Failed to send payment verified notification: {str(e)}")
            return False
    
    def send_delivery_assigned(self, delivery: Delivery) -> bool:
        """Send delivery assignment notification."""
        try:
            # Notify customer
            customer_message = f"""
🚚 *Delivery Update*

Your order {delivery.order.order_number} has been assigned to our delivery agent.

Runner: {delivery.runner.get_full_name()}
Phone: {delivery.runner.phone_number}

Estimated delivery: {delivery.estimated_delivery_time or 'TBD'}

You'll receive updates as your order is on its way! 📦
            """.strip()
            
            customer_sent = whatsapp_service.send_text_message(
                delivery.order.customer.phone_number,
                customer_message,
                delivery.order.business
            ) is not None
            
            # Notify runner
            runner_message = f"""
🚚 *New Delivery Assignment*

You've been assigned a new delivery:

Order: {delivery.order.order_number}
Customer: {delivery.order.customer.get_full_name()}
Phone: {delivery.order.customer.phone_number}

Pickup: {delivery.pickup_address}
Delivery: {delivery.delivery_address}
Fee: R{delivery.delivery_fee}

Please confirm when you're ready to pick up the order.
            """.strip()
            
            runner_sent = whatsapp_service.send_text_message(
                delivery.runner.phone_number,
                runner_message,
                delivery.order.business
            ) is not None
            
            return customer_sent and runner_sent
            
        except Exception as e:
            logger.error(f"Failed to send delivery assigned notification: {str(e)}")
            return False
    
    def send_delivery_delivered(self, delivery: Delivery) -> bool:
        """Send delivery completion notification."""
        try:
            # Notify customer
            customer_message = f"""
✅ *Order Delivered!*

Your order {delivery.order.order_number} has been successfully delivered!

Delivered by: {delivery.runner.get_full_name()}
Delivery time: {delivery.delivered_at.strftime('%Y-%m-%d %H:%M')}

Thank you for choosing {delivery.order.business.name}! 

We hope you enjoy your order. Your feedback is always welcome! 😊
            """.strip()
            
            customer_sent = whatsapp_service.send_text_message(
                delivery.order.customer.phone_number,
                customer_message,
                delivery.order.business
            ) is not None
            
            # Notify business
            business_message = f"""
📋 *Delivery Completed*

Order {delivery.order.order_number} has been delivered successfully!

Customer: {delivery.order.customer.get_full_name()}
Runner: {delivery.runner.get_full_name()}
Delivered: {delivery.delivered_at.strftime('%Y-%m-%d %H:%M')}
Amount: R{delivery.order.total_amount}
            """.strip()
            
            business_sent = whatsapp_service.send_text_message(
                delivery.order.business.owner.phone_number,
                business_message,
                delivery.order.business
            ) is not None
            
            return customer_sent and business_sent
            
        except Exception as e:
            logger.error(f"Failed to send delivery completed notification: {str(e)}")
            return False
    
    def send_new_order_alert(self, order: Order) -> bool:
        """Send new order alert to business owner."""
        try:
            message = f"""
🔔 *New Order Alert!*

You have a new order from {order.customer.get_full_name()}!

Order: {order.order_number}
Customer: {order.customer.get_full_name()}
Phone: {order.customer.phone_number}
Total: R{order.total_amount}
Items: {order.items.count()}

Delivery Address: {order.delivery_address}

Please review and confirm the order in your dashboard.
            """.strip()
            
            return whatsapp_service.send_text_message(
                order.business.owner.phone_number,
                message,
                order.business
            ) is not None
            
        except Exception as e:
            logger.error(f"Failed to send new order alert: {str(e)}")
            return False
    
    def send_low_stock_alert(self, product, business: Business) -> bool:
        """Send low stock alert to business owner."""
        try:
            message = f"""
⚠️ *Low Stock Alert*

Product: {product.name}
Current Stock: {product.stock_quantity}
Threshold: {product.low_stock_threshold}

Please restock this item to avoid running out.

You can update stock levels in your dashboard.
            """.strip()
            
            return whatsapp_service.send_text_message(
                business.owner.phone_number,
                message,
                business
            ) is not None
            
        except Exception as e:
            logger.error(f"Failed to send low stock alert: {str(e)}")
            return False
    
    def send_welcome_message(self, user: User, business: Business) -> bool:
        """Send welcome message to new customer."""
        try:
            template = self._get_template(business, 'welcome')
            
            if template:
                context = {
                    'customer_name': user.get_full_name() or user.username,
                    'business_name': business.name,
                }
                message = template.render(context)
            else:
                message = business.welcome_message or f"""
Welcome to {business.name}! 👋

I'm Luma, your WhatsApp assistant. I can help you:
• Browse our menu
• Place orders
• Track deliveries
• Answer questions

Type 'menu' to see our products or 'help' for more options.

How can I help you today? 😊
                """.strip()
            
            return whatsapp_service.send_text_message(
                user.phone_number,
                message,
                business
            ) is not None
            
        except Exception as e:
            logger.error(f"Failed to send welcome message: {str(e)}")
            return False
    
    def _get_template(self, business: Business, category: str) -> Optional[WhatsAppTemplate]:
        """Get WhatsApp template for business and category."""
        try:
            return WhatsAppTemplate.objects.filter(
                business=business,
                category=category,
                is_active=True
            ).first()
        except Exception:
            return None
    
    def send_bulk_notification(self, users: List[User], message: str, business: Business) -> Dict[str, int]:
        """
        Send bulk notification to multiple users.
        
        Returns:
            Dictionary with success and failure counts
        """
        results = {'success': 0, 'failed': 0}
        
        for user in users:
            try:
                if whatsapp_service.send_text_message(user.phone_number, message, business):
                    results['success'] += 1
                else:
                    results['failed'] += 1
            except Exception as e:
                logger.error(f"Failed to send bulk notification to {user.phone_number}: {str(e)}")
                results['failed'] += 1
        
        return results


# Global notification manager instance
notification_manager = NotificationManager()


# Convenience functions for common notifications
def notify_order_created(order: Order) -> bool:
    """Convenience function to send order created notification."""
    return notification_manager.send_notification('order_created', order=order)


def notify_payment_verified(payment: Payment) -> bool:
    """Convenience function to send payment verified notification."""
    return notification_manager.send_notification('payment_verified', payment=payment)


def notify_delivery_assigned(delivery: Delivery) -> bool:
    """Convenience function to send delivery assigned notification."""
    return notification_manager.send_notification('delivery_assigned', delivery=delivery)


def notify_new_order(order: Order) -> bool:
    """Convenience function to send new order alert to business."""
    return notification_manager.send_notification('new_order_alert', order=order)
