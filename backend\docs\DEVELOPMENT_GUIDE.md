# 🛠️ Mthunzi Development Guide

Welcome to the Mthunzi development environment! This guide will help you set up, run, and develop the Mthunzi WhatsApp-based sales and delivery automation system.

## 📋 Table of Contents

1. [Quick Start](#quick-start)
2. [Development Environment Setup](#development-environment-setup)
3. [Running the System](#running-the-system)
4. [Development Workflow](#development-workflow)
5. [Testing](#testing)
6. [API Development](#api-development)
7. [Frontend Development](#frontend-development)
8. [WhatsApp Bot Development](#whatsapp-bot-development)
9. [Database Management](#database-management)
10. [Troubleshooting](#troubleshooting)

---

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- Node.js 16+
- Git
- Code editor (VS Code recommended)

### 1-Minute Setup
```bash
# Clone the repository
git clone <repository-url>
cd mthunzi

# Set up Python environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up development environment
python setup_dev.py

# Start the backend
python manage.py runserver

# In another terminal, start the frontend
cd frontend
npm install
npm start
```

### Access Points
- **Backend API**: http://localhost:8000/api/
- **Admin Interface**: http://localhost:8000/admin/
- **Frontend Dashboard**: http://localhost:3000/
- **API Documentation**: http://localhost:8000/api/docs/ (if available)

---

## 🔧 Development Environment Setup

### Step 1: Python Environment
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On macOS/Linux:
source venv/bin/activate
# On Windows:
venv\Scripts\activate

# Upgrade pip
pip install --upgrade pip

# Install dependencies
pip install -r requirements.txt

# Install development dependencies
pip install -r requirements-dev.txt
```

### Step 2: Environment Variables
Create a `.env` file in the project root:
```bash
# Django Configuration
SECRET_KEY=your-development-secret-key
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
DJANGO_ENVIRONMENT=development

# Database (SQLite for development)
DATABASE_URL=sqlite:///db.sqlite3

# WhatsApp/Twilio (Optional for development)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_WHATSAPP_NUMBER=whatsapp:+**********
WHATSAPP_WEBHOOK_URL=https://your-ngrok-url.ngrok.io/api/whatsapp/webhook/

# Email (Console backend for development)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend

# Redis (Optional - will use in-memory cache if not available)
REDIS_URL=redis://localhost:6379/0
```

### Step 3: Database Setup
```bash
# Run migrations
python manage.py migrate

# Create sample data
python setup_dev.py

# Or create superuser manually
python manage.py createsuperuser
```

### Step 4: Frontend Setup
```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Start development server
npm start
```

---

## 🏃 Running the System

### Backend Development Server
```bash
# Activate virtual environment
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Start Django development server
python manage.py runserver

# Or run on specific port
python manage.py runserver 8080

# Or run on all interfaces
python manage.py runserver 0.0.0.0:8000
```

### Frontend Development Server
```bash
# In frontend directory
cd frontend

# Start React development server
npm start

# Or start on specific port
PORT=3001 npm start
```

### Running Both Simultaneously
Use two terminal windows/tabs:

**Terminal 1 (Backend):**
```bash
source venv/bin/activate
python manage.py runserver
```

**Terminal 2 (Frontend):**
```bash
cd frontend
npm start
```

### Development Login Credentials
After running `setup_dev.py`, use these credentials:

**System Admin (Forge):**
- Username: `admin`
- Password: `admin123`
- Access: Full system access

**Business Owner (Pod):**
- Username: `business_owner`
- Password: `business123`
- Access: Business management

**Customer (Pulse):**
- Username: `customer`
- Password: `customer123`
- Access: Customer features

**Delivery Agent (Runner):**
- Username: `delivery_guy`
- Password: `runner123`
- Access: Delivery management

---

## 🔄 Development Workflow

### Daily Development Routine

#### 1. Start Development Session
```bash
# Pull latest changes
git pull origin main

# Activate virtual environment
source venv/bin/activate

# Install any new dependencies
pip install -r requirements.txt

# Run migrations if any
python manage.py migrate

# Start development servers
python manage.py runserver &
cd frontend && npm start
```

#### 2. Code Changes
- Make your changes in the appropriate files
- Backend changes are auto-reloaded by Django
- Frontend changes are auto-reloaded by React
- Test your changes in the browser

#### 3. Testing
```bash
# Run backend tests
python manage.py test

# Run frontend tests
cd frontend && npm test

# Run specific test
python manage.py test accounts.tests.UserModelTest
```

#### 4. Commit Changes
```bash
# Add changes
git add .

# Commit with descriptive message
git commit -m "Add user authentication feature"

# Push to repository
git push origin feature-branch
```

### Project Structure
```
mthunzi/
├── accounts/           # User management
├── orders/            # Order management
├── payments/          # Payment processing
├── deliveries/        # Delivery management
├── whatsapp/          # WhatsApp integration
├── frontend/          # React frontend
├── mthunzi/           # Django project settings
├── static/            # Static files
├── media/             # Media files
├── templates/         # Django templates
├── requirements.txt   # Python dependencies
└── manage.py         # Django management script
```

---

## 🧪 Testing

### Backend Testing

#### Run All Tests
```bash
python manage.py test
```

#### Run Specific App Tests
```bash
python manage.py test accounts
python manage.py test orders
python manage.py test whatsapp
```

#### Run Specific Test Class
```bash
python manage.py test accounts.tests.UserModelTest
```

#### Run with Coverage
```bash
pip install coverage
coverage run --source='.' manage.py test
coverage report
coverage html  # Creates htmlcov/index.html
```

#### Test Database
Django automatically creates a test database for testing. No setup required.

### Frontend Testing

#### Run Frontend Tests
```bash
cd frontend
npm test
```

#### Run Tests in Watch Mode
```bash
npm test -- --watch
```

#### Run Tests with Coverage
```bash
npm test -- --coverage
```

### Manual Testing Checklist

#### Backend API Testing
- [ ] User registration and login
- [ ] Order creation and management
- [ ] Payment processing
- [ ] Delivery tracking
- [ ] WhatsApp webhook processing

#### Frontend Testing
- [ ] Login and authentication
- [ ] Dashboard functionality
- [ ] Order management interface
- [ ] Payment verification
- [ ] Responsive design on mobile

#### WhatsApp Bot Testing
- [ ] Bot responds to greetings
- [ ] Menu command works
- [ ] Order placement flow
- [ ] Payment instructions
- [ ] Order tracking

---

## 🔌 API Development

### API Structure
```
/api/auth/          # Authentication endpoints
/api/orders/        # Order management
/api/payments/      # Payment processing
/api/deliveries/    # Delivery management
/api/whatsapp/      # WhatsApp integration
```

### Adding New API Endpoints

#### 1. Create Serializer
```python
# In app/serializers.py
from rest_framework import serializers
from .models import YourModel

class YourModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = YourModel
        fields = '__all__'
```

#### 2. Create ViewSet
```python
# In app/views.py
from rest_framework import viewsets
from .models import YourModel
from .serializers import YourModelSerializer

class YourModelViewSet(viewsets.ModelViewSet):
    queryset = YourModel.objects.all()
    serializer_class = YourModelSerializer
```

#### 3. Add URL
```python
# In app/urls.py
from rest_framework.routers import DefaultRouter
from .views import YourModelViewSet

router = DefaultRouter()
router.register(r'yourmodel', YourModelViewSet)
urlpatterns = router.urls
```

### API Testing with curl
```bash
# Get auth token
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'

# Use token for authenticated requests
curl -X GET http://localhost:8000/api/orders/ \
  -H "Authorization: Token your-token-here"
```

---

## 🎨 Frontend Development

### Frontend Structure
```
frontend/
├── src/
│   ├── components/     # Reusable components
│   ├── pages/         # Page components
│   ├── contexts/      # React contexts
│   ├── hooks/         # Custom hooks
│   ├── utils/         # Utility functions
│   └── App.js         # Main app component
├── public/            # Static assets
└── package.json       # Dependencies
```

### Adding New Pages

#### 1. Create Page Component
```jsx
// src/pages/NewPage.js
import React from 'react';

export default function NewPage() {
  return (
    <div>
      <h1>New Page</h1>
      <p>Your content here</p>
    </div>
  );
}
```

#### 2. Add Route
```jsx
// src/App.js
import NewPage from './pages/NewPage';

// Add to Routes
<Route path="newpage" element={<NewPage />} />
```

#### 3. Add Navigation
```jsx
// src/components/Layout.js
const navigation = [
  // ... existing items
  { name: 'New Page', href: '/newpage', icon: YourIcon },
];
```

### Styling with Tailwind CSS
```jsx
// Example component with Tailwind classes
<div className="bg-white shadow rounded-lg p-6">
  <h2 className="text-lg font-medium text-gray-900 mb-4">
    Title
  </h2>
  <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded">
    Action
  </button>
</div>
```

---

## 🤖 WhatsApp Bot Development

### Bot Structure
```
whatsapp/
├── bot.py             # Main bot logic
├── handlers.py        # Message handlers
├── services.py        # WhatsApp service
└── models.py          # WhatsApp models
```

### Adding New Bot Commands

#### 1. Add Command Handler
```python
# In whatsapp/handlers.py
def handle_new_command(message, business):
    """Handle new command."""
    response = "Response to new command"
    return response
```

#### 2. Register Command
```python
# In whatsapp/bot.py
COMMANDS = {
    'help': handle_help,
    'menu': handle_menu,
    'newcommand': handle_new_command,  # Add here
}
```

#### 3. Test Command
Send "newcommand" to your WhatsApp bot to test.

### Bot Testing Without WhatsApp
```python
# In Django shell
python manage.py shell

from whatsapp.bot import process_message
from accounts.models import Business

business = Business.objects.first()
response = process_message("help", "+***********", business)
print(response)
```

---

## 🗄️ Database Management

### Common Database Commands
```bash
# Create migrations
python manage.py makemigrations

# Apply migrations
python manage.py migrate

# Reset database (CAREFUL!)
rm db.sqlite3
python manage.py migrate
python setup_dev.py

# Django shell
python manage.py shell

# Database shell
python manage.py dbshell
```

### Sample Data Management
```bash
# Create sample data
python setup_dev.py

# Or create specific data
python manage.py shell
>>> from setup_dev import create_sample_data
>>> create_sample_data()
```

### Database Queries in Shell
```python
# Django shell examples
python manage.py shell

# Import models
from accounts.models import User, Business
from orders.models import Order, Product

# Query examples
users = User.objects.all()
businesses = Business.objects.filter(status='active')
orders = Order.objects.filter(status='pending')

# Create objects
user = User.objects.create_user(
    username='testuser',
    email='<EMAIL>',
    phone_number='+***********',
    role='pulse'
)
```

---

## 🔧 Troubleshooting

### Common Issues

#### Backend Issues

**Issue**: `ModuleNotFoundError`
```bash
# Solution: Install missing package
pip install package-name

# Or reinstall all requirements
pip install -r requirements.txt
```

**Issue**: Database errors
```bash
# Solution: Reset database
rm db.sqlite3
python manage.py migrate
python setup_dev.py
```

**Issue**: Port already in use
```bash
# Solution: Use different port
python manage.py runserver 8080

# Or kill process using port
lsof -ti:8000 | xargs kill -9  # macOS/Linux
netstat -ano | findstr :8000   # Windows
```

#### Frontend Issues

**Issue**: `npm` command not found
```bash
# Solution: Install Node.js
# Download from https://nodejs.org/
```

**Issue**: Package installation fails
```bash
# Solution: Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install
```

**Issue**: Frontend can't connect to backend
```bash
# Solution: Check proxy in package.json
"proxy": "http://localhost:8000"

# Or check if backend is running
curl http://localhost:8000/api/
```

#### WhatsApp Integration Issues

**Issue**: Webhook not receiving messages
```bash
# Solution: Use ngrok for local development
npm install -g ngrok
ngrok http 8000

# Update WHATSAPP_WEBHOOK_URL in .env
WHATSAPP_WEBHOOK_URL=https://your-ngrok-url.ngrok.io/api/whatsapp/webhook/
```

**Issue**: Bot not responding
```bash
# Solution: Check bot logic in Django shell
python manage.py shell
>>> from whatsapp.bot import process_message
>>> process_message("help", "+***********", business)
```

### Debug Mode

#### Enable Debug Logging
```python
# In settings/development.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'mthunzi': {
            'handlers': ['console'],
            'level': 'DEBUG',
        },
    },
}
```

#### Django Debug Toolbar
```bash
# Install debug toolbar
pip install django-debug-toolbar

# Add to INSTALLED_APPS in development settings
INSTALLED_APPS += ['debug_toolbar']

# Add middleware
MIDDLEWARE += ['debug_toolbar.middleware.DebugToolbarMiddleware']
```

### Getting Help

#### Documentation
- Django: https://docs.djangoproject.com/
- React: https://reactjs.org/docs/
- Tailwind CSS: https://tailwindcss.com/docs

#### Community
- Django Discord: https://discord.gg/xcRH6mN4fa
- React Discord: https://discord.gg/reactiflux

#### Project-Specific Help
- Check existing issues in repository
- Create new issue with detailed description
- Include error messages and steps to reproduce

---

## 🎯 Development Best Practices

### Code Quality
- Follow PEP 8 for Python code
- Use ESLint for JavaScript code
- Write descriptive commit messages
- Add docstrings to functions
- Keep functions small and focused

### Git Workflow
```bash
# Create feature branch
git checkout -b feature/new-feature

# Make changes and commit
git add .
git commit -m "Add new feature"

# Push to repository
git push origin feature/new-feature

# Create pull request
```

### Performance
- Use database indexes for frequently queried fields
- Optimize API queries with select_related/prefetch_related
- Use React.memo for expensive components
- Implement proper caching strategies

### Security
- Never commit sensitive data (API keys, passwords)
- Use environment variables for configuration
- Validate all user inputs
- Implement proper authentication and authorization

---

## 🚀 Next Steps

### Learning Resources
1. **Django**: Complete the Django tutorial
2. **React**: Build a few small projects
3. **WhatsApp API**: Read Twilio documentation
4. **Testing**: Learn pytest and Jest

### Project Contributions
1. **Bug Fixes**: Start with small bug fixes
2. **Features**: Implement new features
3. **Documentation**: Improve documentation
4. **Testing**: Add more test coverage

### Advanced Topics
1. **Performance Optimization**: Database optimization, caching
2. **Deployment**: Docker, CI/CD, cloud deployment
3. **Monitoring**: Logging, error tracking, metrics
4. **Scaling**: Load balancing, microservices

---

**Happy coding! 🎉**

*For more help, refer to the main USER_GUIDE.md or create an issue in the repository.*
