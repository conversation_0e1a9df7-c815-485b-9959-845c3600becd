"""
Integration tests for WhatsApp business type functionality.
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from decimal import Decimal
from unittest.mock import Mock, patch

from accounts.models import Business
from orders.models import Product, DailyMenu, DailyMenuItem
from services.models import Service, Appointment
from whatsapp.models import WhatsAppMessage, ChatSession
from whatsapp.bot import LumaBot

User = get_user_model()


class WhatsAppBusinessTypeIntegrationTests(TestCase):
    """Test WhatsApp integration with different business types."""

    def setUp(self):
        # Create users
        self.product_owner = User.objects.create_user(
            username='productowner',
            email='<EMAIL>',
            password='testpass123',
            role='pod'
        )
        
        self.service_owner = User.objects.create_user(
            username='serviceowner',
            email='<EMAIL>',
            password='testpass123',
            role='pod'
        )
        
        self.food_owner = User.objects.create_user(
            username='foodowner',
            email='<EMAIL>',
            password='testpass123',
            role='pod'
        )
        
        self.customer = User.objects.create_user(
            username='customer',
            email='<EMAIL>',
            password='testpass123',
            role='pulse',
            phone_number='+***********'
        )
        
        # Create businesses
        self.product_business = Business.objects.create(
            owner=self.product_owner,
            name='Product Store',
            business_type=Business.BusinessType.PRODUCT_GOODS,
            whatsapp_business_number='+***********',
            welcome_message='Welcome to Product Store!'
        )
        
        self.service_business = Business.objects.create(
            owner=self.service_owner,
            name='Beauty Salon',
            business_type=Business.BusinessType.SERVICE,
            whatsapp_business_number='+***********',
            welcome_message='Welcome to Beauty Salon!'
        )
        
        self.food_business = Business.objects.create(
            owner=self.food_owner,
            name='Tasty Restaurant',
            business_type=Business.BusinessType.FOOD_RESTAURANT,
            whatsapp_business_number='+***********',
            welcome_message='Welcome to Tasty Restaurant!'
        )
        
        # Create chat sessions
        self.product_session = ChatSession.objects.create(
            customer=self.customer,
            business=self.product_business,
            whatsapp_number=self.customer.phone_number
        )
        
        self.service_session = ChatSession.objects.create(
            customer=self.customer,
            business=self.service_business,
            whatsapp_number=self.customer.phone_number
        )
        
        self.food_session = ChatSession.objects.create(
            customer=self.customer,
            business=self.food_business,
            whatsapp_number=self.customer.phone_number
        )
        
        # Initialize bot
        self.bot = LumaBot()

    def create_whatsapp_message(self, session, content):
        """Helper to create WhatsApp message."""
        return WhatsAppMessage.objects.create(
            session=session,
            sender=self.customer,
            content=content,
            message_type='text',
            direction='inbound'
        )

    def test_product_business_help_command(self):
        """Test help command for product business."""
        message = self.create_whatsapp_message(self.product_session, 'help')
        
        response = self.bot.process_message(message)
        
        self.assertIn('Shopping Assistant', response)
        self.assertIn('menu', response)
        self.assertIn('order', response)
        self.assertIn('delivery', response)

    def test_service_business_help_command(self):
        """Test help command for service business."""
        message = self.create_whatsapp_message(self.service_session, 'help')
        
        response = self.bot.process_message(message)
        
        self.assertIn('Service Assistant', response)
        self.assertIn('services', response)
        self.assertIn('book', response)
        self.assertIn('appointments', response)

    def test_food_business_help_command(self):
        """Test help command for food business."""
        message = self.create_whatsapp_message(self.food_session, 'help')
        
        response = self.bot.process_message(message)
        
        self.assertIn('Food Ordering Assistant', response)
        self.assertIn('menu', response)
        self.assertIn('specials', response)
        self.assertIn('order', response)

    def test_service_business_services_command(self):
        """Test services command for service business."""
        # Create services
        Service.objects.create(
            business=self.service_business,
            name='Haircut',
            description='Professional haircut',
            price=Decimal('50.00'),
            duration_minutes=60,
            status='active'
        )
        
        Service.objects.create(
            business=self.service_business,
            name='Manicure',
            description='Professional manicure',
            price=Decimal('30.00'),
            duration_minutes=45,
            status='active'
        )
        
        message = self.create_whatsapp_message(self.service_session, 'services')
        
        response = self.bot.process_message(message)
        
        self.assertIn('Beauty Salon Services', response)
        self.assertIn('Haircut', response)
        self.assertIn('R50', response)
        self.assertIn('60 minutes', response)
        self.assertIn('Manicure', response)
        self.assertIn('R30', response)
        self.assertIn('45 minutes', response)

    def test_service_business_services_command_no_services(self):
        """Test services command when no services are available."""
        message = self.create_whatsapp_message(self.service_session, 'services')
        
        response = self.bot.process_message(message)
        
        self.assertIn('No services are currently available', response)

    def test_food_business_specials_command(self):
        """Test specials command for food business."""
        today = timezone.now().date()
        
        # Create product
        product = Product.objects.create(
            business=self.food_business,
            name='Burger Special',
            description='Delicious burger',
            price=Decimal('20.00'),
            sku='BURGER-001'
        )
        
        # Create daily menu
        daily_menu = DailyMenu.objects.create(
            business=self.food_business,
            date=today,
            is_active=True
        )
        
        # Create menu item with special price
        DailyMenuItem.objects.create(
            daily_menu=daily_menu,
            product=product,
            available_quantity=30,
            is_available=True,
            special_price=Decimal('15.00'),
            notes='Limited time offer'
        )
        
        message = self.create_whatsapp_message(self.food_session, 'specials')
        
        response = self.bot.process_message(message)
        
        self.assertIn("Today's Specials", response)
        self.assertIn('Burger Special', response)
        self.assertIn('R15', response)
        self.assertIn('was R20', response)

    def test_food_business_specials_command_no_specials(self):
        """Test specials command when no specials are available."""
        message = self.create_whatsapp_message(self.food_session, 'specials')
        
        response = self.bot.process_message(message)
        
        self.assertIn('No specials available today', response)

    def test_food_business_todays_menu_command(self):
        """Test today's menu command for food business."""
        today = timezone.now().date()
        
        # Create products
        burger = Product.objects.create(
            business=self.food_business,
            name='Classic Burger',
            description='Beef burger with fries',
            price=Decimal('25.00'),
            sku='BURGER-001'
        )
        
        pizza = Product.objects.create(
            business=self.food_business,
            name='Margherita Pizza',
            description='Fresh mozzarella and basil',
            price=Decimal('35.00'),
            sku='PIZZA-001'
        )
        
        # Create daily menu
        daily_menu = DailyMenu.objects.create(
            business=self.food_business,
            date=today,
            is_active=True,
            preparation_time_minutes=25,
            notes='Fresh ingredients today!'
        )
        
        # Create menu items
        DailyMenuItem.objects.create(
            daily_menu=daily_menu,
            product=burger,
            available_quantity=20,
            remaining_quantity=15,
            is_available=True
        )
        
        DailyMenuItem.objects.create(
            daily_menu=daily_menu,
            product=pizza,
            available_quantity=10,
            remaining_quantity=8,
            is_available=True
        )
        
        message = self.create_whatsapp_message(self.food_session, 'today')
        
        response = self.bot.process_message(message)
        
        self.assertIn("Today's Menu", response)
        self.assertIn('Fresh ingredients today!', response)
        self.assertIn('Classic Burger', response)
        self.assertIn('R25', response)
        self.assertIn('15 left', response)
        self.assertIn('Margherita Pizza', response)
        self.assertIn('R35', response)
        self.assertIn('8 left', response)
        self.assertIn('25 minutes', response)

    def test_food_business_todays_menu_no_menu(self):
        """Test today's menu command when no menu is available."""
        message = self.create_whatsapp_message(self.food_session, 'today')
        
        response = self.bot.process_message(message)
        
        self.assertIn('No menu available for today', response)

    def test_service_business_book_command(self):
        """Test book command for service business."""
        message = self.create_whatsapp_message(self.service_session, 'book')
        
        response = self.bot.process_message(message)
        
        self.assertIn('Book an Appointment', response)
        self.assertIn('Service name', response)
        self.assertIn('Preferred date', response)
        self.assertIn('Preferred time', response)
        self.assertIn('book haircut tomorrow 2pm', response)

    def test_service_business_appointments_command(self):
        """Test appointments command for service business."""
        # Create a service
        service = Service.objects.create(
            business=self.service_business,
            name='Haircut',
            price=Decimal('50.00'),
            duration_minutes=60
        )
        
        # Create future appointment
        future_appointment = Appointment.objects.create(
            customer=self.customer,
            business=self.service_business,
            service=service,
            appointment_datetime=timezone.now() + timezone.timedelta(days=2),
            status='confirmed'
        )
        
        message = self.create_whatsapp_message(self.service_session, 'appointments')
        
        response = self.bot.process_message(message)
        
        self.assertIn('Your Upcoming Appointments', response)
        self.assertIn('Haircut', response)
        self.assertIn('Confirmed', response)
        self.assertIn('R50', response)

    def test_service_business_appointments_command_no_appointments(self):
        """Test appointments command when no appointments exist."""
        message = self.create_whatsapp_message(self.service_session, 'appointments')
        
        response = self.bot.process_message(message)
        
        self.assertIn("don't have any upcoming appointments", response)

    def test_greeting_message_customization(self):
        """Test that greeting messages are customized by business type."""
        # Product business greeting
        product_message = self.create_whatsapp_message(self.product_session, 'hi')
        product_response = self.bot.process_message(product_message)
        
        self.assertIn('Welcome to Product Store!', product_response)
        self.assertIn('menu', product_response)
        self.assertIn('order', product_response)
        
        # Service business greeting
        service_message = self.create_whatsapp_message(self.service_session, 'hello')
        service_response = self.bot.process_message(service_message)
        
        self.assertIn('Welcome to Beauty Salon!', service_response)
        self.assertIn('services', service_response)
        self.assertIn('book', service_response)
        
        # Food business greeting
        food_message = self.create_whatsapp_message(self.food_session, 'hey')
        food_response = self.bot.process_message(food_message)
        
        self.assertIn('Welcome to Tasty Restaurant!', food_response)
        self.assertIn('menu', food_response)
        self.assertIn('order', food_response)

    def test_invalid_command_for_business_type(self):
        """Test that invalid commands for business types are handled gracefully."""
        # Try services command on product business
        product_message = self.create_whatsapp_message(self.product_session, 'services')
        product_response = self.bot.process_message(product_message)
        
        self.assertIn('only available for service businesses', product_response)
        
        # Try specials command on product business
        product_specials_message = self.create_whatsapp_message(self.product_session, 'specials')
        product_specials_response = self.bot.process_message(product_specials_message)
        
        self.assertIn('only available for food businesses', product_specials_response)
