"""
Development settings for mthunzi project.
"""

from .base import *

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0']

# Database for development
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# Development-specific apps
INSTALLED_APPS += [
    'django_extensions',
]

# Development middleware
MIDDLEWARE += [
    'django.middleware.common.BrokenLinkEmailsMiddleware',
]

# Email backend for development
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# CORS settings for development
CORS_ALLOW_ALL_ORIGINS = True

# Cache configuration for development
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
    }
}

# Disable HTTPS redirects in development
SECURE_SSL_REDIRECT = False

# Development logging
LOGGING['loggers']['django']['level'] = 'DEBUG'
LOGGING['loggers']['mthunzi']['level'] = 'DEBUG'

# Development-specific settings
INTERNAL_IPS = [
    '127.0.0.1',
    'localhost',
]

# WhatsApp webhook URL for development (using ngrok or similar)
WHATSAPP_WEBHOOK_URL = config('WHATSAPP_WEBHOOK_URL', default='https://your-ngrok-url.ngrok.io/api/whatsapp/webhook/')

# Celery settings for development
CELERY_TASK_ALWAYS_EAGER = True  # Execute tasks synchronously in development
CELERY_TASK_EAGER_PROPAGATES = True
