from rest_framework import serializers
from django.utils import timezone
from datetime import datetime, timedelta
from .models import Service, ServiceAvailability, Appointment
from accounts.serializers import UserSerializer


class ServiceSerializer(serializers.ModelSerializer):
    """
    Serializer for Service model.
    """
    business_name = serializers.CharField(source='business.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Service
        fields = (
            'id', 'business', 'business_name', 'name', 'description', 'price',
            'duration_minutes', 'category', 'image', 'status', 'status_display',
            'requires_deposit', 'deposit_amount', 'buffer_time_minutes',
            'total_duration_minutes', 'is_available',
            'created_at', 'updated_at'
        )
        read_only_fields = (
            'id', 'business_name', 'total_duration_minutes', 'is_available',
            'created_at', 'updated_at'
        )

    def create(self, validated_data):
        # Set the business from the request context
        request = self.context.get('request')
        if request and hasattr(request.user, 'business'):
            validated_data['business'] = request.user.business
        return super().create(validated_data)

    def validate(self, attrs):
        """Validate business type compatibility."""
        request = self.context.get('request')
        if request and hasattr(request.user, 'business'):
            business = request.user.business
            if not business.is_service_business:
                raise serializers.ValidationError(
                    "Only service businesses can create services."
                )
        return attrs


class ServiceAvailabilitySerializer(serializers.ModelSerializer):
    """
    Serializer for ServiceAvailability model.
    """
    day_of_week_display = serializers.CharField(source='get_day_of_week_display', read_only=True)
    
    class Meta:
        model = ServiceAvailability
        fields = (
            'id', 'service', 'day_of_week', 'day_of_week_display',
            'start_time', 'end_time', 'is_available'
        )
        read_only_fields = ('id',)


class AppointmentSerializer(serializers.ModelSerializer):
    """
    Serializer for Appointment model.
    """
    customer = UserSerializer(read_only=True)
    service_name = serializers.CharField(source='service.name', read_only=True)
    business_name = serializers.CharField(source='business.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    end_datetime = serializers.DateTimeField(read_only=True)
    is_past_due = serializers.BooleanField(read_only=True)
    can_be_cancelled = serializers.BooleanField(read_only=True)
    can_be_rescheduled = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = Appointment
        fields = (
            'id', 'appointment_number', 'customer', 'business', 'business_name',
            'service', 'service_name', 'appointment_datetime', 'end_datetime',
            'duration_minutes', 'status', 'status_display',
            'customer_name', 'customer_phone', 'customer_email',
            'customer_notes', 'business_notes',
            'service_price', 'deposit_paid', 'total_amount',
            'is_past_due', 'can_be_cancelled', 'can_be_rescheduled',
            'created_at', 'updated_at', 'confirmed_at', 'completed_at', 'cancelled_at'
        )
        read_only_fields = (
            'id', 'appointment_number', 'customer', 'business_name', 'service_name',
            'end_datetime', 'is_past_due', 'can_be_cancelled', 'can_be_rescheduled',
            'created_at', 'updated_at', 'confirmed_at', 'completed_at', 'cancelled_at'
        )

    def create(self, validated_data):
        # Set customer from request
        request = self.context.get('request')
        if request:
            validated_data['customer'] = request.user
        
        # Set business from service
        service = validated_data.get('service')
        if service:
            validated_data['business'] = service.business
            
        return super().create(validated_data)

    def validate_appointment_datetime(self, value):
        """
        Validate that appointment is not in the past and within business hours.
        """
        if value <= timezone.now():
            raise serializers.ValidationError("Appointment cannot be scheduled in the past.")
        
        # Check if it's too far in advance
        service = self.initial_data.get('service')
        if service:
            business = service.business
            max_advance_days = business.booking_advance_days
            max_date = timezone.now() + timedelta(days=max_advance_days)
            if value > max_date:
                raise serializers.ValidationError(
                    f"Appointment cannot be scheduled more than {max_advance_days} days in advance."
                )
        
        return value


class AppointmentUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating appointment status and details.
    """
    
    class Meta:
        model = Appointment
        fields = (
            'status', 'business_notes', 'confirmed_at', 'completed_at', 'cancelled_at'
        )

    def update(self, instance, validated_data):
        # Set timestamps based on status changes
        status = validated_data.get('status')
        if status:
            if status == 'confirmed' and not instance.confirmed_at:
                validated_data['confirmed_at'] = timezone.now()
            elif status == 'completed' and not instance.completed_at:
                validated_data['completed_at'] = timezone.now()
            elif status == 'cancelled' and not instance.cancelled_at:
                validated_data['cancelled_at'] = timezone.now()
        
        return super().update(instance, validated_data)


class ServiceWithAvailabilitySerializer(ServiceSerializer):
    """
    Service serializer that includes availability schedule.
    """
    availability_schedule = ServiceAvailabilitySerializer(many=True, read_only=True)
    
    class Meta(ServiceSerializer.Meta):
        fields = ServiceSerializer.Meta.fields + ('availability_schedule',)
