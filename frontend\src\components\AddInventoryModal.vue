<template>
  <div v-if="isOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
    <div class="bg-[#FFFFFF] rounded-2xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-[#CCCCCC]/30">
        <h2 class="text-xl font-bold text-[#111111]">Add New Product</h2>
        <button @click="closeModal" class="p-2 hover:bg-[#F5F5F5] rounded-xl transition-colors duration-300">
          <svg class="w-5 h-5 text-[#4B4B4B]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>

      <!-- Form -->
      <form @submit.prevent="handleSubmit" class="p-6 space-y-6">
        <!-- Product Name -->
        <div class="space-y-2">
          <label for="productName" class="block text-sm font-medium text-[#111111]">
            Product Name *
          </label>
          <input
            id="productName"
            v-model="formData.name"
            type="text"
            placeholder="Enter product name"
            class="w-full px-4 py-3 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] placeholder-[#4B4B4B] focus:bg-[#FFFFFF] focus:ring-2 focus:ring-[#1E4E79]/20 focus:border-[#1E4E79] transition-all duration-300 outline-none"
            required
          />
        </div>

        <!-- Description -->
        <div class="space-y-2">
          <label for="productDescription" class="block text-sm font-medium text-[#111111]">
            Description
          </label>
          <textarea
            id="productDescription"
            v-model="formData.description"
            rows="3"
            placeholder="Enter product description"
            class="w-full px-4 py-3 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] placeholder-[#4B4B4B] focus:bg-[#FFFFFF] focus:ring-2 focus:ring-[#1E4E79]/20 focus:border-[#1E4E79] transition-all duration-300 outline-none resize-none"
          ></textarea>
        </div>

        <!-- Price and Stock -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <label for="productPrice" class="block text-sm font-medium text-[#111111]">
              Price (R) *
            </label>
            <input
              id="productPrice"
              v-model.number="formData.price"
              type="number"
              step="0.01"
              min="0"
              placeholder="0.00"
              class="w-full px-4 py-3 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] placeholder-[#4B4B4B] focus:bg-[#FFFFFF] focus:ring-2 focus:ring-[#1E4E79]/20 focus:border-[#1E4E79] transition-all duration-300 outline-none"
              required
            />
          </div>
          <div class="space-y-2">
            <label for="productStock" class="block text-sm font-medium text-[#111111]">
              Stock Quantity *
            </label>
            <input
              id="productStock"
              v-model.number="formData.stock_quantity"
              type="number"
              min="0"
              placeholder="0"
              class="w-full px-4 py-3 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] placeholder-[#4B4B4B] focus:bg-[#FFFFFF] focus:ring-2 focus:ring-[#1E4E79]/20 focus:border-[#1E4E79] transition-all duration-300 outline-none"
              required
            />
          </div>
        </div>

        <!-- Low Stock Threshold -->
        <div class="space-y-2">
          <label for="lowStockThreshold" class="block text-sm font-medium text-[#111111]">
            Low Stock Alert Threshold
          </label>
          <input
            id="lowStockThreshold"
            v-model.number="formData.low_stock_threshold"
            type="number"
            min="0"
            placeholder="10"
            class="w-full px-4 py-3 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] placeholder-[#4B4B4B] focus:bg-[#FFFFFF] focus:ring-2 focus:ring-[#1E4E79]/20 focus:border-[#1E4E79] transition-all duration-300 outline-none"
          />
          <p class="text-xs text-[#4B4B4B]">You'll be notified when stock falls below this number</p>
        </div>

        <!-- Image Upload -->
        <div class="space-y-2">
          <label class="block text-sm font-medium text-[#111111]">
            Product Images
          </label>
          <div class="border-2 border-dashed border-[#CCCCCC] rounded-xl p-6 text-center hover:border-[#1E4E79] transition-colors duration-300">
            <input
              ref="fileInput"
              type="file"
              multiple
              accept="image/*"
              @change="handleFileSelect"
              class="hidden"
            />
            <div v-if="selectedImages.length === 0" @click="$refs.fileInput.click()" class="cursor-pointer">
              <svg class="w-12 h-12 text-[#4B4B4B] mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
              </svg>
              <p class="text-[#4B4B4B] mb-2">Click to upload images</p>
              <p class="text-xs text-[#4B4B4B]">PNG, JPG, GIF up to 10MB each</p>
            </div>
            
            <!-- Image Previews -->
            <div v-if="selectedImages.length > 0" class="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div v-for="(image, index) in selectedImages" :key="index" class="relative group">
                <img :src="image.preview" :alt="`Preview ${index + 1}`" class="w-full h-24 object-cover rounded-lg"/>
                <button
                  @click="removeImage(index)"
                  type="button"
                  class="absolute -top-2 -right-2 w-6 h-6 bg-[#C62828] text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                  </svg>
                </button>
              </div>
              <button
                @click="$refs.fileInput.click()"
                type="button"
                class="w-full h-24 border-2 border-dashed border-[#CCCCCC] rounded-lg flex items-center justify-center hover:border-[#1E4E79] transition-colors duration-300"
              >
                <svg class="w-8 h-8 text-[#4B4B4B]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Error Message -->
        <div v-if="errorMessage" class="p-4 bg-[#C62828]/10 border border-[#C62828]/30 rounded-xl">
          <p class="text-sm text-[#C62828]">{{ errorMessage }}</p>
        </div>

        <!-- Action Buttons -->
        <div class="flex space-x-4 pt-4">
          <button
            type="button"
            @click="closeModal"
            class="flex-1 px-6 py-3 bg-[#F5F5F5] text-[#4B4B4B] rounded-xl hover:bg-[#CCCCCC]/30 transition-colors duration-300"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="isSubmitting"
            class="flex-1 px-6 py-3 bg-gradient-to-r from-[#1E4E79] to-[#132F4C] text-white rounded-xl hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center"
          >
            <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isSubmitting ? 'Adding Product...' : 'Add Product' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { inventoryService } from '../services/podApi.js'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'product-added'])

// Form data
const formData = reactive({
  name: '',
  description: '',
  price: 0,
  stock_quantity: 0,
  low_stock_threshold: 10
})

const selectedImages = ref([])
const isSubmitting = ref(false)
const errorMessage = ref('')
const fileInput = ref(null)

// Handle file selection
const handleFileSelect = (event) => {
  const files = Array.from(event.target.files)
  
  files.forEach(file => {
    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      errorMessage.value = 'File size must be less than 10MB'
      return
    }
    
    const reader = new FileReader()
    reader.onload = (e) => {
      selectedImages.value.push({
        file: file,
        preview: e.target.result
      })
    }
    reader.readAsDataURL(file)
  })
  
  // Clear the input
  event.target.value = ''
}

// Remove image
const removeImage = (index) => {
  selectedImages.value.splice(index, 1)
}

// Handle form submission
const handleSubmit = async () => {
  if (isSubmitting.value) return
  
  errorMessage.value = ''
  isSubmitting.value = true
  
  try {
    // Add the product first
    const productData = {
      name: formData.name,
      description: formData.description,
      price: formData.price,
      stock_quantity: formData.stock_quantity,
      low_stock_threshold: formData.low_stock_threshold
    }
    
    const newProduct = await inventoryService.addProduct(productData)
    
    // Upload images if any
    if (selectedImages.value.length > 0) {
      for (const image of selectedImages.value) {
        try {
          await inventoryService.uploadProductImage(newProduct.id, image.file)
        } catch (imageError) {
          console.error('Error uploading image:', imageError)
          // Continue with other images even if one fails
        }
      }
    }
    
    emit('product-added', newProduct)
    closeModal()
  } catch (error) {
    errorMessage.value = error.message || 'Failed to add product'
  } finally {
    isSubmitting.value = false
  }
}

// Close modal
const closeModal = () => {
  emit('close')
  resetForm()
}

// Reset form
const resetForm = () => {
  formData.name = ''
  formData.description = ''
  formData.price = 0
  formData.stock_quantity = 0
  formData.low_stock_threshold = 10
  selectedImages.value = []
  errorMessage.value = ''
}

// Reset form when modal closes
watch(() => props.isOpen, (newValue) => {
  if (!newValue) {
    resetForm()
  }
})
</script>
