"""
Tests for business type functionality.
"""

from django.test import TestCase
from django.core.exceptions import ValidationError
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from accounts.models import Business
from accounts.validators import BusinessTypeValidator, validate_business_operation

User = get_user_model()


class BusinessTypeModelTests(TestCase):
    """Test business type model functionality."""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testowner',
            email='<EMAIL>',
            password='testpass123',
            role='pod'
        )

    def test_business_type_choices(self):
        """Test that all business type choices are available."""
        choices = Business.BusinessType.choices
        expected_choices = [
            ('product_goods', 'Product & Goods'),
            ('service', 'Service Business'),
            ('food_restaurant', 'Food & Restaurant'),
        ]
        self.assertEqual(choices, expected_choices)

    def test_default_business_type(self):
        """Test that default business type is product_goods."""
        business = Business.objects.create(
            owner=self.user,
            name='Test Business'
        )
        self.assertEqual(business.business_type, Business.BusinessType.PRODUCT_GOODS)

    def test_business_type_properties(self):
        """Test business type property methods."""
        # Product business
        product_business = Business.objects.create(
            owner=self.user,
            name='Product Business',
            business_type=Business.BusinessType.PRODUCT_GOODS
        )
        self.assertTrue(product_business.is_product_business)
        self.assertFalse(product_business.is_service_business)
        self.assertFalse(product_business.is_food_business)
        self.assertTrue(product_business.requires_inventory_management)
        self.assertFalse(product_business.supports_appointments)
        self.assertFalse(product_business.supports_daily_menu)

        # Service business
        service_business = Business.objects.create(
            owner=User.objects.create_user(
                username='serviceowner',
                email='<EMAIL>',
                password='testpass123',
                role='pod'
            ),
            name='Service Business',
            business_type=Business.BusinessType.SERVICE
        )
        self.assertFalse(service_business.is_product_business)
        self.assertTrue(service_business.is_service_business)
        self.assertFalse(service_business.is_food_business)
        self.assertFalse(service_business.requires_inventory_management)
        self.assertTrue(service_business.supports_appointments)
        self.assertFalse(service_business.supports_daily_menu)

        # Food business
        food_business = Business.objects.create(
            owner=User.objects.create_user(
                username='foodowner',
                email='<EMAIL>',
                password='testpass123',
                role='pod'
            ),
            name='Food Business',
            business_type=Business.BusinessType.FOOD_RESTAURANT
        )
        self.assertFalse(food_business.is_product_business)
        self.assertFalse(food_business.is_service_business)
        self.assertTrue(food_business.is_food_business)
        self.assertFalse(food_business.requires_inventory_management)
        self.assertFalse(food_business.supports_appointments)
        self.assertTrue(food_business.supports_daily_menu)

    def test_business_str_representation(self):
        """Test business string representation includes business type."""
        business = Business.objects.create(
            owner=self.user,
            name='Test Business',
            business_type=Business.BusinessType.SERVICE
        )
        expected_str = "Test Business (Service Business)"
        self.assertEqual(str(business), expected_str)


class BusinessTypeValidatorTests(TestCase):
    """Test business type validation utilities."""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testowner',
            email='<EMAIL>',
            password='testpass123',
            role='pod'
        )
        
        self.product_business = Business.objects.create(
            owner=self.user,
            name='Product Business',
            business_type=Business.BusinessType.PRODUCT_GOODS
        )
        
        self.service_business = Business.objects.create(
            owner=User.objects.create_user(
                username='serviceowner',
                email='<EMAIL>',
                password='testpass123',
                role='pod'
            ),
            name='Service Business',
            business_type=Business.BusinessType.SERVICE
        )
        
        self.food_business = Business.objects.create(
            owner=User.objects.create_user(
                username='foodowner',
                email='<EMAIL>',
                password='testpass123',
                role='pod'
            ),
            name='Food Business',
            business_type=Business.BusinessType.FOOD_RESTAURANT
        )

    def test_validate_product_business(self):
        """Test product business validation."""
        # Should pass for product business
        BusinessTypeValidator.validate_product_business(self.product_business)
        
        # Should pass for food business (can have products)
        BusinessTypeValidator.validate_product_business(self.food_business)
        
        # Should fail for service business
        with self.assertRaises(ValidationError):
            BusinessTypeValidator.validate_product_business(self.service_business)

    def test_validate_service_business(self):
        """Test service business validation."""
        # Should pass for service business
        BusinessTypeValidator.validate_service_business(self.service_business)
        
        # Should fail for product business
        with self.assertRaises(ValidationError):
            BusinessTypeValidator.validate_service_business(self.product_business)
        
        # Should fail for food business
        with self.assertRaises(ValidationError):
            BusinessTypeValidator.validate_service_business(self.food_business)

    def test_validate_food_business(self):
        """Test food business validation."""
        # Should pass for food business
        BusinessTypeValidator.validate_food_business(self.food_business)
        
        # Should fail for product business
        with self.assertRaises(ValidationError):
            BusinessTypeValidator.validate_food_business(self.product_business)
        
        # Should fail for service business
        with self.assertRaises(ValidationError):
            BusinessTypeValidator.validate_food_business(self.service_business)

    def test_validate_business_operation(self):
        """Test the validate_business_operation function."""
        # Test product operations
        validate_business_operation(self.product_business, 'product', 'manage products')
        
        with self.assertRaises(ValidationError):
            validate_business_operation(self.service_business, 'product', 'manage products')
        
        # Test service operations
        validate_business_operation(self.service_business, 'service', 'manage services')
        
        with self.assertRaises(ValidationError):
            validate_business_operation(self.product_business, 'service', 'manage services')
        
        # Test food operations
        validate_business_operation(self.food_business, 'food', 'manage daily menus')
        
        with self.assertRaises(ValidationError):
            validate_business_operation(self.product_business, 'food', 'manage daily menus')


class BusinessTypeAPITests(APITestCase):
    """Test business type API functionality."""

    def setUp(self):
        self.product_owner = User.objects.create_user(
            username='productowner',
            email='<EMAIL>',
            password='testpass123',
            role='pod'
        )
        
        self.service_owner = User.objects.create_user(
            username='serviceowner',
            email='<EMAIL>',
            password='testpass123',
            role='pod'
        )
        
        self.food_owner = User.objects.create_user(
            username='foodowner',
            email='<EMAIL>',
            password='testpass123',
            role='pod'
        )
        
        self.customer = User.objects.create_user(
            username='customer',
            email='<EMAIL>',
            password='testpass123',
            role='pulse'
        )
        
        self.product_business = Business.objects.create(
            owner=self.product_owner,
            name='Product Business',
            business_type=Business.BusinessType.PRODUCT_GOODS
        )
        
        self.service_business = Business.objects.create(
            owner=self.service_owner,
            name='Service Business',
            business_type=Business.BusinessType.SERVICE
        )
        
        self.food_business = Business.objects.create(
            owner=self.food_owner,
            name='Food Business',
            business_type=Business.BusinessType.FOOD_RESTAURANT
        )

    def test_business_capabilities_endpoint(self):
        """Test the business capabilities endpoint."""
        # Test product business capabilities
        self.client.force_authenticate(user=self.product_owner)
        response = self.client.get('/api/auth/capabilities/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertEqual(data['business_type'], 'product_goods')
        self.assertTrue(data['capabilities']['supports_products'])
        self.assertTrue(data['capabilities']['supports_inventory'])
        self.assertFalse(data['capabilities']['supports_services'])
        self.assertFalse(data['capabilities']['supports_appointments'])
        self.assertFalse(data['capabilities']['supports_daily_menu'])

    def test_specific_business_capabilities(self):
        """Test getting capabilities for a specific business."""
        self.client.force_authenticate(user=self.customer)
        response = self.client.get(f'/api/auth/businesses/{self.service_business.id}/capabilities/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertEqual(data['business_type'], 'service')
        self.assertFalse(data['capabilities']['supports_products'])
        self.assertTrue(data['capabilities']['supports_services'])
        self.assertTrue(data['capabilities']['supports_appointments'])

    def test_business_dashboard_endpoint(self):
        """Test the business dashboard endpoint returns type-specific data."""
        # Test service business dashboard
        self.client.force_authenticate(user=self.service_owner)
        response = self.client.get('/api/auth/dashboard/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertEqual(data['business_type'], 'service')
        self.assertEqual(data['dashboard_type'], 'service_business')
        self.assertIn('services', data['metrics'])
        self.assertIn('appointments', data['metrics'])

    def test_unauthorized_access_to_capabilities(self):
        """Test that unauthenticated users cannot access capabilities."""
        response = self.client.get('/api/auth/capabilities/')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_customer_without_business_capabilities(self):
        """Test that customers without businesses get appropriate error."""
        self.client.force_authenticate(user=self.customer)
        response = self.client.get('/api/auth/capabilities/')
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
