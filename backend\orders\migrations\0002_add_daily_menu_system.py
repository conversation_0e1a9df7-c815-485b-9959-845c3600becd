# Generated by Django 4.2.7 on 2025-07-10 14:58

from decimal import Decimal
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0003_add_business_types'),
        ('orders', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='DailyMenu',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(help_text='Menu date')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this menu is active for the day')),
                ('preparation_time_minutes', models.PositiveIntegerField(blank=True, help_text='Override business preparation time for this menu', null=True)),
                ('notes', models.TextField(blank=True, help_text='Special notes or announcements for the day')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business', models.ForeignKey(limit_choices_to={'business_type': 'food_restaurant'}, on_delete=django.db.models.deletion.CASCADE, related_name='daily_menus', to='accounts.business')),
            ],
            options={
                'ordering': ['-date'],
                'unique_together': {('business', 'date')},
            },
        ),
        migrations.CreateModel(
            name='DailyMenuItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('available_quantity', models.PositiveIntegerField(help_text='Quantity available for this day (0 = unlimited)')),
                ('remaining_quantity', models.PositiveIntegerField(help_text='Remaining quantity for the day')),
                ('is_available', models.BooleanField(default=True, help_text='Whether this item is available today')),
                ('special_price', models.DecimalField(blank=True, decimal_places=2, help_text='Special price for today (overrides product price)', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('notes', models.TextField(blank=True, help_text='Special notes about this item for today')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('daily_menu', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='menu_items', to='orders.dailymenu')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='daily_menu_items', to='orders.product')),
            ],
            options={
                'ordering': ['product__name'],
                'unique_together': {('daily_menu', 'product')},
            },
        ),
    ]
