"""
Tests for food business functionality (daily menus).
"""

from django.test import TestCase
from django.core.exceptions import ValidationError
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from django.utils import timezone
from decimal import Decimal

from accounts.models import Business
from orders.models import Product, DailyMenu, DailyMenuItem

User = get_user_model()


class DailyMenuModelTests(TestCase):
    """Test DailyMenu model functionality."""

    def setUp(self):
        self.food_owner = User.objects.create_user(
            username='foodowner',
            email='<EMAIL>',
            password='testpass123',
            role='pod'
        )
        
        self.product_owner = User.objects.create_user(
            username='productowner',
            email='<EMAIL>',
            password='testpass123',
            role='pod'
        )
        
        self.food_business = Business.objects.create(
            owner=self.food_owner,
            name='Food Business',
            business_type=Business.BusinessType.FOOD_RESTAURANT
        )
        
        self.product_business = Business.objects.create(
            owner=self.product_owner,
            name='Product Business',
            business_type=Business.BusinessType.PRODUCT_GOODS
        )

    def test_create_daily_menu_for_food_business(self):
        """Test creating a daily menu for a food business."""
        today = timezone.now().date()
        
        daily_menu = DailyMenu.objects.create(
            business=self.food_business,
            date=today,
            is_active=True,
            preparation_time_minutes=25,
            notes='Fresh ingredients today!'
        )
        
        self.assertEqual(daily_menu.business, self.food_business)
        self.assertEqual(daily_menu.date, today)
        self.assertTrue(daily_menu.is_active)
        self.assertEqual(daily_menu.effective_preparation_time, 25)

    def test_daily_menu_effective_preparation_time(self):
        """Test effective preparation time falls back to business default."""
        today = timezone.now().date()
        
        # Menu without specific preparation time
        daily_menu = DailyMenu.objects.create(
            business=self.food_business,
            date=today,
            is_active=True
        )
        
        # Should use business default (30 minutes)
        self.assertEqual(daily_menu.effective_preparation_time, 30)

    def test_daily_menu_validation_for_non_food_business(self):
        """Test that daily menus cannot be created for non-food businesses."""
        today = timezone.now().date()
        
        with self.assertRaises(ValidationError):
            daily_menu = DailyMenu(
                business=self.product_business,
                date=today,
                is_active=True
            )
            daily_menu.clean()

    def test_daily_menu_str_representation(self):
        """Test daily menu string representation."""
        today = timezone.now().date()
        
        daily_menu = DailyMenu.objects.create(
            business=self.food_business,
            date=today,
            is_active=True
        )
        
        expected_str = f"Food Business - {today}"
        self.assertEqual(str(daily_menu), expected_str)


class DailyMenuItemModelTests(TestCase):
    """Test DailyMenuItem model functionality."""

    def setUp(self):
        self.food_owner = User.objects.create_user(
            username='foodowner',
            email='<EMAIL>',
            password='testpass123',
            role='pod'
        )
        
        self.food_business = Business.objects.create(
            owner=self.food_owner,
            name='Food Business',
            business_type=Business.BusinessType.FOOD_RESTAURANT
        )
        
        self.product = Product.objects.create(
            business=self.food_business,
            name='Burger',
            description='Delicious burger',
            price=Decimal('15.00'),
            sku='BURGER-001'
        )
        
        self.daily_menu = DailyMenu.objects.create(
            business=self.food_business,
            date=timezone.now().date(),
            is_active=True
        )

    def test_create_daily_menu_item(self):
        """Test creating a daily menu item."""
        menu_item = DailyMenuItem.objects.create(
            daily_menu=self.daily_menu,
            product=self.product,
            available_quantity=50,
            is_available=True,
            special_price=Decimal('12.00'),
            notes='Special weekend price'
        )
        
        self.assertEqual(menu_item.product, self.product)
        self.assertEqual(menu_item.available_quantity, 50)
        self.assertEqual(menu_item.remaining_quantity, 50)  # Should be set automatically
        self.assertEqual(menu_item.effective_price, Decimal('12.00'))  # Special price
        self.assertTrue(menu_item.is_orderable)

    def test_daily_menu_item_effective_price(self):
        """Test effective price calculation."""
        # Item with special price
        menu_item_special = DailyMenuItem.objects.create(
            daily_menu=self.daily_menu,
            product=self.product,
            available_quantity=30,
            special_price=Decimal('12.00')
        )
        self.assertEqual(menu_item_special.effective_price, Decimal('12.00'))
        
        # Item without special price (uses product price)
        menu_item_regular = DailyMenuItem.objects.create(
            daily_menu=self.daily_menu,
            product=Product.objects.create(
                business=self.food_business,
                name='Pizza',
                price=Decimal('25.00'),
                sku='PIZZA-001'
            ),
            available_quantity=20
        )
        self.assertEqual(menu_item_regular.effective_price, Decimal('25.00'))

    def test_daily_menu_item_sold_out_logic(self):
        """Test sold out logic."""
        # Limited quantity item
        menu_item = DailyMenuItem.objects.create(
            daily_menu=self.daily_menu,
            product=self.product,
            available_quantity=5,
            remaining_quantity=0  # Sold out
        )
        self.assertTrue(menu_item.is_sold_out)
        self.assertFalse(menu_item.is_orderable)
        
        # Unlimited quantity item (0 means unlimited)
        unlimited_item = DailyMenuItem.objects.create(
            daily_menu=self.daily_menu,
            product=Product.objects.create(
                business=self.food_business,
                name='Unlimited Item',
                price=Decimal('10.00'),
                sku='UNLIMITED-001'
            ),
            available_quantity=0  # Unlimited
        )
        self.assertFalse(unlimited_item.is_sold_out)
        self.assertTrue(unlimited_item.is_orderable)

    def test_reduce_quantity(self):
        """Test reducing quantity when ordered."""
        menu_item = DailyMenuItem.objects.create(
            daily_menu=self.daily_menu,
            product=self.product,
            available_quantity=10,
            remaining_quantity=10
        )
        
        # Reduce quantity
        menu_item.reduce_quantity(3)
        menu_item.refresh_from_db()
        self.assertEqual(menu_item.remaining_quantity, 7)
        
        # Reduce more than available (should not go below 0)
        menu_item.reduce_quantity(10)
        menu_item.refresh_from_db()
        self.assertEqual(menu_item.remaining_quantity, 0)

    def test_daily_menu_item_str_representation(self):
        """Test daily menu item string representation."""
        today = timezone.now().date()
        
        menu_item = DailyMenuItem.objects.create(
            daily_menu=self.daily_menu,
            product=self.product,
            available_quantity=30
        )
        
        expected_str = f"{today} - Burger"
        self.assertEqual(str(menu_item), expected_str)


class DailyMenuAPITests(APITestCase):
    """Test Daily Menu API functionality."""

    def setUp(self):
        self.food_owner = User.objects.create_user(
            username='foodowner',
            email='<EMAIL>',
            password='testpass123',
            role='pod'
        )
        
        self.product_owner = User.objects.create_user(
            username='productowner',
            email='<EMAIL>',
            password='testpass123',
            role='pod'
        )
        
        self.customer = User.objects.create_user(
            username='customer',
            email='<EMAIL>',
            password='testpass123',
            role='pulse'
        )
        
        self.food_business = Business.objects.create(
            owner=self.food_owner,
            name='Food Business',
            business_type=Business.BusinessType.FOOD_RESTAURANT
        )
        
        self.product_business = Business.objects.create(
            owner=self.product_owner,
            name='Product Business',
            business_type=Business.BusinessType.PRODUCT_GOODS
        )
        
        self.product = Product.objects.create(
            business=self.food_business,
            name='Burger',
            description='Delicious burger',
            price=Decimal('15.00'),
            sku='BURGER-001'
        )

    def test_create_daily_menu_as_food_business(self):
        """Test creating a daily menu as a food business owner."""
        self.client.force_authenticate(user=self.food_owner)
        
        today = timezone.now().date()
        data = {
            'date': today.isoformat(),
            'is_active': True,
            'preparation_time_minutes': 25,
            'notes': 'Fresh ingredients today!',
            'menu_items': [
                {
                    'product': self.product.id,
                    'available_quantity': 30,
                    'is_available': True,
                    'special_price': '12.00',
                    'notes': 'Special price today'
                }
            ]
        }
        
        response = self.client.post('/api/orders/daily-menus/', data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        daily_menu = DailyMenu.objects.get(id=response.data['id'])
        self.assertEqual(daily_menu.business, self.food_business)
        self.assertEqual(daily_menu.menu_items.count(), 1)

    def test_create_daily_menu_as_product_business_fails(self):
        """Test that product businesses cannot create daily menus."""
        self.client.force_authenticate(user=self.product_owner)
        
        today = timezone.now().date()
        data = {
            'date': today.isoformat(),
            'is_active': True
        }
        
        response = self.client.post('/api/orders/daily-menus/', data, format='json')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_todays_menu_endpoint(self):
        """Test the today's menu endpoint."""
        today = timezone.now().date()
        
        # Create daily menu
        daily_menu = DailyMenu.objects.create(
            business=self.food_business,
            date=today,
            is_active=True,
            notes='Fresh ingredients today!'
        )
        
        # Create menu item
        DailyMenuItem.objects.create(
            daily_menu=daily_menu,
            product=self.product,
            available_quantity=30,
            is_available=True,
            special_price=Decimal('12.00')
        )
        
        self.client.force_authenticate(user=self.food_owner)
        response = self.client.get('/api/orders/todays-menu/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertEqual(data['business_name'], 'Food Business')
        self.assertEqual(len(data['menu_items']), 1)
        self.assertEqual(data['menu_items'][0]['product_name'], 'Burger')

    def test_todays_menu_for_specific_business(self):
        """Test getting today's menu for a specific business."""
        today = timezone.now().date()
        
        # Create daily menu
        daily_menu = DailyMenu.objects.create(
            business=self.food_business,
            date=today,
            is_active=True
        )
        
        DailyMenuItem.objects.create(
            daily_menu=daily_menu,
            product=self.product,
            available_quantity=20,
            is_available=True
        )
        
        self.client.force_authenticate(user=self.customer)
        response = self.client.get(f'/api/orders/businesses/{self.food_business.id}/todays-menu/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertEqual(data['business_name'], 'Food Business')

    def test_todays_menu_no_menu_available(self):
        """Test today's menu when no menu is available."""
        self.client.force_authenticate(user=self.food_owner)
        response = self.client.get('/api/orders/todays-menu/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertEqual(data['message'], 'No menu available for today')
        self.assertEqual(data['menu_items'], [])
