"""
Health check views for monitoring system status.
"""

import logging
from django.http import JsonResponse
from django.db import connection
from django.core.cache import cache
from django.conf import settings
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt

logger = logging.getLogger(__name__)


@csrf_exempt
@require_http_methods(["GET"])
def health_check(request):
    """
    Comprehensive health check endpoint.
    Returns system status and component health.
    """
    health_status = {
        'status': 'healthy',
        'timestamp': timezone.now().isoformat(),
        'version': '1.0.0',
        'environment': getattr(settings, 'DJANGO_ENVIRONMENT', 'unknown'),
        'checks': {}
    }
    
    overall_healthy = True
    
    # Database health check
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            cursor.fetchone()
        health_status['checks']['database'] = {
            'status': 'healthy',
            'message': 'Database connection successful'
        }
    except Exception as e:
        health_status['checks']['database'] = {
            'status': 'unhealthy',
            'message': f'Database connection failed: {str(e)}'
        }
        overall_healthy = False
    
    # Cache health check
    try:
        cache_key = 'health_check_test'
        cache.set(cache_key, 'test_value', 30)
        cached_value = cache.get(cache_key)
        if cached_value == 'test_value':
            health_status['checks']['cache'] = {
                'status': 'healthy',
                'message': 'Cache is working'
            }
        else:
            raise Exception('Cache value mismatch')
    except Exception as e:
        health_status['checks']['cache'] = {
            'status': 'unhealthy',
            'message': f'Cache check failed: {str(e)}'
        }
        overall_healthy = False
    
    # WhatsApp service health check
    try:
        from whatsapp.services import whatsapp_service
        if whatsapp_service.is_configured():
            health_status['checks']['whatsapp'] = {
                'status': 'healthy',
                'message': 'WhatsApp service is configured'
            }
        else:
            health_status['checks']['whatsapp'] = {
                'status': 'warning',
                'message': 'WhatsApp service not configured'
            }
    except Exception as e:
        health_status['checks']['whatsapp'] = {
            'status': 'unhealthy',
            'message': f'WhatsApp service check failed: {str(e)}'
        }
        overall_healthy = False
    
    # File system health check
    try:
        import os
        import tempfile
        
        # Test write access to media directory
        media_root = getattr(settings, 'MEDIA_ROOT', '/tmp')
        test_file = os.path.join(media_root, 'health_check_test.txt')
        
        with open(test_file, 'w') as f:
            f.write('health check test')
        
        # Read back the file
        with open(test_file, 'r') as f:
            content = f.read()
        
        # Clean up
        os.remove(test_file)
        
        if content == 'health check test':
            health_status['checks']['filesystem'] = {
                'status': 'healthy',
                'message': 'File system is writable'
            }
        else:
            raise Exception('File content mismatch')
            
    except Exception as e:
        health_status['checks']['filesystem'] = {
            'status': 'unhealthy',
            'message': f'File system check failed: {str(e)}'
        }
        overall_healthy = False
    
    # Memory usage check
    try:
        import psutil
        memory = psutil.virtual_memory()
        memory_usage = memory.percent
        
        if memory_usage < 80:
            status = 'healthy'
            message = f'Memory usage: {memory_usage:.1f}%'
        elif memory_usage < 90:
            status = 'warning'
            message = f'High memory usage: {memory_usage:.1f}%'
        else:
            status = 'unhealthy'
            message = f'Critical memory usage: {memory_usage:.1f}%'
            overall_healthy = False
        
        health_status['checks']['memory'] = {
            'status': status,
            'message': message,
            'usage_percent': memory_usage
        }
    except ImportError:
        health_status['checks']['memory'] = {
            'status': 'unknown',
            'message': 'psutil not available for memory monitoring'
        }
    except Exception as e:
        health_status['checks']['memory'] = {
            'status': 'unhealthy',
            'message': f'Memory check failed: {str(e)}'
        }
        overall_healthy = False
    
    # Disk usage check
    try:
        import shutil
        disk_usage = shutil.disk_usage('/')
        used_percent = (disk_usage.used / disk_usage.total) * 100
        
        if used_percent < 80:
            status = 'healthy'
            message = f'Disk usage: {used_percent:.1f}%'
        elif used_percent < 90:
            status = 'warning'
            message = f'High disk usage: {used_percent:.1f}%'
        else:
            status = 'unhealthy'
            message = f'Critical disk usage: {used_percent:.1f}%'
            overall_healthy = False
        
        health_status['checks']['disk'] = {
            'status': status,
            'message': message,
            'usage_percent': used_percent
        }
    except Exception as e:
        health_status['checks']['disk'] = {
            'status': 'unhealthy',
            'message': f'Disk check failed: {str(e)}'
        }
        overall_healthy = False
    
    # Set overall status
    if not overall_healthy:
        health_status['status'] = 'unhealthy'
    elif any(check.get('status') == 'warning' for check in health_status['checks'].values()):
        health_status['status'] = 'warning'
    
    # Return appropriate HTTP status code
    if health_status['status'] == 'healthy':
        status_code = 200
    elif health_status['status'] == 'warning':
        status_code = 200  # Still operational
    else:
        status_code = 503  # Service unavailable
    
    return JsonResponse(health_status, status=status_code)


@csrf_exempt
@require_http_methods(["GET"])
def readiness_check(request):
    """
    Readiness check for Kubernetes/container orchestration.
    Returns 200 if the application is ready to serve traffic.
    """
    try:
        # Quick database check
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            cursor.fetchone()
        
        return JsonResponse({
            'status': 'ready',
            'timestamp': timezone.now().isoformat()
        })
    except Exception as e:
        return JsonResponse({
            'status': 'not ready',
            'error': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=503)


@csrf_exempt
@require_http_methods(["GET"])
def liveness_check(request):
    """
    Liveness check for Kubernetes/container orchestration.
    Returns 200 if the application is alive.
    """
    return JsonResponse({
        'status': 'alive',
        'timestamp': timezone.now().isoformat()
    })
